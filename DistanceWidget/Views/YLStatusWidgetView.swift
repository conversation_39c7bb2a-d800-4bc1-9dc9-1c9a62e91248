//
//  YLStatusWidgetView.swift
//  DistanceWidget
//
//

import WidgetKit
import SwiftUI
import SwiftDate

// MARK: - Entry
struct YLStatusEntry: TimelineEntry {
	let date: Date
	let mine: YLWidgetStatus?
	let peer: YLWidgetStatus?
	let mineAvatar: UIImage?
	let peerAvatar: UIImage?
}

// MARK: - Provider
struct YLStatusProvider: TimelineProvider {
	
	func placeholder(in context: Context) -> YLStatusEntry {
		YLStatusEntry(date: Date(),
					  mine: nil,
					  peer: nil,
					  mineAvatar: nil,
					  peerAvatar: nil)
	}
	
	func getSnapshot(in context: Context,
					 completion: @escaping (YLStatusEntry) -> Void) {
		YLWidgetStatusData.shared.getPairState { mine, peer, mineAvt, peerAvt in
			completion(
				YLStatusEntry(date: Date(),
							  mine: mine,
							  peer: peer,
							  mineAvatar: mineAvt,
							  peerAvatar: peerAvt)
			)
		}
	}
	
	func getTimeline(in context: Context,
					 completion: @escaping (Timeline<YLStatusEntry>) -> Void) {
		SwiftDate.defaultRegion = .current
		
		YLWidgetStatusData.shared.getPairState { mine, peer, mineAvt, peerAvt in
			let entry = YLStatusEntry(date: Date(),
									  mine: mine,
									  peer: peer,
									  mineAvatar: mineAvt,
									  peerAvatar: peerAvt)
			
			let minutes = 15
			let next = Calendar.current.date(byAdding: .minute,
											 value: minutes,
											 to: Date())!
			completion(Timeline(entries: [entry],
								policy: .after(next)))
		}
	}
}

// MARK: - View
struct YLStatusWidgetEntryView: View {
	var entry: YLStatusProvider.Entry
	
	var body: some View {
		ZStack {
			if !YLWidgetDeskPhotoData.shared.isLogin || !YLWidgetDeskPhotoData.shared.isMatch {
				YLStatusErrorView().widgetBackground(Color.clear)
			}else {
				Image("status_widget_bg")
					.resizable()
					.scaledToFill()
					.frame(maxWidth: .infinity, maxHeight: .infinity)
				VStack {
					// 左右头像覆盖
					avatarOverlay
						.padding(.horizontal, 12)
						.padding(.top, 20)
						.frame(height: 34) // 控制线的高度
					HStack(spacing: 40) {
						statusColumn(status: entry.mine, isMine: true)
						statusColumn(status: entry.peer, isMine: false)
					}
					.padding(.horizontal, 48)
					.padding(.top, 5)
					Spacer(minLength: 10)
				}
			}
			
		}
		.widgetURL(URL(string: "ylcloser://status")!)
		
	}
	
	// MARK: 状态列
	@ViewBuilder
	private func statusColumn(status: YLWidgetStatus?,
							  isMine: Bool) -> some View {
		VStack(spacing: 2) {
			// Emoji / 问号
			if let s = status {
				Text(s.emoji).font(.system(size: 46))
					.frame(width: 46, height: 46)
			} else {
				Image("stautsquestionmark")
					.resizable()
					.frame(width: 46, height: 46)
			}
			
			// 文字
			if let s = status {
				if let t = s.text {
					Text(t).font(.system(size: 12, weight: .medium))
						.foregroundColor(Color(red: 0.47, green: 0.3, blue: 0.26))
						.frame(height: 17)
				}
				relativeTimeText(for: s.timestamp)
					.padding(.top,6)
					.frame(height: 24)
			} else {
				Text(NSLocalizedString("status_no", comment: ""))
					.font(.system(size: 12, weight: .medium))
					.foregroundColor(Color(red: 0.18, green: 0.18, blue: 0.21).opacity(0.4))
					.padding(.top,2)
				if isMine {
					
					Image("status_add")
						.frame(height: 24)
						.padding(.top, 10)
				}else {
					
					Image("status_alarm")
						.frame(height: 24)
						.padding(.top, 10)
					
				}
			}
		}.frame(width: 101)
	}
	
	// 时间描述
	@ViewBuilder
	private func relativeTimeText(for ts: TimeInterval) -> some View {
		
		if ts < 60 * 5 {
			Text("Just Now")
				.font(.system(size: 12))
				.foregroundColor(.gray)
		}else {
			Text(Self.format(minutes: ts))
				.font(.system(size: 12))
				.foregroundColor(.gray)
		}
		
	}
	private static func format(minutes: TimeInterval) -> String {
		let diff = Int(minutes)
		let min = diff / 60
		let hour = min / 60
		switch hour {
		case 0:  
			return "\(min)min ago"
		default:
			switch min {
			case 0:
				return "\(hour)h ago"
			default:
				return "\(hour)h \(min%60)min ago"
			}
		}
	}
	// 头像
	private var avatarOverlay: some View {
		HStack {
			avatarView(uiImage: entry.mineAvatar)
			Spacer()
			avatarView(uiImage: entry.peerAvatar)
		}
		
	}
	
	@ViewBuilder
	private func avatarView(uiImage: UIImage?) -> some View {
		let width:CGFloat = 34
		
		ZStack{
			Color(.white)
				.frame(width:width,height:width)
				.clipShape(Circle())
			
			Image(uiImage: uiImage ?? UIImage(named: "widget_default_avator")!)
				.resizable()
				.aspectRatio(contentMode: .fill)
				.frame(width:width-2,height:width-2)
				.clipShape(Circle())
		}
	}
	
}
struct YLStatusErrorView: View {
	var body: some View {
		ZStack {
			Image("status_widget_bg")
				.resizable()
				.scaledToFill()
				.frame(maxWidth: .infinity, maxHeight: .infinity)
			
			VStack(spacing: 15) {
				Text(NSLocalizedString("status_widget_content_empty", comment: ""))
					.font(
						Font.custom("PingFang SC", size: 12)
							.weight(.medium)
					)
					.foregroundColor(Color(red: 0.47, green: 0.3, blue: 0.26).opacity(0.7))
				
				Image("status_error")
					.resizable()
					.aspectRatio(contentMode: .fill)
					.frame(width: 65, height: 62)
					.clipped()
			}
		}
		.frame(maxWidth: .infinity, maxHeight: .infinity)
	}
}


// MARK: - Widget
struct YLStatusWidget: Widget {
	let kind = "YLStatusWidget"
	
	var body: some WidgetConfiguration {
		StaticConfiguration(kind: kind,
							provider: YLStatusProvider()) { entry in
			YLStatusWidgetEntryView(entry: entry)
				.widgetBackground(Color.clear)
		}
							.configurationDisplayName(NSLocalizedString("status_realtime", comment: ""))
							.description(NSLocalizedString("status_share_mood", comment: ""))
							.supportedFamilies([.systemMedium])
							.disableContentMarginsIfNeeded()
	}
}
