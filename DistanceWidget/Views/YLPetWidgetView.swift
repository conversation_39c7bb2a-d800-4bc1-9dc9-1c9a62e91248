//  YLPetWidgetView.swift
//  DistanceWidget
//
//
//  宠物小组件：展示宠物当前状态、饥饿值与心情值。
//

import WidgetKit
import SwiftUI
import SwiftyJSON

// MARK: - Entry
struct YLPetEntry: TimelineEntry {
	let date: Date
	let pet: YLSpinePetModel?
}

// MARK: - Provider
struct YLPetProvider: TimelineProvider {
	func placeholder(in context: Context) -> YLPetEntry {
		YLPetEntry(date: Date(), pet: nil)
	}
	
	func getSnapshot(in context: Context, completion: @escaping (YLPetEntry) -> Void) {
		YLWidgetPetData.shared.getPetStatus { pet in
			completion(YLPetEntry(date: Date(), pet: pet))
		}
	}
	
	func getTimeline(in context: Context, completion: @escaping (Timeline<YLPetEntry>) -> Void) {
		YLWidgetPetData.shared.getPetStatus { pet in
			let entry = YLPetEntry(date: Date(), pet: pet)
			// 每 15 分钟刷新一次
			let next = Calendar.current.date(byAdding: .minute, value: 15, to: Date()) ?? Date().addingTimeInterval(900)
			completion(Timeline(entries: [entry], policy: .after(next)))
		}
	}
}

// MARK: - ProgressBar View
struct PetProgressBar: View {
	let value: Int // 0 ~ 100
	
	var body: some View {
		GeometryReader { geo in
			ZStack(alignment: .leading) {
				Image("pet_wdiget_progress_bg")
					.resizable()
					.frame(width: geo.size.width)
				
				Image("pet_wdiget_progress_yellow")
					.resizable()
					.frame(width: geo.size.width * CGFloat(max(0, min(value, 100))) / 100 - 4)
					.mask(RoundedRectangle(cornerRadius: (geo.size.height - 4) / 2))
					.padding(.horizontal, 2)
					.padding(.vertical, 2)
			}
		}
		.frame(height: 12)
	}
}

// MARK: - Entry View
struct YLPetWidgetEntryView: View {
	var entry: YLPetProvider.Entry
	
	var body: some View {
		Group {
			if let pet = entry.pet {
				petContent(for: pet)
			} else {
				emptyContent
			}
		}
		.widgetURL(URL(string: "ylcloser://catinfo")!)
	}
	
	// 正常展示
	@ViewBuilder
	private func petContent(for pet: YLSpinePetModel) -> some View {
		ZStack {
			Image("pet_widget_bg")
				.resizable()
				.scaledToFill()
				.frame(maxWidth: .infinity, maxHeight: .infinity)
				.padding(.horizontal, -1)
			VStack(spacing: 6) {
				// 宠物形象
				if pet.petStatus.description.isEmpty {
					Image("pet_empty_icon")
						.resizable()
						.aspectRatio(contentMode: .fit)
						.frame(maxWidth: .infinity, maxHeight: .infinity)
				}else {
					
					SmallGifVideoPlayView(name: pet.gifName)
						.aspectRatio(contentMode: .fit)
						.frame(maxWidth: 100, maxHeight: 100)
						.scaleEffect(1.3) // 放大内容，但不影响 frame 本身
						.padding(.top, 10)
					
					// 进度条
					VStack(spacing: 0) {
						HStack(spacing: 4) {
							PetProgressBar(value: (pet.petStatus == .critical || pet.petStatus == .sick) ? 0 : pet.satiety)
							Image("pet_food_icon")
								.frame(width: 20, height: 20)
						}
						HStack(spacing: 4) {
							PetProgressBar(value: (pet.petStatus == .critical || pet.petStatus == .sick) ? 0 : pet.mood)
							Image("pet_mood_icon")
								.frame(width: 20, height: 20)
						}
						
					}
					.padding(.horizontal, 12)
					.padding(.bottom, 12)
					.padding(.top, 8)
				}
				
			}
		}
	}
	
	// 无宠物
	private var emptyContent: some View {
		ZStack {
			Image("pet_widget_bg")
				.resizable()
				.scaledToFill()
				.frame(maxWidth: .infinity, maxHeight: .infinity)
			VStack {
				Image("status_error")
					.resizable()
					.aspectRatio(contentMode: .fit)
					.frame(maxWidth: 70, maxHeight: 70)
				Text(NSLocalizedString("status_widget_content_empty", comment: ""))
					.font(.system(size: 12))
					.foregroundColor(Color.gray)
			}
		}
	}
}

// MARK: - Widget
struct YLPetWidget: Widget {
	let kind = "YLPetWidget"
	
	var body: some WidgetConfiguration {
		StaticConfiguration(kind: kind, provider: YLPetProvider()) { entry in
			YLPetWidgetEntryView(entry: entry)
				.widgetBackground(Color.clear)
		}
		.configurationDisplayName(NSLocalizedString("status_pets", comment: ""))
		.description(NSLocalizedString("status_pets_moments", comment: ""))
		.supportedFamilies([.systemSmall])
		.disableContentMarginsIfNeeded()
	}
}

