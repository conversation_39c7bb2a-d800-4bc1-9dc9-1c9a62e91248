import Foundation
import Moya
import WidgetKit
import SwiftDate

public struct YLWidgetStatus: Codable {
	public let emoji: String
	public let text: String?
	public let timestamp: TimeInterval
}

final class YLWidgetStatusData {
    static let shared = YLWidgetStatusData()
    private init() {}

	func getPairState(completion: @escaping (_ mine: YLWidgetStatus?,
											 _ peer: YLWidgetStatus?,
											 _ mineAvatar: UIImage?,
											 _ peerAvatar: UIImage?) -> Void) {
		widgetServiceProvider.request(.pairStatus) { result in
            switch result {
            case let .success(resp):
                guard let json = try? JSONSerialization.jsonObject(with: resp.data) as? [String: Any],
					  let data = json["data"] as? [String: Any] else {
					completion(nil, nil,nil,nil)
					return
				}
                func mapStatus(_ dict: [String: Any]?) -> YLWidgetStatus? {
                    guard let d = dict, let uni = d["unicode"] as? String else { return nil }
                    let words = d["words"] as? String
                    // duration: 服务器返回多少小时内过期
                    let duration = d["duration"] as? Double ?? 0
                    let timestamp = duration
                    return YLWidgetStatus(emoji: uni.emojiFromCodePoint(), text: words, timestamp: timestamp)
                }
                let mineArr = data["selfRealRecords"] as? [[String: Any]]
                let peerArr = data["otherRealRecords"] as? [[String: Any]]
                let mineStatus = mapStatus(mineArr?.first)
                let peerStatus = mapStatus(peerArr?.first)
				if let selfAvatar = data["selfAvatar"] as? String, let otherAvatar = data["otherAvatar"] as? String {
					UserDataHelper.getAvatarImg(avatarUrl: selfAvatar, avatarOtherUrl: otherAvatar) { (avatars) in

						completion(mineStatus, peerStatus,avatars.avatarImage,avatars.avatarOtherImage)
					}
				}else {
					completion(mineStatus, peerStatus,nil,nil)
				}
	
              
            case .failure(_):
				completion(nil, nil,nil,nil)
            }
        }
    }
}
