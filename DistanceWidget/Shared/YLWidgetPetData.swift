// YLWidgetPetData.swift
// DistanceWidget
//

import Foundation
import Moya

/// 小组件宠物数据源
final class YLWidgetPetData {
    static let shared = YLWidgetPetData()
    private init() {}
    
    /// 获取宠物状态
    /// - Parameter completion: 回调 `YLSpinePetModel?`，失败或无数据时返回 `nil`
    func getPetStatus(completion: @escaping (YLSpinePetModel?) -> Void) {
		widgetServiceProvider.request(.getPetStatus) { result in
            switch result {
            case let .success(resp):

				guard let json = try? JSONSerialization.jsonObject(with: resp.data) as? [String: Any],
					  let data = json["data"] as? [String: Any], let petInfo = data["petInfo"]  as? [String: Any] else {
					completion(nil)
					return
				}
                let model = YLSpinePetModel()
				model.satiety = petInfo["satiety"] as? Int ?? 0
				model.mood = petInfo["mood"] as? Int ?? 0
				model.petStatus = YLSpinePetStatus(rawValue: petInfo["petstatus"] as? Int ?? 0) ?? .none
				model.currentLevel = petInfo["currentLevel"] as? Int ?? 1
                completion(model)
            case .failure(_):
                completion(nil)
            }
        }
    }
}
class YLSpinePetModel: NSObject, Codable {
	var petStatus: YLSpinePetStatus = .none
	var satiety: Int = 0 // 饱腹
	var mood: Int = 0 //心情
	var currentLevel: Int = 1
	
	var gifName: String {
		var fullName = ""
		switch currentLevel {
		case 1:
			fullName = "幼年"
		case 2:
			fullName = "少年"
		case 3:
			fullName = "青年"
		case 4:
			fullName = "老年"
		default:
			break
		}
		if petStatus == .out {
			return petStatus.description
		}
		return fullName + petStatus.description
	}
}
enum YLSpinePetStatus: Int, Codable {
	case none = 0
	case normal = 1      // 1: 正常
	case hungry = 2      // 2: 饥饿
	case depressed = 3    // 3: 抑郁
	case sick = 4        // 4: 生病
	case critical = 5    // 5: 病重
	case recovering = 6   // 6: 疗养
	case out = 7         // 7: 外出
	case left = 8        // 8: 离开

	var description: String {
		switch self {
		case .normal:
			return "待机"
		case .hungry:
			return "待机"
		case .depressed:
			return "待机"
		case .sick:
			return "生病"
		case .critical:
			return "生病"
		case .recovering:
			return "生病"
		case .out:
			return "离家出走"
		case .left:
			return ""
		default:
			return ""
		}
	}
}
