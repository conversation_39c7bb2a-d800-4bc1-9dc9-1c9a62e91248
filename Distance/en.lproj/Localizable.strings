/* 
  Localizable.strings
  Distance

  Created by Q on 2024/8/27.
  
*/
//翻译到 
//通用图片名称拼接
"YL_allImageLanguage" = "en";
//web语言通用参数
"YL_webLanguage" = "en";

"YL_DefaultLocationlon" = "-77.038721";
"YL_DefaultLocationlat" = "38.896349";

"YL_AddressOne" = "Couple2 Global HQ​88 Market Street, #22-03 CapitaRise Tower​Singapore";
"YL_AddressTwo" = "The CoSpace @ Cecil​137 Cecil Street, #08-01 Lattice Point​Singapore";
"YL_AddressTree" = "Couple2 Innovation Lab​1 Science Park Drive, #B1-07 Synapse Hub​Singapore";

"login_banner_title1" = "Couple2 Space";
"login_banner_desc1" = "DIY your own couple zone.";
"login_banner_title2" = "Raise a Pet Together";
"login_banner_desc2" = "Care for it diligently and witness its growth.";
"login_banner_title3" = "Stay Close, Stay Safe";
"login_banner_desc3" = "Real-time location sharing with trust and consent.";
"login_banner_title4" = "Memories, Tools & Fun";
"login_banner_desc4" = "Designed just for couples.";

"status_widget_teach_title" = "What is the Status Widget?";
"status_widget_teach_center_content" = "With this feature, your status instantly appears on your partner's screen, letting them feel your status changes.";
"status_widget_teach_bottom_content" = "When your partner updates their status, you can also view it within 24 hours!";

"chat_extend_to_try" = "Try";
"real_time_status" = "Real-time Status";
"status_now_action" = "What are you doing now?";
"status_pair_share_tips" = "Let your partner know how you're feeling right now!";
"status_add" = "Add Status";
"status" = "Status";
"status_choose_one_status" = "Select a status";
"status_pair_show_one_day" = "Partner can view for 24 hrs";
"status_edit" = "Edit Status";
"status_add_text" = "Add Text";
"status_publish" = "Post";
"status_publish_success" = "Posted successfully";
"just_now" = "Just now";
"status_delete_sure_tips" = "Are you sure to delete this status?";
"status_self_no_yet" = "No status yet";
"status_pair_no_yet" = "Your partner hasn't updated their status in 24 hours!";
"status_request_pair_update" = "Request update";
"status_request_pair_update_text" = "Share your latest status now!";
"status_request_sended" = "Update reminder sent!";
"status_no" = "No Status";
"add" = "Add";

"email_feedback_default_text1" = "Dear User,\nPlease kindly describe your issue in detail and provide a screenshot of the error. We will look into the problem as soon as possible.";
"email_feedback_default_text2" = "Please do not delete the above information, as it will help us identify the issue more quickly.";


"try_vip" = "Trial VIP";
"person_unmatch_tips" = "Terminating this connection will revert your account to unpaired status.";
"person_logout_tips" = "Your service access will cease, and account data will be ​permanently deleted.";
"week_vip" = "Weekly VIP";
"month_vip" = "Monthly VIP";
"year_vip" = "Annual VIP";
"person_vip_valid_until" = "Valid until​";
"person_view_benefit" = "View Benefits";
"person_about" = "About & Support";
"person_go_vip" = "Upgrade to VIP";
"person_vip_time_limit" = "Limited Time";
"person_vip_claim_now" = "Claim Now";
"person_sounds" = "Sound Effects";
"person_account" = "Account & Relationship";
"person_encourage" = "Encourage us";
"person_follow" = "Follow Couple2";
"person_follow_tips" = "Follow our account to receive the latest updates. We will be here to serve you better!";
"chat_extend_kiss_hint" = "Bring your partner to \"Talk\"\nTry tapping and holding in this area";
"chat_extend_kiss_waiting" = "Waiting";
"chat_extend_kissing" = "Kissing";
"chat_extend_kiss_is_what" = "What is \"Thumb Kiss\"?";
"chat_extend_kiss_introduction" = "A magical new way to reach through the screen and feel your partner's presence.Tap together, feel the moment. The screen gently vibrates, and adorable emojis fall.\nNo matter where you are, your love stays close.In just one second, let a simple touch say: I miss you.";
"chat_extend_to_try" = "Try";
"chat_extend_camera" = "Camera";
"chat_extend_photo" = "Photo";
"chat_extend_kiss" = "Thumb Kiss";
"upgrade_to_premium" = "Upgrade to Premium";
"unlock_partner_status" = "Unlock partner's status";
"use_phone_setting_open" = "Settings";
"use_phone_setting_reminder" = "Reminders";
"use_phone_use_duration_per_day" = "Screen time/day";
"use_phone_use_times_per_day" = "Phone unlocks/day";
"use_phone_screen_remind" = "Unlock alert";
"use_phome_pair_screen_remind_me" = "Notify me on unlock";
"use_phone_once_screen_duration" = "Longest Session";
"chat_quote_recall" = "Citation withdrawn";
"diary_deleted" = "The diary has been deleted";
"track_msg_remind_other" = "Message Partner to remind";
"map_track_distance" = "Distance";
"chat_emoji_delete_tips" = "Do you want to delete the selected emoji?";
"chat_emoji_collect_success" = "Successfully added to favorites";
"chat_emoji_delete" = "Delete";
"chat_emoji_send" = "Send";
"chat_emoji_top" = "Move to the front";
"chat_emoji_favorite" = "Favorite Stickers";
"chat_emoji_manage" = "Manage";
"dress_shop_no_dadta" = "No products found~​";
"put_food_in_firsh" = "Please add food first";
"string_net_error_refresh" = "Network error, \nplease refrest";
"chat_report_allow_pair_privacy" = "Show protection reminder in \"Talk\"";
"use_phone_setting" = "Phone Usage Reminder Settings";
"map_country" = "Country";
"report_msg_too_much" = "Too many auto report notifications?";
"map_track_staying" = "Staying";
"track_stay_time" = "Stayed %1$s h %2$s min";
"map_track_title" = "Today: %1$s places visited";
"map_street" = "Street & Block";
"map_district" = "District";
"map_city" = "City";
"map_province" = "Province";
"chat_report_track_setting" = "Location Reminder Setting";
"chat_report_track_remind" = "Location Reminder";
"chat_report_track_leave" = "Arrival/Departure Alert";
"chat_report_point" = "Stopover Reminder";
"chat_report_allow_pair_track" = "Show location updates in \"Talk\"";
"chat_report_use_protect_title" = "Usage Protection Settings";
"chat_report_permission_set" = "Permission Settings";
"chat_report_allow_pair_privacy_history" = "Your time and location records are visible to your partner.";
"chat_report_set_talk_only" = "Receive \"Talk\" only";
"chat_report_set_all_allow" = "Receive all (Talk & System Notifications)";
"chat_report_all_receive" = "All";
"chat_report_talk_only" = "\"Talk\" only";
"hour" = "h";
"use_phone_times" = "times";
"minute" = "min";
"use_phone_over_time_setting" = "Time Limit Settings";
"use_phone_over_times_setting" = "Usage Limit Settings";
"use_phone_remind" = "Usage Reminder";
"chat_report_remind_setting" = "Reminder Settings";
"use_phone_show_chat" = "Show phone activity in \"Talk\"";
"use_phone_total_time_remind" = "Excess Alert: Total screen time";
"use_phone_over_time" = "Time exceeded";
"use_phone_times_remind" = "Excess Alert: Phone pickups";
"use_phone_over_times" = "Limit reached";
"use_phone_continue_time_remind" = "Excess Alert: Continuous phone use";
"use_phone_open_phone_remind" = "Screen Unlock Alert";
"use_phone_open_phone_hint" = "Remind on every screen unlock";
"use_phone_hint" = "Tips:\n1. Please complete the setup to ensure the best experience. \n2. Data is recorded based on the other party's time zone. \n3. If both parties are in different time zones, the earlier time zone will be used as the reference. \n4. Enabling the subscription reminder will allow you to receive relevant notification alerts.";
"report_push_title_use_phone" = "Phone Usage Reminder";
"chat_report_privacy_set_title" = "Usage Protection Reminder";
"chat_auto_report_setting" = "Auto Report Settings";
"vip_add_3days_free" = "+3 days free trial";
"vip_start_3days_free" = "Start a 3-day free trial";
"string_appraise_preview_title" = "Request a Store Review";
"string_appraise_preview_content" = "We are eager to receive your recognition!";
"submit" = "Submit";
"string_appraise_bad_title" = "We need your suggestions for improvement!";
"app_raise_input_hint" = "Enter your feedback here...";
"person_center_comment" = "Review";
"app_raise_star_low_toast" = "Please give us a 5-star rating, pretty please! QAQ";
"button_sound" = "Button sound effects";
"custom_music_no" = "No music file found, please upload a music file.";
"vip_toast_pair_vip" = "Your partner is already a member. Select a single plan.";
"music_support" = "MP3、AAC、WAV、M4A format only";
"vip_single" = "Single Membership";
"vip_double" = "Couple Membership";
"back_music" = "Background music";
"mode_select" = "Mode selection";
"all_play" = "Global playback";
"cabin_only_play" = "Cabin Only";
"music_off" = "Turn off music";
"music_custom" = "Custom";
"pet_go_out_reward_full" = "This trip was incredibly fruitful~";
"pet_shower" = "Start showering (favor↑)";
"gou_out_desc_title" = "Outing Notice";
"go_out_desc1" = "Every time your pet successfully goes out, it will bring back different rewards. If you're lucky, you might even unlock unique pet stories.";
"go_out_desc2" = "You need to prepare food for your pet's outing. The more lavish the food you prepare, the richer the rewards will be for this outing.";
"go_out_desc3" = "Please note that if you recall your pet early, the outing will be considered a failure, and the food will not be refunded.";
"chat_edit_note" = "Name Your Pet";
"tap_screen_to_continue" = "Click to continue";
"pet_play_enough" = "Pet all you want today, but I won't react!";
"switch_prop_data_dialog_desc" = "Since the old-version props are incompatible with the new pet system, you can exchange them for the new ones.";
"right_now_check" = "View now";
"finish_guide_dialog_content" = "The pet's Satiety and Mood Values will decrease over time. Keep these two values healthy, and your pet will grow and bring you unexpected surprises!";
"help_pet" = "Feeding Guide";
"gift_prop_data_dialog_title" = "New Version Gift Pack";
"gift_prop_data_dialog_desc" = "Prop Gift Pack for loyal pet players~ Helping you explore and enjoy the new pet system!";
"accept_with_joy" = "Accept";
"pet_go_out_alarm" = "The pet is not at home right now.";
"pet_traveling" = "Traveling...";
"pet_recall_now" = "Recall Now";
"view_story" = "View Story";
"growth" = "Growth";
"pet_go_out_recall" = "Your pet is currently away from home. If you recall now, no reward or food will be returned. Are you sure you want to continue?";
"invite_other_put_food" = "Invite your love";
"go" = "Departure";
"intimacy" = "Favor";
"pet_seriously_sick_now" = "has been unwell for a long time, and its condition has been critical";
"pet_sick_seriously_remind_bad" = "If left untreated, your pet may no longer be with you...";
"pet_sick_healing" = "Treatment is in progress, please be patient...";
"pet_sick_now" = "hasn't been cared for a long time and has been sick...";
"pet_sick_remind_bad" = "Without treatment, your furry baby's condition will get worse.";
"pet_heal_skip_wait" = "Skip";
"pet_heal_ad_minus_time" = "Wait Time";
"pet_heal_now" = "Instant Cure";
"pet_play_net_error" = "Your network is acting up, please try again.";
"pet_normal_medicine_time" = "Recovered in %1$s";
"pet_quick_medicine_vip_free" = "VIP First-Time Free";
"pet_shower_over_limit" = "Daily limit reached";
"pet_medicine_vip_free" = "VIP First-Time Free";
"pet_come_play" = "Poop-picker, what are you doing now~ Come give me a pet!";
"pet_play_no_feeling" = "I don't feel anything. Let's do it again.";
"pet_effect_mood" = "Mood";
"pet_play_bad" = "Poop-picker, your massage skills are awful.\n Poor rating!";
"pet_play_good" = "Nice massage, five-star praise.";
"send_msg_to_other" = "A reminder message has been sent to the partner.";
"warn_other" = "The partner has been reminded";
"notice_other_put_food" = "Let your partner prepare food for the pet when going out, to earn more rewards!";
"go_recharge" = "Recharge";
"pet_buy_and_use" = "Purchase and Use";
"pet_use" = "Use";
"pet_heal_ill" = "Treat illness.";
"pet_heal_sick" = "Treat serious illness.";
"pet_look_feed_book" = "View Feeding Guide";
"ok" = "OK";
"pet_healthy_now" = "【%1$s】has fully recovered! \nPlease take good care of your furry friend~";
"purchase_quantity" = "Quantity";
"fresh_shrimp" = "Fresh Shrimp";
"pet_guide_food_box" = "Your pet is a bit hungry now. Click here to feed!";
"pet_guide_buy_food" = "Click on the food to feed your pet~";
"pet_guide_toy_box" = "Your pet feels gloomy now. Click here to give a toy and cheer it up~";
"pet_guide_buy_toy" = "Click on the toys to play with your pet～";
"skip_guide" = "Confirm to skip the beginner tutorial?";
"feeding_guide" = "View more information in the Feeding Guide";
"YL_Skiptheintroduction" = "Skip";
"pet_pop_string1" = "Raising pets together and watching them grow little by little, each progress brings joy to the heart";
"pet_pop_string2" = "Come and experience the heartwarming pet feature~Raise a little cutie together with your partner~";
"pet_choose_new" = "Upgrade Now";
"pet_choose_old" = "Proceed with current version";
"pet_new_version_online" = "We are introducing an entirely new pet system to offer you a refresh experience.\nUpgrading to the new system means:";
"pet_old_clear" = "Clearing old pet data and discontinuing the original pet system. Your remaining props will be exchanged for new ones.";
"pet_pair_new_version" = "Once upgraded to the new version, the latest pet system will be automatically activated.";
"pet_switch_check_toast" = "Please check all the notes";
"pet_play_add_power" = "Refresh stamina";
"YL_Staminaresetsdailyat" = "Stamina resets daily at midnight. Cherish the opportunity!";
"YL_Notenoughstaminacant" = "Not enough stamina, can't play with this cat for now.";
"pet_play_power_vip_infinite" = "VIP Unlimited Stam";
"pet_play_power_add_now" = "Restore now";
"pet_play_power_tips" = "Watch a video to cheer up the cat, and restore %1$s stamina. (Stamina refreshes daily at midnight, please use it in time.)";
"pet_play_refresh" = "Refresh stamina after %1$s";
"go_at_once" = "Depart Now";
"warn_ta" = "Remind";
"pet_go_out_food_tips" = "Honey, come and prepare food for our pet's travel~";
"shop_free_use" = "Free to use";
"lng" = "Longitude";
"lat" = "Latitude";
"YL_Mapresourceloadingfa" = "Map resource loading failed!";
"msg_recall_by_user" = "%1$s recalled a message.";
"msg_recall_by_self" = "You recalled a message.";
"msg_recall_re_edit" = "Re-edit";
"map_send_locatioon_empty" = "Getting address details. Please try again later.";
"map_pair_location_remind" = "Location update stopped";
"map_track_no_stop_point" = "No tracking data available";
"map_track_point_pair_remind" = "Please enable your partner's location sharing before viewing";
"to_open" = "Enable";
"map_location_send_success" = "Share location successfully";
"map_gps_weak" = "weak";
"map_phone_cellular" = "Network Data";
"map_pair_location_open_im_title" = "Babe, enable location services so I can always be there for you.";
"map_pair_real_loc_open_im_title" = "Babe, enable real-time location sharing so I can always be there for you.";
"map_pair_location_off" = "Partner's location unavailable.";
"map_pair_real_location_off" = "Partner's real-time location off.";
"format_hour" = "h";
"format_minute" = "min";
"map_track_points" = "Places";
"map_pair_remind_location" = "Reminder to turn on";
"map_phone_sleeping" = "Inactive";
"map_track_stop_point" = "Stopover Point";
"map_track_stop_time" = "Stop Duration";
"map_pair_distance" = "Distance";
"map_phone_use" = "Phone Activity";
"distance" = "Distance";
"map_send_self_location" = "Share location";
"map_remind_sent" = "Reminder message sent";
"map_send_location_fail" = "Location sharing failed. Please enable location sharing first.";
"action_frequent_try_after" = "The operation is too frequent, please try again later.";
"map_distance_vip" = "Partner is at*** ";
"YL_Unabletogetlocationitryagainlater" = "Unable to access location. Please try again later.";
"map_pair_no_location" = "Current location not available";
"jump_third_desc" = "Follow our platform for the latest news!";
"map_share_off" = "Location sharing off";
"map_location_no_share" = "Not sharing your location";
"map_real_location_share_off" = "Share your real-time location";
"map_real_location_delay" = "Location update delay";
"chat_phone_use_time" = "Using %1$s min";
"chat_phone_use_min_ago" = "%1$s min ago";
"chat_phone_use_hour_ago" = "%1$s h ago";
"chat_phone_unuse" = "Not used";
"map_pair_phone_using" = "Active";
"map_phone_unknown" = "Unknown";
"chat_network" = "Network";
"chat_battery" = "Battery";
"login_recent" = "Recent Use";
"register_login" = "Sign up and log in";
"login_email_quick" = "Sign in with this account";
"login_other_platform" = "Sign in with other account";
"app_slogan" = "Sweet interactive space for couples";
"The_recording_time_is_too_short" = "Message too short";
"coin_charge_limit" = "Sale";
"account_log_off" = "Account Cancellation";
"vip_get_package" = "Claim the Gift Package";
"release_to_cancel" = "Release to Cancel";
"YL_Loading" = "Loading...";
"vip_protect_other" = "Protecting and Cherishing Your Other Half";
"YL_Thecouplemembershipi" = "Your membership is expiring soon, and numerous benefits will be unavailable.";
"chat_voice_send" = "Release to send";
"chat_voice_send_cancel" = "Release to Cancel";
"chat_voice_speak" = "Hold to Talk";
"vip_recharge" = "Renewal successful";
"vip_permanent" = "Permanently valid";
"vip_valid_time" = "Valid until %1$s";
"YL_Membershipactivation" = "Membership activation failed";

"chat_quote_vip_toast" = "The quoting feature is a VIP exclusive benefit. Please activate your VIP membership and try again";
"YL_Coversetsuccessfully" = "Cover set successfully";
"YL_Failedtoloadresource" = "Failed to load resources, please try again~";
"move_up_to_cancel" = "Slide Up to Cancel";
"accept" = "Accept";
"anniversary_delete_hint" = "Are you sure you want to delete this special day?";
"shop_dress_rollback_content" = "Once confirmed, it will revert to the layout before entering the store.";
"album_upload_hint" = "Uploading image/video, please do not exit Couple2.";
"album_upload_fail" = "Upload failed";
"album_pending_upload" = "To be uploaded";
"cat_die_hint" = "My dear, I have already gone to heaven. Please take good care of the next cat~";
"cat_die_to_shop" = "Go to the mall";
"YL_Healthvalueisfullnon" = "Healthy enough! No need for any medicine.";
"YL_Clicktoprocessmyinvi" = "Click here to process my invitation~";
"YL_Clicktoviewmymatchin" = "Click here to view my matching code~";
"YL_Immatchingwithyoucom" = "I’m matching with you, come agree quickly, and let’s have sweet interactions in our daily moments of excitement!";
"match_share_text" = "What are you waiting for? Quickly fill in the matching code and sweetly interact with me in Heartbeat Daily!";
"please_scan_other" = "Please scan the matching code of your partner";
"cat_nickname_empty_toast" = "Nickname cannot be empty~";
"YL_Invalidmatchingcode" = "Invalid matching code";
"YL_Nicknamemustbecharac" = "Nickname cannot exceed 10 characters.";
"YL_Pleaseselectgender" = "Please select your gender";
"person_message_received" = "Message received";
"person_message_sent" = "Message sent";
"track_recording_stay_data" = "Recording stopover data";
"coin_charge_once" = "1st refill";
"YL_PleaseCheckConfirm" = "Please check to confirm";
"YL_PleaseCheckPrecautions" = "Please check the notice before proceeding.";
"match_same_sex_no_dress" = "Same-gender matching do not support the dress-up mall feature.";
"reload" = "Reload";
"pay_fail" = "Payment failed";
"YL_CancelPaymentA" = "Cancel Payment";
"person_vip_other" = "Open a membership for them";
"vip_send_pair" = "The membership gift has been accepted! Your partner is now also a VIP member.";
"vip_receive_pair" = "Darling, I've gotten you a VIP membership service! Let's record our lives together~";
"YL_TryAgainLater" = "try again later.";
"pay_continue" = "Continue to pay";
"close" = "Close";
"pay_no_complete" = "Payment not completed";
"YL_WithinMinutesMore" = "Within minutes, you or your partner may have attempted to purchase a membership for you. To avoid duplicate payments, please confirm that no payment has been made before proceeding with the purchase. Do you wish to continue with the payment?";
"YL_WithinMinutesMoreADD" = "Within the past few minutes, you or your partner may have attempted to purchase membership service. To avoid duplicate payments, please confirm that no payment has been made before proceeding. Do you want to proceed with the payment?";
"pay_error_des" = "You have canceled the payment or an error occurred during payment. If the payment has been completed, please ignore this message";
"cat_but_congratulation" = "Congratulations to earn";
"vip_members" = "Couple2 Premium";
"coin_charge_success" = "Payment completed successfully! You have earned";
"YL_Paymentsuccess" = "Payment successful";
"pic_wall_back_edit" = "Do you want to discard your changes to this image?";
"save_to_phone" = "Save to phone";
"shop_other_purchase" = "Your partner just purchased %1$s.";
"other_low_version" = "The other party's version is too low, please remind them to upgrade!";
"month_array[0]" = "Jan";
"month_array[1]" = "Feb";
"month_array[2]" = "Mar";
"month_array[3]" = "Apr";
"month_array[4]" = "May";
"month_array[5]" = "Jun";
"month_array[6]" = "Jul";
"month_array[7]" = "Aug";
"month_array[8]" = "Sept";
"month_array[9]" = "Oct";
"month_array[10]" = "Nov";
"month_array[11]" = "Dec";

"pig_vip_get_double_btn" = "VIP double benefits";
"match_copy_toast" = "The matching code has been copied, send it to your partner";
"YL_Unabletoobtaintheoth" ="Unable to obtain the other person's location.";
"map_permission_to_open" = "Please Use location permissions before checking the location";
"YL_Adloadingerror" = "Error loading ads";
"YL_Stickynotesnotyetsav" = "Notes not saved yet";
"YL_Loginfailed" = "Login failed";
"YL_Youraccounthasbeenlo" = "Your account is logged in on another device.";
"YL_Accelerationfailure" = "Acceleration failed";
"pig_accelerate_once" = "Each claim can only be accelerated once. If you need to accelerate again, please claim first~";
"sticky_delete_confirm" = "Once deleted, the note cannot be recovered. Do you want to proceed?";
"input_reached_limit" = "Character limit exceeded.";
"after_vip_experience" = "After becoming a Premium , you can experience~";
"YL_RestorePurchases" = "Restore Purchase";
"YL_RestorePurchasesSuccese" = "Purchase restored successfully";
"YL_FreeThreedayTrial" = "3-day free trial";
"YL_ContinuousMonthlySubscription" = "Monthly VIP";
"YL_SignmembershipQYHYTC" = "Popup for Trial Membership Sign-Up";
"YL_Year" = "Year";
"month" = "Month";
"diary_all" = "All Diaries";
"diary_has_written_to_complete" = "Today's diary entry is ready. Click here to complete it now!";
"diary_to_complete" = "Complete now";
"diary_today_has_written" = "Today's diary has been added. Would you like to edit it?";
"diary_to_edit" = "Edit";
"cancel" = "Cancel";
"diary_private_not_view" = "Private diary is unable to view.～";
"diary_not_add_future" = "Diary entries cannot be added for future dates.";
"diary_has_unsaved_edit" = "There are unsaved edits on this date, do you want to continue editing?";
"diary_new" = "Add";
"diary_edit" = "Edit";
"diary_add" = "Add diary";
"diary_not_saved_quit" = "The diary has not been saved yet. Are you sure you want to exit?";
"confirm" = "Confirm";
"diary_modify_not_saved_quit" = "The modified diary has not been saved. Are you sure you want to exit?";
"diary" = "Diary";
"diary_no_available" = "No diary entries available~";
"save" = "Save";
"diary_content_exceeded_limit" = "Your content has exceeded the character limit";
"YL_FailedUploadImagePleaseTryAgain" = "Failed to upload the image, please try again～";
"YL_EditSuccess" = "Edit successful～";
"anniversary_add_success" = "Added successfully～";
"YL_YouCanAdduptoPictures" = "You can add up to 9 images~";
"diary_comment" = "Message";
"desktop_send" = "Send";
"person_words_input_hint" = "Type here...";
"YL_InputZSDDSX" = "Input the number of characters to reach the %1$s character limit";
"delete" = "Delete";
"diary_comment_hint" = "Leave your footprints....";
"diary_no_comment" = "No messages yet～";
"network_error" = "Network is acting up. Please try again later.!";
"error_retry_later" = "Failed to retrieve data";
"edit" = "Edit";
"YL_ErrorGettingDiaryDetails" = "Retrieving diary details failed";
"diary_delete_confirm" = "Sure you want to delete that diary?";
"delete_success" = "Delete Successful";
"diary_delete_comment_confirm" = "Sure you want to delete this message?";
"YL_EmoticonShop" = "Emoji Store";
"diary_moment_feeling" = "This moment's mood feels like...";
"diary_that_moment_feeling" = "The feeling at that moment..";
"YL_AtLeastChooseMood" = "At least pick one~";
"diary_select_max_3" = "Maximum of 3 selections allowed";
"YL_OnMonday" = "on Monday";
"YL_Tuesday" = "Tuesday";
"YL_Wednesday" = "Wednesday";
"YL_Thursday" = "Thursday";
"YL_Friday" = "Friday";
"YL_Saturday" = "Saturday";
"YL_Sunday" = "Sunday";
"sticky_input_hint" = "Please enter content";
"diary_reason" = "Reason";
"diary_private" = "Private";
"diary_share_feeling" = "Share your feelings...";
"action_save_emoj" = "Add emoji";
"person_center_activate_now" = "Activate now";
"YL_TheNoteCannotEmpty" = "The note cannot be empty~";

"login_privacy" = "By logging in, you agree to 《Terms of Use》 & 《Privacy Policy》";
"login_privacy_agreement" = "《Privacy Policy》";
"login_user_agreement" = "《Terms of User》";
"pet_edit_name" = "Change Name";
"cat_cannot_change_nickname" = "Please adopt a cat first before you change the nickname.";
"cat_own_nickname" = "Give your cat a unique nickname!";
"YL_CatHouseExploration" = "Cat Nest Exploration 2";
"cat_new_nickname_hint5" = "Enter a new nickname (5 characters).";
"cat_new_nickname_hint12" = "Enter a new nickname (12 characters).";
"input_note_hint" = "Edit Nickname (12 characters)";
"YL_CatNameTooLong" = "The cat's name is too long.";
"YL_NetworkConnectionFailed" = "Network connection failure";
"pay_success" = "Purchase Successful";
"pet_shop_top_up" = "Recharge";
"pay_now" = "Pay now";
"buy_num" = "Purchase Quantity";
"pet_shop_total" = "Total";
"coins" = "Coins";
"cat_health" = "Health";
"cat_hunger" = "Satiety";
"cat_mood" = "Mood";
"cat_bond" = "Favor";
"cat_die" = "Dead";
"cat_sick" = "Sick";
"cat_ill" = "Critically Ill";
"pet_effect_health" = "Health";
"think_again" = "Cancel";
"modify_nickname" = "Change Username";
"days" = "days";
"album_some_photos" = "%1$s Photos";
"album_some_videos" = "%1$s videos";
"push_new_message" = "You have a new message";
"image" = "[Photos]";
"video" = "[VIDEO]";
"push_voice" = "[VOICE]";
"push_location" = "[Location Information]";
"update_new_version" = "A new version is online";
"update_use_now" = "Update now";
"sticky_empty" = "No note records yet~";
"shop_dress_rollback" = "Do you want to undo all changes and restore your previous furniture and styling setup?";
"reward_vip_double" = "Member Privileges Double Rewards";
"km" = "km";
"track_Left" = "Left";
"track_move" = "Moved";
"track_Stay" = "Stop";
"track_question_title" = "Why isn't the location updating?";
"track_question_hint" = "The following issues may have occurred:";
"track_question_title1" = "\"Always share location\" is not enabled.";
"track_question_tips1" = "Tip: Set location sharing to \"Always share location\".";
"track_question_title2" = "Android: Background app refresh is turned off.";
"track_question_tips2" = "Tip: Enable Background App Refresh and set permissions to keep the app running.";
"track_question_title3" = "IOS: Couple2 malfunctioning.";
"track_question_tips3" = "Tip: Reopen the app.";
"track_question_title4" = "Weak GPS or network signal.";
"track_question_tips4" = "Tip: Retry in an open area with good GPS signal.";
"track_question_suggestion_title" = "If the issue persists, try the following resolutions:";
"track_question_suggestion_tips1" = "Turn off Low Power Mode";
"track_question_suggestion_tips2" = "Reinstall Couple2";
"track_i_know" = "Got it";
"track_no_data" = "New location unavailable.";
"track_no_data_why" = "Why";
"track_no_arrival" = "No stopover point available";
"YL_Babecheckthelocation" = "Babe, check the location permission settings, upload your location, so I can always be there for you.";
"track_recording_move_data" = "Tracking movement data";


////////mark: 以下皆为自动生成
"login_by_email" = "Sign in with Email";
"login_google_btn" = "Sign in with Google";
"login_email_receive_code" = "Get verification code";
"register_title" = "Complete personal information";
"register_input_nickname" = "Please enter a nickname";
"register_input_birthday" = "Please select your birthday";
"match_info_male" = "Male";
"match_info_female" = "Female";
"next_step" = "Next step";
"match_my_code" = "My matching code";
"match_scan_description" = "Simply have your partner scan, and the match will be completed";
"match_send_invitation" = "Send Invitation";
"match_scan_to_pair" = "Scan to Match";
"match_confirm_description" = "Registered, you can fill in the matching code of the other party";
"match_confirm_btn" = "Enter matching code";
"match_dialog_title" = "Please enter the matching code of your partner";
"match_dialog_input_hint" = "Please fill in";
"match_check_dialog_title" = "Verify matching information";
"match_check_dialog_content" = "The character's appearance will differ based on your chosen gender, so please review both options carefully.";
"match_check_dialog_modify_info" = "Change Information";
"match_dialog_btn_bind" = "Binding";
"notification" = "Notification";
"change_sex_alert" = "The current gender is 【%1$s】. Are you sure you want to change the gender to 【%2$s】?";
"love_info_sex_alarm2" = "Same-gender matching for character costumes is not currently supported";
"love_info_sex_alarm" = "Notice: Characters displayed for different genders may vary, and loading may take longer~";
"distance" = "Location";
"shop" = "Store";
"house_guide_text_step1" = "You can swipe left or right in the current scene~";
"house_guide_text_step2" = "Long press on the furniture to drag it freely~";
"house_guide_welcome" = "Welcome home";
"pig_full_text" = "Your piggy bank is full, please claim it soon~";
"pig_countdown_text" = "Countdown to completion";
"pig_speed_btn" = "Boost Now";
"pig_get_btn" = "Claim";
"pig_get_double_btn" = "Excessive claim";
"pig_no_coins" = "No coins available to claim~ ";
"YL_Congratulationsonrec" = "Congrats! you get";
"pic_wall" = "Photo wall";
"reset" = "Reset";
"pic_wall_replace" = "Replace";
"pic_wall_download" = "Download";
"pic_wall_share" = "Share";
"person_words_title" = "Please enter a message for your partner; they will be able to view it once they're online~";
"person_confirm_words" = "Confirm";
"map_open_realtime_hint" = "Enable real-time location to let them guard you at all times";
"map_open_loc_hint" = "Use location to view distance";
"map_setting_open_loc" = "Use location";
"map_setting_start_set" = "Setting up";
"map_setting" = "View distance settings";
"map_setting_not_open_loc" = "Do not Use location";
"map_not_open_loc_description" = "Your partner cannot view your location on the map";
"map_open_loc_description" = "Your partner can track your location on the map";
"map_setting_realtime" = "Real-time location";
"map_setting_recommend" = "recommended";
"map_setting_realtime_description" = "Timely update your Use location";
"diary_save_edited_records" = "Retain edit history";
"cat_happy" = "Happy";
"cat_melancholy" = "Sad";
"diary_what_feel" = "What makes you feel %1$s?";
"pet_food" = "Food";
"button_preview" = "Preview";
"common_all" = "All";
"sticky_write" = "Write Notes";
"sticky_is_top" = "Pin to top?";
"sticky_send_desktop" = "Send to your partner";
"person_center_widgets" = "Desktop widgets";
"cat_feed" = "Feed";
"cat_toy" = "Toy";
"cat_medicine" = "Medication";
"cat_1min" = "Satiety 1/min";
"pet_effect_Bond" = "Favor";
"growth_value" = "Satiety +xxx, Mood +xxx, Health +xxx, Favorability +xxx";
"cat_feed_empty" = "Your cart is empty~ Go grab something now!";
"cat_go_store" = "Go to Pet Store";
"cat_feed_full" = "The cat is already full; feeding it now will not increase its fullness or mood values. Do you want to continue feeding?";
"cat_play_full" = "The cat's mood value is already full. Playing with toys won't add its mood value any further. Would you like to continue playing with the toy?";
"cat_play" = "Play with toys";
"cat_not_waste" = "Not a penny wasted!";
"YL_Usingcatfoodisnotver" = "It's not the best time to feed %1$s now, as it will only increase the satiety value by %2$s. Are you sure you want to use it?";
"cat_confirm_use" = "Confirm use";
"cat_melancholy" = "Depressed";
"cat_hungry" = "Hungry";
"cat_to_die" = "Dead";
"pet_shop_count" = "Amount";
"pet_shop_top_up" = "Top Up";
"pet_shop_coin_short" = "You are still short of %1$s coins~";
"chat_card_low_version" = "Couple2";
"slogan" = "Sweet Couple Interaction Space";
"task" = "Task";
"resign" = "Make Up ";
"sign_7_days_title" = "Sign in for 7 consecutive days and receive more coins!";

"sign_rewards_double" = "VIP double sign-in rewards!";
"sign_7_days_resign_vip" = "Would you like to spend XXX gold coins to make up for that day's missed sign-in?";
"sign_7_days_resign" = "You have missed today's sign-in. Would you like to make up and receive the reward by watching the video?";
"sign_7_days_resign_get" = "Sign-in Make-up Reward";
"match_info_title" = "Love Information";
"change_avatar" = "Change Avatar";
"match_info_nickname" = "Nickname";
"match_info_birthday" = "Birthday";
"love_info_sex" = "Gender";
"text_delivered_msg" = "Delivered";
"text_ack_msg" = "Read";
"chat_emoji_recent_use" = "Recently Used";
"chat_emoji_all" = "Emojis";
"sticky" = "Notes  ";
"chat_detail_setting" = "Message Settings";
"chat_detail_setting_alarm" = "Sound, vibration, etc";
"chat_message_sound" = "Message Sound";
"chat_message_vibration" = "Vibration";
"chat_delete_msg_desc" = "Once confirmed, local chat history will be deleted.";
"chat_notification_setting" = "Show Notification Details";
"chat_notification_setting_desc" = "When turned off, notification will show as \"You have a new message.\"";
"chat_search_no_result2" = "No files in this category";
"anniversary" = "Anniversary";
"anniversary_start_date" = "Start Date: ";
"anniversary_list_help" = "Anniversary List Help";
"anniversary_delete_help" = "How to delete/pin an anniversary?";
"anniversary_delete_desc" = "Swipe left on an anniversary to reveal action buttons (Relationship anniversary cannot be deleted).";
"anniversary_order_help" = "How to rearrange the order of anniversaries?";
"anniversary_order_desc" = "Long press on an anniversary, then drag to rearrange the order.";
"anniversary_edit" = "Edit Anniversary";
"anniversary_title" = "Anniversary Title / Please enter the event name";
"anniversary_include_start" = "Include Start Date";
"anniversary_repeat" = "Repeat Annually?";
"anniversary_remind" = "Set Periodic Reminders?";
"permission_enable" = "Enable Permissions";
"permission_ensure" = "To ensure the proper functioning of features";
"calendar" = "Calendar / Used for anniversary reminder function";
"start_now" = "Activate Now";
"YL_Album" = "Album";
"album_unlock_unlimited_space" = "Unlock unlimited space";
"album_create_new" = "Create New Album";
"album_other_upload_new" = "Your partner uploaded %1$s new contents";
"album_input_name_hint" = "Please enter the album name (within 20 characters)";
"album_empty_toast" = "The album name cannot be empty~";
"album_upload_img_video" = "Upload photo/video";
"album_empty" = "This album has no photos yet~";
"album_delete" = "Delete Album";
"album_exit_sure" = "Are you sure you want to exit?";
"album_exit_after" = "Content will not be saved upon exit.";
"album_empty_cover" = "This album is currently empty. Please add some photos and then select a cover.";
"album_batch_manage" = "Batch Management  ";
"album_selected" = "Selected";
"album_unselected" = "Deselect";
"album_select" = "Select";
"album_no_for_action" = "No images/videos available for action  ";
"album_delete_content_sure" = "Are you sure you want to delete the selected【%1$s】photos/videos?";
"album_delete_cannot_recover" = "Once deleted, they cannot be recovered.";
"album_edit" = "Edit Album";
"album_rename" = "Change Album Name";
"album_modify_name" = "Modify Album Name";
"album_change_cover" = "Change Cover";
"album_select_cover" = "Select Cover";
"album_delete_sure" = "Are you sure you want to delete this album?";
"album_deleted_over" = "Once deleted, all photos in the album will be deleted as well";
"album_set_cover" = "Set as Cover";
"love_list" = "Love Checklist";
"love_list_add_plan" = "Add Plan";
"love_list_name" = "Plan Name";
"YL_SaveQIshi" = "Save %1$s0%";
"chat_emoji_management_complete" = "Completed";
"love_all_list" = "All Lists";
"love_list_complete_task" = "Completed";
"love_list_not_complete" = "Incomplete";
"love_list_add" = "Add New Small Task";
"love_list_ta" = "I want to do this with you";
"love_list_input_name" = "Please Enter the Small Task Name";
"love_list_click_upload" = "Click to Upload This Moment";
"love_list_think" = "Write down your feelings~";
"love_list_show_off" = "Share";
"love_list_no_task" = "No tasks yet, create one now~";
"desktop" = "Pic Drop";
"desktop_camera_permission" = "Please enable camera permissions";
"desktop_camera_now_open" = "Enable Now";
"delay_photo" = "Time-lapse";
"desktop_history" = "History";
"desktop_backup" = "Return to Image Transfer";
"my" = "Me";
"desktop_image_delete_tips" = "Are you sure you want to delete this image?";
"desktop_image_delete_tips_content" = "Deleting it will also remove the image from the  Album and it cannot be recovered";
"desktop_alarm" = "Desktop Image Transfer Prompt";
"desktop_alarm_desc" = "To see the photos captured by each other, both parties need to add widgets to the desktop~";
"shop_furniture" = "Furniture";
"shop_character" = "Character";
"shop_category" = "Categories";
"camera" = "Camera";
"shop_owned" = "Owned";
"shop_bill" = "Store Spending Bill";
"coin_recharge_help" = "Recharge Help";

"coin_charge" = "Coin Recharge";
"coin_recharge_flow" = "Coin Transactions";
"open_vip_desc" = "Upgrade to Membership Now, Enjoy Exclusive Services More!";
"vip_month_desc" = "You already have the monthly membership.";
"vip_year_desc" = "You already have the annual membership.";
"person_vip_surprise" = "Gift";
"person_center_widgets" = "Widgets";
"person_center_advice" = "Feedback";
"person_center_privacy" = "Privacy Policy";
"person_center_about" = "About Us";
"person_center_website" = "Visit the official website";
"person_center_language" = "Language";
"person_center_language_sys" = "Follow the system";
"person_center_unbind" = "Unpair";

"log_out" = "Log Out";
"person_center_logout_sure" = "Are you sure you want to log out?";
"vip_auto_renewal" = "Automatic renewal, cancel anytime";
"coin_charge_policy" = "I have read and agree to the 《Terms of User》 & 《Privacy Policy》";
"vip_buy_other" = "Gift";

"vip_open_now" = "Subscribe now";
"YL_Uploadsuccessful" = "Upload successful";
"YL_OhnoTheHeartbeatmemb" = "Oh no! The Heartbeat membership has expired";
"vip_gone_limit" = "Membership expired! Renew now to regain access to member privileges.";
"vip_renew" = "Renew now";
"diary_not_add_future" = "Diary entries cannot be added for future dates.";
"diary_continue_edit" = "Continue";
"delete_success" = "Delete Successful";
"YL_Failedtoretrievethea" = "Failed to retrieve the anniversary date.";
"anniversary_top_success" = "Pinned successfully.";
"YL_Aspecialday" = "A special day!";
"anniversary_remind_today" = "Remind today";
"anniversary_remind_before" = "Remind one day before";
"YL_Already" = "Already";
"day" = "Day";
"YL_Remaining" = "Remaining";
"every_year" = "/Every year";
"YL_Todayisdontforgetthi" = "Today is %1$s, don't forget this important day!";
"YL_Tomorrowisdontforget" = "Tomorrow is %1$s, don't forget this important day!";
"anniversary_title_hint" = "Please enter the event name.";
"anniversary_add_date_empty" = "Please enter the event date.";
"anniversary_date" = "Anniversary date";
"anniversary_include_start_alarm" = "（Only positive days）";
"love_list_before_today" = "Choose a date before today!";
"love_list_delete_plan_content" = "Once deleted, the plan cannot be recovered. Are you sure to delete it?";
"love_list_delete_plan_title" = "Confirm delete plan.";
"loading" = "Loading failed";
"string_love_list_finish_empty" = "No completed tasks yet\nLove is a long journey, take your time~";
"string_love_list_unfinish_empty" = "Wow, fantastic! All the tasks are done!\nLet's be together on your future journey~";
"YL_Networkerrorpleasetr" = "Network error, please Please try again later..";
"YL_Thistaskhasbeendelet" = "This task has been deleted.";
"love_list_count_max" = "The number of tasks cannot exceed 999.";
"love_list_create_success" = "Created successfully";
"love_list_delete_desc" = "Once deleted, the task cannot be recovered. Are you sure to delete it?";
"love_list_delete" = "Confirm to delete the task.";
"love_list_pic_empty" = "Please add a check-in image first!";
"save_success" = "Saved successfully.";
"love_list_plan_want" = "Write down the plan you want to accomplish together!";
"love_list_plan_name_empty" = "Please enter the plan name.";
"love_list_logo_alarm" = "Select a cute icon!";
"love_list_max_limit" = "Maximum character limit reached.";
"YL_Textcannotexceedchar" = "Text cannot exceed 20 characters.";
"love_list_plan_logo" = "Plan icon";
"album_not_support_upload" = "Pic Drop requires camera access to function properly.";
"report_msg_too_much_set" = "Go to Settings";
"YL_Requestcamerapermiss" = "Request camera permission";
"desktop_no_history" = "No photos available at the moment~";
"desktop_sending" = "Sending";
"YL_Imageuploadfailedple" = "Image upload failed, please try again";
"desktop_send_success" = "Sent successfully";
"YL_Dearaddthedesktoppho" = "Dear~ adding Pic Drop widget, you'll be able to receive the photos I send anytime on your desktop~";
"YL_Viewtutorial" = "View tutorial";
"YL_Failedtodownloadthei" = "Failed to download the image, please confirm if the album permissions are enabled";
"image_download_success" = "Download image successfully";
"warehouse" = "Warehouse";
"YL_Loadingfailedpleasec" = "Failed to load, please try again.";
"gold_charge_agree" = "Read and agree";
"we_together_date" = "The days we have been together";
"modify_fail" = "Failed to modify";
"person_vip_center" = "Membership Center";
"modify_success" = "Modified successfully";
"YL_WhatshouldIdoifImwor" = "What should I do if I am unable to receive notifications when my partner sends messages?";
"YL_ClickGotoSettingsand" = "Click \"Go to Settings,\" and we will provide a solution. Follow the steps to receive message notifications";
"YL_Failedtoretrieveimag" = "Failed to obtain the image address.";
"chat_emoji_save_success" = "Emoji saved successfully.";
"YL_Failedtoretrievefavo" = "Failed to retrieve Favorite Stickers, please try again";
"action_copy" = "Copy";
"action_recall" = "Withdraw";
"action_quote" = "Quote";
"YL_Theotherpartyretract" = "Your partner recalled a message";
"YL_Messagelengthexceede" = "Message length exceeded";
"chat_quote_no_exist" = "The quoted text does not exist";
"chat_detail" = "Chat Details";
"chat_detail_find" = "Search chat history";
"search" = "Search";
"chat_quick_search" = "Quickly search by category";
"chat_image_video" = "Image/Video";
"chat_search_no_result" = "No results";
"YL_Queryrecordfailed" = "Failed to retrieve records";
"chat_detail_roam_date" = "Up to 180 days";
"chat_detail_roam" = "Restore chat history on roaming";
"YL_RoamingSky" = "%1$s days of roaming access";
"YL_Restoreroamingdays" = "Restore %1$s days of roaming data";
"YL_Afteryouopenamembers" = "1. After activating a membership, it will be automatically set to roaming for 180 days, and you can recover the cross-device chat records from the past 180 days.
2. Non-member users can only synchronize and recover chat records for the last 7 days.";
"YL_Theabovereferstothes" = "The above refers to the sound and vibration settings for receiving new messages when the app is in active state (excluding Chat).";
"YL_Inthechatpagetherear" = "In the Chat page, there are no sound or vibration alerts for new messages.";
"chat_detail_report_success" = "Report successful";
"chat_detail_report" = "Report";
"chat_detail_delete" = "Delete chat history";
"YL_GuidetoPreventingOnl" = "Online Fraud Prevention Guide";
"YL_Theotherpartyhasdele" = "The album has been deleted by your partner  and cannot be accessed.";
"YL_OurAlbum" = "Our Album";
"YL_Significantotherwrot" = "Your partner has wrote %1$s messages.";
"album_free_space" = "Free Space";
"YL_Thevideoistoolargepl" = "The video is too large, please reselect";

"congratulations_receive" = "Congratulations";
"task_claim" = "Claim";
"today" = "Today";
"yesterday" = "Yesterday";
"days_later" = "%1$sDays later";
"days_ago" = "%1$s Days ago";
"anniversary_together" = "Together for";
"anniversary_input_date" = "Click here to enter date";
"open_notification_content" = "To ensure a better experience, it is recommended that you enable notification permissions for Couple2.";
"love_list_operation" = "Operation successfully";
"love_list_quit_alarm" = "You have unsaved changes. Do you want to exit without saving?";
"back_location_tips" = "Please enable Couple2 running in the background to ensure continuous location sharing for your partner. Don't worry, it's battery-friendly!";
"no_more_tips" = "Don't Show Again";

"desktop_permission" = "Requires 'Desktop Shortcut' permission";
"desktop_permission_desc" = "To quickly add a widget, please grant 'Desktop Shortcut' permission in Settings.";
"desktop_permission_to" = "Proceed to authorization";
"them" = "Them";
"just_now" = "Just now";
"minutes_ago" = "minutes ago";
"match_has_unbind" = "has unpaired from you.";

"login_or_sign_up" = "Login or Sign Up";
"login_email_tips" = "Please enter your e-mail address";
"login_email_pwd_tips" = "Please enter your login password";
"login_emial_set_tips" = "Please set your login password";
"login_email_pwd" = "Login Password";
"login_email_pwd_limit" = "min. 8 characters";
"login_forget_pwd" = "Forgot your password";
"login" = "Login";
"login_email_receive_code" = "Receive verification code";
"login_verify" = "Verify";
"login_code_sent" = "Verification code has been sent";
"login_enter_code" = "Please enter the verification code received by %1$s.";
"login_resend_code" = "Re-send verification code";
"login_change_pwd" = "Change your password";
"login_new_pwd" = "New Password";
"login_enter_new_pwd" = "Please enter your new password";
"login_re_enter" = "Re-enter";
"login_re_enter_new_pwd" = "Please enter your password again";
"login_change_confirm" = "Confirm change";
"login_change_pwd_unpair" = "The two passwords do not match";
"login_change_pwd_success" = "Password changed successfully";

"free" = "free";
"cat_feed_not_hungry" = "%1$s is full now~Feeding at this time will not increase Satiety Value. Do you want to continue？";
"cat_feed_not_worth" = "Using %1$s now isn't the best choice—it will only increase Satiety Value by %2$s. Do you want to continue?";
"cat_mood_full" = "%1$s is in a pretty good mood~Playing with toys won't increase its Mood Value. Do you want to continue?";
"cat_toy_not_worth" = "Using %1$s now isn't the best choice—it will only increase Mood Value by %2$s. Do you want to continue?";
"cat_toy_over_times" = "%1$s is out of energy. Let's rest for a while before playing with toys again.";
"pop_form_change_text" = "Aha! I feel I'm much taller now!";
"pop_out_reward_text" = "I'm back! I have brought you some gifts!";
"get_props" = "Acquire props";
"pet_no_toy" = "No toys available at the moment";
"pet_no_food" = "No food available at the moment";
"go_buy" = "Buy";


"desc_pet_grow1" = "【%1$s】feeling itchy.";
"desc_pet_grow2" = "has grown up!";
"pet_click_to_view" = "Click to view";
"growth_diary" = "Growth Diary";
"buy_a_new" = "Buy a new one";
"pet_leave_tips" = "【%1$s】felt so unloved and sick for a long time, it had to run away.";
"get_back_it" = "Retrieve";
"pig_buy_coins_discount" = "Special Coin Deals";
"pig_can_get_coins" = "Claim coins";

"vip_unlock_all_benefits" = "Unlock all benefits";
"vip_next_time" = "purchase at original price later";
"vip_number_left" = "%1$s Left";

"pet_cat" = "Pet Cat";
"pet_new_cat" = "Raise a new cat";
"pet_new_cat_tips" = "Once you get a new cat, you won't be able to retrieve your previous one.";
"pet_buy_now" = "Purchase";
"pet_get_back" = "Retrieve your previous cat";
"pet_get_back_desc" = "Get your previous cat back and restore its previous values.";
"immediate_retrieval" = "Retrieve now";

"pet_what_happened" = "What happened?";
"store" = "Store";
"task_go_done" = "Go";
"task_remind_pair_login" = "The messenger has been sent to remind the other party, and they will be online soon.";
"pig_vip_claim" = "VIP claim";
"pig_no_vip_claim_num" = "Non-VIP claim %1$s";
"pig_claim_num" = "Claim %1$s";
"pig_open_vip_btn" = "Activate VIP to claim %1$s";

"report_push_title_location" = "Partner Location Reminder";
"report_push_title_battery" = "Partner Battery Reminder";
"report_push_title_network" = "Partner Network Reminder";
"report_push_title_track" = "Partner Track Reminder";
"ease_device_login_phone" = "View Records";
"ease_device_vip_login_history" = "Activate VIP to check location";
"ease_device_vip_low_battery" = "Activate VIP to check battery";
"ease_device_vip_wifi" = "Activate VIP to check WiFi";
"ease_device_vip" = "VIP required to view";
"ease_device_vip_phone_time" = "Activate VIP to view duration";
"ease_device_vip_phone_count" = "Activate VIP to see counts";
"ease_device_vip_loc_detail" = "Activate VIP to view details";
"report_open_location" = "Location Sharing Enabled";
"report_close_location" = "Location Sharing Disabled";
"report_open_realtime_location" = "Real-time Location Enabled";
"report_close_realtime_location" = "Real-time Location Disabled";
"report_open_app" = "Your partner opened Couple2.";
"report_logout" = "Partner's %1$s logged out here.";
"report_battery_low" = "Battery alert: %1$s.";
"report_battery_connected" = "Charging. Battery: %1$s";
"report_battery_disconnected" = "Charge end. Battery: %1$s";
"report_cellular" = "Using mobile data.";
"report_wifi" = "Connected to WiFi%1$s";
"report_open_login_record" = "Sensitive Records Access Activated";
"report_play_phone_continue_time" = "Phone usage: %1$s min.";
"report_play_phone_total_time" = "Total screen time is more than %1$s h.";
"report_play_phone_times" = "Phone picked up over %1$s times.";
"report_unlock" = "Partner's Screen unlocked.";
"report_arrive_location" = "Just arrived at %1$s.";
"report_leave_location" = "Just left %1$s.";
"report_stay_place_count" = "%1$s stopovers today.";
"report_stay_place_duration" = "Stayed at %1$s for %{ss} h.";
"report_stay_place_long_time" = "Long stayed at %1$s.";
"person_center_together_day" = "Together for %1$s days";
"love_info_pair_people" = "Couple2's %1$sth couple";
"pet_shower_limit" = "Up to  %1$s times a day";
"report_login" = "Partner's %1$s logged in here.";
"chat_extend_kiss_pair_here" = "%1$s is here";
"person_vip_sale" = "%1$s% Off";

//iOS特有通用占位
"YL_TookXXXstobeatXXofth" = "Took %1$ss, defeated%2$s% of cat owners";
"YL_Youhaveterminatedthe" = "You have ended your relationship on %1$s/%2$s/%3$s.";
"YL_OnlyXXday" = "Only %1$sday";
"YL_CompletedXX" = "Completed %1$s";
"YL_Usingthetoynowisntve" = "It's not the best time to use the %1$s now, as it will only increase the mood value by %2$s. Are you sure you want to use it?";
"YL_TogetherforXXDays" = "Together for %1$s days";

//iOS特有的key
"YL_Thisformatisnotsuppo" = "This format is not supported. Please upload files in MP3, AAC, WAV, or M4A format.";
"YL_SigninwithApple" = "Sign in with Apple";
"YL_VideoSaveFailedPlease" = "Video save failed. Please check photo library permissions.";
"YL_VideoSavedToPhotos" = "Video saved to Photos.";
"YL_TopList" = "Top List";
"YL_Help" = "Help";
"YL_OceanofLove" = "Ocean of Love";
"YL_Romance" = "Romance";
"YL_RomanticHoliday" = "Romantic Holiday";
"YL_BeautifulDream" = "Beautiful Dream";
"YL_YouandMe" = "You and Me";
"YL_HeartofSpain" = "Heart of Spain";
"YL_MelodiousJazz" = "Melodious Jazz";
"YL_HappyFestival" = "Happy Festival";
"YL_Pleaseenteryourpetsn" = "Please enter your pet's nickname.";
"YL_Failedtorecallpet" = "Failed to recall pet.";
"YL_Watchads" = "Watch Ads";
"YL_Owneryouvelearnedhow" = "Owner, you've learned how to pet me, remember to visit often~";
"YL_Musicbox" = "Music Box";
"YL_Followthegesturetope" = "Follow the gesture to pet the cat, try to reach the comfort zone.";
"YL_HeyyouknowwhatIvebee" = "Hey, you know what? I've been realizing lately that you're truly a super awesome person!";
"YL_Icantdescribethefeel" = "I can't describe the feeling when you gently stroke me—it's like my fur is dancing with joy.";
"YL_Ifeellikethehappiest" = "Every time you gently rub my tummy, I feel like the happiest kitty in the world.";
"YL_Nowmyfavoritismtowar" = "Now, my affection for you has leveled up with a cheerful 'Meow'! Meow~ Meow~";
"YL_Meowoooh" = "Meooow......";
"YL_Mybodybecamesoheavyt" = "My body felt so heavy, it seems like every step was an immense struggle.";
"YL_Icantseemtomakethest" = "I don't have the energy to play.";
"YL_ItwouldbeniceifIcoul" = "It would be nice if I could get better soon and play with you again.";
"YL_Pleasetakecareofyour" = "Please take care of yourself and stay happy, even when I'm gone.";
"YL_RightnowIjustwanttol" = "Right now, all I want is to rest quietly.";
"YL_Thenoseisalsooutofbr" = "I got nasal congestion and trouble breathing.";
"YL_Itfeltlikemybodywass" = "My body felt so heavy, as if something was pressing on me, making it so hard to move.";
"YL_Mystomachisstartingt" = "My stomach is growling, I'm so hungry!";
"YL_ImhungryComeandfeedm" = "I'm starving. Come and feed me!";
"YL_Aimmeow" = "Meow~Meow~!";
"YL_WheresthefoodWherest" = "Where is the food? Stroking won't satisfy my hunger.";
"YL_Todaysmooditseemstob" = "Today's mood feels all cloudy and gray... No wonder my little paws can't find any happiness.";
"YL_Hopefullytomorrowmym" = "I hope tomorrow I'll be as cheerful as the sunshine outside. Meow...";
"YL_Givethismeowsometoys" = "Give me some toys to swat away this dark, gloomy cloud.";
"YL_MoodyMeow" = "Depressed Kitty...";
"YL_Enablestepcountshari" = "Share your step count to improve location accuracy.";
"YL_Setup" = "Setup";
"YL_StarttheCoupleJourne" = "Start the Couple2 Journey";
"YL_SensitiveRecordsAcced" = "Sensitive Records Access Deactivated";
"YL_MotionFitnessAccessE" = "Motion & Fitness Access Enabled";
"YL_MotionFitnessAccessD" = "Motion & Fitness Access Disabled";
"YL_Outfitfailedpleasetr" = "Outfit failed, please try again";
"YL_Outfitsuccessful" = "Outfit successful";
"YL_Insufficientbalance" = "Insufficient balance.";
"YL_Rewardalreadyclaimed" = "Reward already claimed.";
"YL_Dailyviewinglimitrea" = "Daily viewing limit reached.";
"YL_Trip" = "Trip";
"YL_Shower" = "Bath";
"YL_Catpetting" = "Rub";
"YL_sync" = "sync";
"YL_Incorrectaccountorpa" = "Incorrect account or password, please log in again~";
"hours_ago" = "%1$s h ago";
"YL_Theotherpersonhasnte" = "The other person hasn't enabled real-time location.";
"YL_soupdatesmaybedelaye" = "Location updates may not be instant.";
"YL_Unabletoretrievetheo" = "Unable to retrieve the other person's location at the moment.";
"YL_Theotherpartymaynoth" = "The other person may not have updated Use location.";
"YL_Theotherpartyhasnote" = "The other person has not enabled location services";
"YL_Theinputtextcannotex" = "Enter text not exceeding 20 characters.";
"YL_Thealbumdoesnotexist" = "The album does not exist";
"YL_Pleaseselectthephoto" = "Please select the photos you want to delete";
"YL_Youarealreadyamember" = "You are already a VIP member and cannot try it out";

"phone_use_intro_total_title" = "What is \"Screen Time\"?";
"phone_use_intro_total_desc"  = "It refers to the total time you spend on your phone each day.\n\nUsage is categorized based on your daily total:";
"phone_use_intro_total_grade_good" = "Great";
"phone_use_intro_total_grade_ok"   = "Okay";
"phone_use_intro_total_grade_bad"  = "Oops";
"phone_use_intro_total_range_good" = "<3h";
"phone_use_intro_total_range_ok"   = "3–6h";
"phone_use_intro_total_range_bad"  = ">6h";
"phone_use_intro_total_tip"  = "We recommend using your phone in moderation. Excessive use may affect your physical and mental health. Let's take care of ourselves and each other with Couple2.";

"phone_use_intro_cont_title" = "What is \"Longest Session\"?";
"phone_use_intro_cont_desc"  = "It refers to the longest uninterrupted phone use within a single day.\n\nUsage is categorized based on your daily total:";
"phone_use_intro_cont_grade_good" = "Great";
"phone_use_intro_cont_grade_ok"   = "Okay";
"phone_use_intro_cont_grade_bad"  = "Oops";
"phone_use_intro_cont_range_good" = "≤15min";
"phone_use_intro_cont_range_ok"   = "15–30min";
"phone_use_intro_cont_range_bad"  = ">30min";
"phone_use_intro_cont_tip"  = "We recommend using your phone in moderation. Excessive use may affect your physical and mental health. Let's take care of ourselves and each other with Couple2.";
"more" = "More";
"use_phone_use_duration_tips" = "What is Screen Time?";
"use_phone_once_screen_duration_tips" = "What is Longest Session?";
"use_phone_use_duration_singletips" = "Screen time";
"use_phone_history_data"            = "History";
"use_phone_use_duration_times"      = "Times used";
"use_phone_history_no_data_today"   = "No data for this day~";

/* Weekdays */
"weekday_mon" = "Mon.";
"weekday_tue" = "Tue.";
"weekday_wed" = "Wed.";
"weekday_thu" = "Thu.";
"weekday_fri" = "Fri.";
"weekday_sat" = "Sat.";
"weekday_sun" = "Sun.";
