//
//  YLQCloudApi.swift
//  Distance
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/1.
//

import Foundation
import Moya
import QCloudCOSXML
import Photos

let qCloudApi = MoyaProvider<YLQCloudApi>(session: yl_session, plugins: [YLSignPlugin(), AccessTokenPlugin { _ in UserData.shared.loginToken}])

enum YLQCloudApi {
	
	static var uploadManger: YLQCloudUploadManager = YLQCloudUploadManager()
	
	case getToken
	//获取七牛低频存储token
	case getTokenLowFrequency
	
	case checkImage(url: String)
}

extension YLQCloudApi: TargetType, AccessTokenAuthorizable {
	
	var authorizationType: AuthorizationType? { .bearer }
	
	var baseURL: URL {
		URL(string: YLTestEnviromentManager.shared.baseUrl)!
	}
	
	var path: String {
		switch self {
		case .getToken:
			return "/api/qiniu"
		case .getTokenLowFrequency:
			return "/api/qiniu_lf"
		case .checkImage:
			return "/user/checkImage"
		}
	}
	
	var method: Moya.Method {
		.get
	}
	
	var sampleData: Data {
		Data()
	}
	
	var task: Task {
		var parameter = YLPublicParameter.publicParameter()
		switch self {
		case .getToken, .getTokenLowFrequency:
			break
		case let .checkImage(url):
			parameter["image"] = url
		}
		return .requestParameters(parameters: parameter, encoding: URLEncoding.queryString)
	}
	
	var headers: [String : String]? {
		nil
	}
}

class YLQCloudUploadManager: NSObject {
	
	static let secretID = "AKIDkeejlgQGVbXHrKpBNOErGUWdt57xd7PV"
	static let secretKey = "xiJyYyFNJ4f8OWTHvSp9KYheZLchaKNK"
	static let appID = "1254058474"
	static let bucket = "couple2-com-1254058474"
	static let regionName = "ap-singapore"
	
	func put(_ imageData: Data,
			 fileName: String? = nil,
			 key: String,
			 token: String,
			 expiredTime: TimeInterval? = nil,
			 startTime: TimeInterval? = nil,
			 complete completionHandler: @escaping (_ info: QCloudUploadObjectResult?, _ key: String?, _ resp: [AnyHashable: Any]?) -> Void?) {
		putBaseAction(imageData: imageData, fileName: fileName, key: key, token: token, expiredTime: expiredTime, startTime: startTime, complete: completionHandler)
	}
	
	func put(asset: PHAsset,
			 key: String,
			 token: String,
			 expiredTime: TimeInterval? = nil,
			 startTime: TimeInterval? = nil,
			 complete completionHandler: @escaping (_ info: QCloudUploadObjectResult?, _ key: String?, _ resp: [AnyHashable: Any]?) -> Void?) {
		do {
			let assetFile = try YLPHAssetFile(phAsset: asset)
			putBaseAction(imageData: assetFile.assetData, key: key, token: token, expiredTime: expiredTime, startTime: startTime, complete: completionHandler)
		} catch {
			completionHandler(nil, nil, nil)
		}
		
	}
	
	func putFile(_ filePath: String,
				 key: String,
				 token: String,
				 expiredTime: TimeInterval? = nil,
				 startTime: TimeInterval? = nil,
				 complete completionHandler: @escaping (_ info: QCloudUploadObjectResult?, _ key: String?, _ resp: [AnyHashable: Any]?) -> Void?) {
		putBaseAction(filePath: filePath, key: key, token: token, expiredTime: expiredTime, startTime: startTime, complete: completionHandler)
	}
	
	func putBaseAction(filePath: String? = nil,
					   imageData: Data? = nil,
					   fileName: String? = nil,
					   key: String,
					   token: String,
					   expiredTime: TimeInterval? = nil,
					   startTime: TimeInterval? = nil,
					   complete completionHandler: @escaping (_ info: QCloudUploadObjectResult?, _ key: String?, _ resp: [AnyHashable: Any]?) -> Void?) {
		
		// 设置可复用临时密钥
		let credential = QCloudCredential()
		credential.secretID = YLQCloudUploadManager.secretID
		credential.secretKey = YLQCloudUploadManager.secretKey
		credential.token = token

		if let expiredTime = expiredTime, let startTime = startTime {
			credential.expirationDate = Date(timeIntervalSince1970: expiredTime)
			credential.startDate = Date(timeIntervalSince1970: startTime)
		}

		// 创建上传请求
		let put = QCloudCOSXMLUploadObjectRequest<AnyObject>()
		put.credential = credential
        put.bucket = YLQCloudUploadManager.bucket
        put.regionName = YLQCloudUploadManager.regionName
		put.object = key
		if let filePath = filePath {
			let fileURL = URL(fileURLWithPath: filePath)
			put.body = fileURL as AnyObject
		} else if let imageData = imageData {
			put.body = imageData as AnyObject
		} else {
			completionHandler(nil, nil, nil)
			return
		}

		// 上传进度
        put.sendProcessBlock = { bytesSent, totalBytesSent, totalBytesExpectedToSend in
			DDLogInfo("上传进度: \(totalBytesSent)/\(totalBytesExpectedToSend)")
		}

		// 上传完成
		put.setFinish { result, error in
			if let result = result {
				let location = result.location
				DDLogInfo("上传成功，文件地址: \(location)")

				if let headers = result.__originHTTPURLResponse__?.allHeaderFields,
				   let crc64 = headers["x-cos-hash-crc64ecma"] as? String {
					DDLogInfo("文件 CRC64: \(crc64)")
				}

                completionHandler(result, location, nil)
			} else {
				DDLogInfo("上传失败: \(error?.localizedDescription ?? "未知错误")")
				completionHandler(nil, nil, nil)
			}
		}
		
		// 发起上传请求
		QCloudCOSTransferMangerService.defaultCOSTransferManager().uploadObject(put)
	}
	
}

class YLPHAssetFile {
	
	let phAsset: PHAsset
	var fileSize: Int64 = 0
	var fileModifyTime: Int64 = 0
	var assetData: Data?
	var hasRealFilePath = false
	var filePath: String?
	private var file: FileHandle?
	private let lock = NSLock()
	
	init(phAsset: PHAsset) throws {
		self.phAsset = phAsset
		if let createTime = phAsset.creationDate {
			fileModifyTime = Int64(createTime.timeIntervalSince1970)
		}
		getInfo()
		
		if assetData == nil, let path = filePath {
			let fileAttr = try FileManager.default.attributesOfItem(atPath: path)
			fileSize = (fileAttr[.size] as? NSNumber)?.int64Value ?? 0
			
			if fileSize > 16 * 1024 * 1024 {
				file = try FileHandle(forReadingFrom: URL(fileURLWithPath: path))
			} else {
				assetData = try Data(contentsOf: URL(fileURLWithPath: path), options: .mappedIfSafe)
			}
		}
	}
	
	func read(offset: Int64, size: Int, error: inout NSError?) -> Data {
		var data = Data()
		lock.lock()
		defer { lock.unlock() }
		
		do {
			if let assetData = assetData, offset < assetData.count {
				let realSize = min(size, assetData.count - Int(offset))
				data = assetData.subdata(in: Int(offset)..<Int(offset) + realSize)
			} else if let file = file, offset < fileSize {
				try file.seek(toOffset: UInt64(offset))
				if #available(iOS 13.4, *) {
					data = try file.read(upToCount: size) ?? Data()
				} else {
					data = file.readData(ofLength: size)
				}
			}
		} catch {
			print("read file failed: \(error.localizedDescription)")
		}
		return data
	}
	
	func readAll() throws -> Data {
		var error: NSError? = nil
		let data = read(offset: 0, size: Int(fileSize), error: &error)
		if let error = error {
			throw error
		}
		return data
	}

	
	func close() {
		if phAsset.mediaType == .video {
			try? file?.close()
			if !hasRealFilePath, let filePath = filePath {
				try? FileManager.default.removeItem(atPath: filePath)
			}
		}
	}
	
	func path() -> String? {
		return hasRealFilePath ? filePath : nil
	}
	
	func modifyTime() -> Int64 {
		return fileModifyTime
	}
	
	func size() -> Int64 {
		return fileSize
	}
	
	func fileType() -> String {
		return "PHAsset"
	}
	
	private func getInfo() {
		switch phAsset.mediaType {
		case .image:
			getImageInfo()
		case .video:
			if filePath == nil {
				exportAsset()
			}
		default:
			exportAsset()
		}
	}
	
	private func getImageInfo() {
		let options = PHImageRequestOptions()
		options.version = .current
		options.deliveryMode = .highQualityFormat
		options.resizeMode = .none
		options.isNetworkAccessAllowed = false
		options.isSynchronous = true
		
		if #available(iOS 13.0, *) {
			PHImageManager.default().requestImageDataAndOrientation(for: phAsset, options: options) { data, _, _, _ in
				self.assetData = data
				self.fileSize = Int64(data?.count ?? 0)
				self.hasRealFilePath = false
			}
		} else {
			PHImageManager.default().requestImageData(for: phAsset, options: options) { data, _, _, _ in
				self.assetData = data
				self.fileSize = Int64(data?.count ?? 0)
				self.hasRealFilePath = false
			}
		}
	}
	
	private func exportAsset() {
		let resources = PHAssetResource.assetResources(for: phAsset)
		guard let resource = resources.first(where: {
			$0.type == .pairedVideo || $0.type == .video
		}) else {
			return
		}
		
		let fileName = String(format: "tempAsset-%.0f-%d.mov", Date().timeIntervalSince1970, arc4random() % 100000)
		let tempPath = NSTemporaryDirectory().appending(fileName)
		let tempURL = URL(fileURLWithPath: tempPath)
		
		try? FileManager.default.removeItem(at: tempURL)
		
		let options = PHAssetResourceRequestOptions()
		options.isNetworkAccessAllowed = false
		
		let semaphore = DispatchSemaphore(value: 0)
		
		PHAssetResourceManager.default().writeData(for: resource, toFile: tempURL, options: options) { error in
			if error != nil {
				self.filePath = nil
			} else {
				self.filePath = tempPath
			}
			self.hasRealFilePath = false
			semaphore.signal()
		}
		
		semaphore.wait()
	}
}
