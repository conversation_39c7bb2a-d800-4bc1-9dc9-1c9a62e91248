<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="YLAccountRelationshipViewController" customModule="Distance" customModuleProvider="target">
            <connections>
                <outlet property="view" destination="Tcb-t6-700" id="Som-bp-OBw"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="Tcb-t6-700">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="loveInfoBg" translatesAutoresizingMaskIntoConstraints="NO" id="Cwg-9U-INU">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                </imageView>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3tX-wW-0xe">
                    <rect key="frame" x="0.0" y="59" width="393" height="823"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="eWJ-7I-Lb0">
                            <rect key="frame" x="0.0" y="8" width="393" height="148.66666666666666"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1t3-5V-E84" customClass="UIControl">
                                    <rect key="frame" x="0.0" y="0.0" width="393" height="72.333333333333329"/>
                                    <subviews>
                                        <view alpha="0.20000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nz6-jY-E1t">
                                            <rect key="frame" x="15" y="72" width="363" height="0.3333333333333286"/>
                                            <color key="backgroundColor" red="0.18186587100000001" green="0.1774442792" blue="0.2068564594" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="0.5" id="0v1-hp-vtj"/>
                                            </constraints>
                                        </view>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_unpair" translatesAutoresizingMaskIntoConstraints="NO" id="PF7-Hv-168">
                                            <rect key="frame" x="15" y="12" width="22" height="20"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="解除匹配" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZZB-nB-LaM">
                                            <rect key="frame" x="48" y="13.666666666666671" width="55.666666666666657" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <color key="highlightedColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_center_unbind"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="back_icon" translatesAutoresizingMaskIntoConstraints="NO" id="QAL-DI-lZM">
                                            <rect key="frame" x="370" y="29.333333333333329" width="8" height="14"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="8" id="6nn-Pz-7oW"/>
                                                <constraint firstAttribute="height" constant="14" id="bRi-8j-eke"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="解除你们的关系，你将回到未配对状态。" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xkl-2B-hG8">
                                            <rect key="frame" x="15" y="44.666666666666671" width="328" height="15.666666666666664"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <color key="textColor" red="0.18186587100000001" green="0.1774442792" blue="0.2068564594" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_unmatch_tips"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" red="0.99975711109999998" green="0.98867398500000003" blue="0.96470302340000003" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="Xkl-2B-hG8" secondAttribute="trailing" constant="50" id="1Eq-at-w1n"/>
                                        <constraint firstAttribute="trailing" secondItem="QAL-DI-lZM" secondAttribute="trailing" constant="15" id="2dU-Su-kvH"/>
                                        <constraint firstItem="PF7-Hv-168" firstAttribute="leading" secondItem="1t3-5V-E84" secondAttribute="leading" constant="15" id="3kM-BF-xfZ"/>
                                        <constraint firstItem="ZZB-nB-LaM" firstAttribute="leading" secondItem="PF7-Hv-168" secondAttribute="trailing" constant="11" id="6yo-n4-Og9"/>
                                        <constraint firstItem="PF7-Hv-168" firstAttribute="top" secondItem="1t3-5V-E84" secondAttribute="top" constant="12" id="Epw-Ek-VmG"/>
                                        <constraint firstItem="nz6-jY-E1t" firstAttribute="leading" secondItem="1t3-5V-E84" secondAttribute="leading" constant="15" id="Fh5-tW-SQi"/>
                                        <constraint firstAttribute="bottom" secondItem="Xkl-2B-hG8" secondAttribute="bottom" constant="12" id="NQZ-ft-UBA"/>
                                        <constraint firstAttribute="trailing" secondItem="nz6-jY-E1t" secondAttribute="trailing" constant="15" id="Ni1-r6-nHa"/>
                                        <constraint firstItem="Xkl-2B-hG8" firstAttribute="leading" secondItem="1t3-5V-E84" secondAttribute="leading" constant="15" id="UeC-Kf-P6A"/>
                                        <constraint firstItem="PF7-Hv-168" firstAttribute="leading" secondItem="1t3-5V-E84" secondAttribute="leading" constant="15" id="W5F-Zh-PSx"/>
                                        <constraint firstItem="QAL-DI-lZM" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="ZZB-nB-LaM" secondAttribute="trailing" constant="8" symbolic="YES" id="apB-gU-bdD"/>
                                        <constraint firstItem="ZZB-nB-LaM" firstAttribute="centerY" secondItem="PF7-Hv-168" secondAttribute="centerY" id="dlB-0u-ndG"/>
                                        <constraint firstItem="QAL-DI-lZM" firstAttribute="centerY" secondItem="1t3-5V-E84" secondAttribute="centerY" id="lDT-z8-IcC"/>
                                        <constraint firstAttribute="bottom" secondItem="nz6-jY-E1t" secondAttribute="bottom" id="nXN-eW-YGH"/>
                                        <constraint firstItem="Xkl-2B-hG8" firstAttribute="top" secondItem="ZZB-nB-LaM" secondAttribute="bottom" constant="14" id="vKK-pW-sK4"/>
                                    </constraints>
                                    <connections>
                                        <action selector="unmatchTap:" destination="-1" eventType="touchUpInside" id="mfE-Xc-nDY"/>
                                    </connections>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ufz-dK-KZr" customClass="UIControl">
                                    <rect key="frame" x="0.0" y="72.333333333333343" width="393" height="76.333333333333343"/>
                                    <subviews>
                                        <view alpha="0.20000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WIn-9O-DZN">
                                            <rect key="frame" x="15" y="76" width="363" height="0.3333333333333286"/>
                                            <color key="backgroundColor" red="0.18186587100000001" green="0.1774442792" blue="0.2068564594" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="0.5" id="8gB-cy-xcG"/>
                                            </constraints>
                                        </view>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="sz_new_zhzz_icon" translatesAutoresizingMaskIntoConstraints="NO" id="FD4-RD-POU">
                                            <rect key="frame" x="15" y="12" width="22" height="22"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="注销账号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="986-ts-rVk">
                                            <rect key="frame" x="48" y="14.666666666666657" width="55.666666666666657" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <color key="highlightedColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="account_log_off"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="back_icon" translatesAutoresizingMaskIntoConstraints="NO" id="itp-nr-a24">
                                            <rect key="frame" x="370" y="31.***************" width="8" height="14"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="8" id="A2h-Fh-9IG"/>
                                                <constraint firstAttribute="height" constant="14" id="rvy-Ww-Z2A"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="你将不再使用我们的服务，并永久删除账户信息。" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RtS-8a-nuI">
                                            <rect key="frame" x="15" y="45.666666666666657" width="328" height="18.666666666666671"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <color key="textColor" red="0.18186587100000001" green="0.1774442792" blue="0.2068564594" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_logout_tips"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" red="0.99975711109999998" green="0.98867398500000003" blue="0.96470302340000003" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstItem="itp-nr-a24" firstAttribute="centerY" secondItem="Ufz-dK-KZr" secondAttribute="centerY" id="2L6-nh-jf6"/>
                                        <constraint firstAttribute="bottom" secondItem="WIn-9O-DZN" secondAttribute="bottom" id="GEY-a9-RNt"/>
                                        <constraint firstItem="FD4-RD-POU" firstAttribute="top" secondItem="Ufz-dK-KZr" secondAttribute="top" constant="12" id="HAJ-ee-da7"/>
                                        <constraint firstItem="WIn-9O-DZN" firstAttribute="leading" secondItem="Ufz-dK-KZr" secondAttribute="leading" constant="15" id="Hu1-Z4-IYS"/>
                                        <constraint firstItem="RtS-8a-nuI" firstAttribute="top" secondItem="986-ts-rVk" secondAttribute="bottom" constant="14" id="JEG-Lv-8Pc"/>
                                        <constraint firstAttribute="trailing" secondItem="RtS-8a-nuI" secondAttribute="trailing" constant="50" id="KfV-X6-fhi"/>
                                        <constraint firstAttribute="bottom" secondItem="RtS-8a-nuI" secondAttribute="bottom" constant="12" id="LkS-u4-Wvl"/>
                                        <constraint firstItem="986-ts-rVk" firstAttribute="leading" secondItem="FD4-RD-POU" secondAttribute="trailing" constant="11" id="OJi-kh-d0C"/>
                                        <constraint firstAttribute="trailing" secondItem="WIn-9O-DZN" secondAttribute="trailing" constant="15" id="Rsl-gW-jWw"/>
                                        <constraint firstItem="FD4-RD-POU" firstAttribute="leading" secondItem="Ufz-dK-KZr" secondAttribute="leading" constant="15" id="YbC-S1-lxZ"/>
                                        <constraint firstAttribute="trailing" secondItem="itp-nr-a24" secondAttribute="trailing" constant="15" id="ZSp-3I-Ett"/>
                                        <constraint firstItem="986-ts-rVk" firstAttribute="centerY" secondItem="FD4-RD-POU" secondAttribute="centerY" id="boX-kw-FoP"/>
                                        <constraint firstItem="RtS-8a-nuI" firstAttribute="leading" secondItem="Ufz-dK-KZr" secondAttribute="leading" constant="15" id="ldk-pt-nRU"/>
                                        <constraint firstItem="itp-nr-a24" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="986-ts-rVk" secondAttribute="trailing" constant="8" symbolic="YES" id="mak-nc-kir"/>
                                    </constraints>
                                    <connections>
                                        <action selector="cancelaccountTap:" destination="-1" eventType="touchUpInside" id="7QH-VH-U4k"/>
                                    </connections>
                                </view>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="eWJ-7I-Lb0" secondAttribute="trailing" id="O56-rj-aY1"/>
                        <constraint firstItem="eWJ-7I-Lb0" firstAttribute="leading" secondItem="3tX-wW-0xe" secondAttribute="leading" id="Zs8-8R-1ah"/>
                        <constraint firstItem="eWJ-7I-Lb0" firstAttribute="top" secondItem="3tX-wW-0xe" secondAttribute="top" constant="8" id="cOq-KW-QCt"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="16"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GOA-7F-oUO">
                    <rect key="frame" x="66.666666666666686" y="728" width="260" height="50"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="HhW-dn-SwA"/>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="260" id="fcZ-FV-y6d"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                    <state key="normal" title="log out" backgroundImage="rl_tc_qbj_icon">
                        <color key="titleColor" red="0.4941176471" green="0.25882352939999997" blue="0.1137254902" alpha="1" colorSpace="calibratedRGB"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="string" keyPath="titleLocalizeKey" value="log_out"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="loginOutAction:" destination="-1" eventType="touchUpInside" id="Hbd-QC-F0d"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="PuO-sq-zJ3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="Cwg-9U-INU" secondAttribute="bottom" id="6DI-CV-DGq"/>
                <constraint firstItem="Cwg-9U-INU" firstAttribute="top" secondItem="Tcb-t6-700" secondAttribute="top" id="8sO-l9-ngN"/>
                <constraint firstItem="Cwg-9U-INU" firstAttribute="trailing" secondItem="PuO-sq-zJ3" secondAttribute="trailing" id="HFU-pA-5Rz"/>
                <constraint firstItem="GOA-7F-oUO" firstAttribute="centerX" secondItem="Tcb-t6-700" secondAttribute="centerX" id="Rhc-La-Plo"/>
                <constraint firstAttribute="bottom" secondItem="3tX-wW-0xe" secondAttribute="bottom" constant="-30" id="Sh9-TQ-cBK"/>
                <constraint firstItem="Cwg-9U-INU" firstAttribute="leading" secondItem="PuO-sq-zJ3" secondAttribute="leading" id="V6W-Qn-s0n"/>
                <constraint firstItem="PuO-sq-zJ3" firstAttribute="bottom" secondItem="GOA-7F-oUO" secondAttribute="bottom" constant="40" id="Vpl-Ve-BPX"/>
                <constraint firstItem="3tX-wW-0xe" firstAttribute="leading" secondItem="PuO-sq-zJ3" secondAttribute="leading" id="abN-ym-jaE"/>
                <constraint firstItem="3tX-wW-0xe" firstAttribute="trailing" secondItem="PuO-sq-zJ3" secondAttribute="trailing" id="f1v-xs-3Cv"/>
                <constraint firstItem="3tX-wW-0xe" firstAttribute="top" secondItem="PuO-sq-zJ3" secondAttribute="top" id="mPO-aT-TT0"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="back_icon" width="8" height="15"/>
        <image name="loveInfoBg" width="375" height="812"/>
        <image name="person_unpair" width="22" height="20"/>
        <image name="rl_tc_qbj_icon" width="50.333332061767578" height="50"/>
        <image name="sz_new_zhzz_icon" width="22" height="22"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
