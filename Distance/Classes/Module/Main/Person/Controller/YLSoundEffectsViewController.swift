//
//  YLSoundEffectsViewController.swift
//  Distance
//
//  Created by Q on 2025/7/18.
//

import UIKit
import URLNavigator

class YLSoundEffectsViewController: DTBaseViewController, CustomNavigation {
	var customNavStyle: NavStyle = .clear
	
	@IBOutlet weak var musicSwitch: UISwitch!
	override func viewDidLoad() {
        super.viewDidLoad()

		title = "person_sounds".localized
		musicSwitch.isOn = YLChatAccessoryView.shared.isOn
		musicSwitch.transform = CGAffineTransformMakeScale(0.8, 0.8)
    }
	
	@IBAction func switchChangeAction(_ sender: UISwitch) {
		YLSoundEffectCenter.soundEffectIsToggleOn = sender.isOn
		YLStatisticsHelper.trackEvent("Set.CK", dic: ["pt":"按钮音效"])
		YLStatisticsHelper.trackEvent("Setting.Sound.CK", dic: ["isOn": sender.isOn])
	}
	
	@IBAction func bgMusicTap(_ sender: Any) {
		YLStatisticsHelper.trackEvent("Set.CK", dic: ["pt":"背景音乐"])
		navigationController?.pushViewController(YLPlayViewController(), animated: true)
	}
	
}
