<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="YLNewPersonViewController" customModule="Distance" customModuleProvider="target">
            <connections>
                <outlet property="barrarBGView" destination="jAq-0u-9YM" id="aUy-ch-CN0"/>
                <outlet property="daysLab" destination="JPA-ez-BJb" id="lyd-Az-HVr"/>
                <outlet property="leftStarImageView" destination="kLh-3J-UBg" id="7pr-St-omB"/>
                <outlet property="meIconBGView" destination="5C7-y4-xNe" id="zpp-nb-sf3"/>
                <outlet property="meIconImageView" destination="2X5-m5-ewF" id="kXr-gh-2TY"/>
                <outlet property="meNameLab" destination="Ysu-37-D2U" id="WwU-6U-qS5"/>
                <outlet property="rightStarImageView" destination="M9U-y8-87j" id="KyJ-kc-wDB"/>
                <outlet property="saleLab" destination="ec4-kD-BI8" id="xY6-Q6-C1j"/>
                <outlet property="taIconBGView" destination="lO2-cF-EI3" id="PXf-n3-CKr"/>
                <outlet property="taIconImageView" destination="PsE-8Z-o2I" id="Tpu-wU-RiZ"/>
                <outlet property="taNameLab" destination="dEc-Q4-pFP" id="9al-xQ-50z"/>
                <outlet property="topbqBGView" destination="ckC-Xc-1Ox" id="n0c-qa-Iga"/>
                <outlet property="topbqLab" destination="QNj-gN-FYp" id="M3M-Wh-i5c"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outlet property="vipButton" destination="qjg-9j-sAi" id="fAq-3b-aAS"/>
                <outlet property="vipSmLab" destination="amk-Dh-jvt" id="laT-tP-Whe"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="image_new_person" translatesAutoresizingMaskIntoConstraints="NO" id="xif-Ow-cQr">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                </imageView>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yQd-WE-hs1">
                    <rect key="frame" x="0.0" y="59" width="393" height="793"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KBZ-ho-kaL">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="867.33333333333337"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Os1-RU-LbG" customClass="UIControl">
                                    <rect key="frame" x="0.0" y="0.0" width="377" height="142.66666666666666"/>
                                    <subviews>
                                        <stackView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" alignment="bottom" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="8XG-dG-kHa">
                                            <rect key="frame" x="144.66666666666666" y="0.0" width="87.666666666666657" height="28.666666666666668"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1314" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JPA-ez-BJb">
                                                    <rect key="frame" x="0.0" y="0.0" width="52.333333333333336" height="28.666666666666668"/>
                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="24"/>
                                                    <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="days" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="r9N-T8-VPC">
                                                    <rect key="frame" x="57.33333333333335" y="4.6666666666666643" width="30.333333333333336" height="24"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="sBQ-R9-aMd"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="days"/>
                                                    </userDefinedRuntimeAttributes>
                                                </label>
                                            </subviews>
                                        </stackView>
                                        <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BzH-ht-cTd" customClass="SVGAImageView">
                                            <rect key="frame" x="106.66666666666669" y="39" width="164" height="70"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="70" id="H46-sa-ev5"/>
                                                <constraint firstAttribute="width" constant="164" id="tbm-FH-MqM"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="imageName" value="桃心动效"/>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5C7-y4-xNe">
                                            <rect key="frame" x="45" y="31" width="68" height="68"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="2X5-m5-ewF">
                                                    <rect key="frame" x="4" y="4" width="60" height="60"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="60" id="413-Uw-L3b"/>
                                                        <constraint firstAttribute="width" constant="60" id="cPc-Xo-TdW"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="30"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                                    </userDefinedRuntimeAttributes>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="68" id="Cvp-UN-ORP"/>
                                                <constraint firstAttribute="height" constant="68" id="IuH-hd-6m4"/>
                                                <constraint firstItem="2X5-m5-ewF" firstAttribute="centerX" secondItem="5C7-y4-xNe" secondAttribute="centerX" id="MoV-rK-sQ4"/>
                                                <constraint firstItem="2X5-m5-ewF" firstAttribute="centerY" secondItem="5C7-y4-xNe" secondAttribute="centerY" id="cuA-oO-k62"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="32"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="meIconTap:" destination="-1" eventType="touchUpInside" id="h4j-Hk-zVh"/>
                                            </connections>
                                        </view>
                                        <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lO2-cF-EI3">
                                            <rect key="frame" x="264" y="31" width="68" height="68"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="PsE-8Z-o2I">
                                                    <rect key="frame" x="4" y="4" width="60" height="60"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="60" id="Jyu-sb-Ipj"/>
                                                        <constraint firstAttribute="height" constant="60" id="a6A-12-1vw"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="30"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                                    </userDefinedRuntimeAttributes>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                            <constraints>
                                                <constraint firstItem="PsE-8Z-o2I" firstAttribute="centerX" secondItem="lO2-cF-EI3" secondAttribute="centerX" id="Edi-zn-fsL"/>
                                                <constraint firstAttribute="width" constant="68" id="RIn-sR-jyD"/>
                                                <constraint firstItem="PsE-8Z-o2I" firstAttribute="centerY" secondItem="lO2-cF-EI3" secondAttribute="centerY" id="pxj-kg-yWW"/>
                                                <constraint firstAttribute="height" constant="68" id="wqL-rQ-Ums"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="32"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="taIconTap:" destination="-1" eventType="touchUpInside" id="u35-a5-1K3"/>
                                            </connections>
                                        </view>
                                        <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fOg-Pl-Eop">
                                            <rect key="frame" x="188" y="-17.333333333333336" width="1" height="80"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="80" id="5aM-Ch-3IF"/>
                                                <constraint firstAttribute="width" constant="1" id="Z6z-0E-fnQ"/>
                                            </constraints>
                                        </view>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="布拉德皮特" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ysu-37-D2U">
                                            <rect key="frame" x="46.666666666666664" y="107" width="64.***************" height="15.666666666666671"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="安吉丽娜朱莉" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dEc-Q4-pFP">
                                            <rect key="frame" x="259.33333333333331" y="107" width="77.***************" height="15.666666666666671"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="fOg-Pl-Eop" firstAttribute="leading" secondItem="5C7-y4-xNe" secondAttribute="trailing" constant="75" id="1l1-YW-JiB"/>
                                        <constraint firstItem="fOg-Pl-Eop" firstAttribute="centerX" secondItem="Os1-RU-LbG" secondAttribute="centerX" id="AFA-51-keU"/>
                                        <constraint firstItem="8XG-dG-kHa" firstAttribute="top" secondItem="Os1-RU-LbG" secondAttribute="top" id="MCE-1I-Xy8"/>
                                        <constraint firstItem="Ysu-37-D2U" firstAttribute="top" secondItem="5C7-y4-xNe" secondAttribute="bottom" constant="8" id="SLV-dc-UOF"/>
                                        <constraint firstItem="dEc-Q4-pFP" firstAttribute="top" secondItem="lO2-cF-EI3" secondAttribute="bottom" constant="8" id="Skn-YG-q6G"/>
                                        <constraint firstAttribute="bottom" secondItem="fOg-Pl-Eop" secondAttribute="bottom" constant="80" id="TWd-4E-Va7"/>
                                        <constraint firstItem="dEc-Q4-pFP" firstAttribute="centerX" secondItem="lO2-cF-EI3" secondAttribute="centerX" id="Xki-H5-Aan"/>
                                        <constraint firstAttribute="bottom" secondItem="8XG-dG-kHa" secondAttribute="bottom" constant="114" id="gyV-lq-mah"/>
                                        <constraint firstItem="lO2-cF-EI3" firstAttribute="top" secondItem="5C7-y4-xNe" secondAttribute="top" id="kf9-B2-8yF"/>
                                        <constraint firstItem="5C7-y4-xNe" firstAttribute="top" secondItem="Os1-RU-LbG" secondAttribute="top" constant="31" id="qck-IH-cli"/>
                                        <constraint firstItem="Ysu-37-D2U" firstAttribute="centerX" secondItem="5C7-y4-xNe" secondAttribute="centerX" id="rVf-3w-BHe"/>
                                        <constraint firstItem="lO2-cF-EI3" firstAttribute="leading" secondItem="fOg-Pl-Eop" secondAttribute="trailing" constant="75" id="xLK-gX-7QA"/>
                                        <constraint firstItem="8XG-dG-kHa" firstAttribute="centerX" secondItem="Os1-RU-LbG" secondAttribute="centerX" id="z6f-bk-H2e"/>
                                        <constraint firstItem="lO2-cF-EI3" firstAttribute="bottom" secondItem="BzH-ht-cTd" secondAttribute="bottom" constant="-10" id="zTe-ik-ml1"/>
                                        <constraint firstItem="BzH-ht-cTd" firstAttribute="centerX" secondItem="Os1-RU-LbG" secondAttribute="centerX" id="zWC-wc-xdc"/>
                                    </constraints>
                                    <connections>
                                        <action selector="avatarTap:" destination="-1" eventType="touchUpInside" id="Me7-gb-cgb"/>
                                    </connections>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nUv-9p-Agl">
                                    <rect key="frame" x="16" y="147.66666666666666" width="361" height="137.99999999999997"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ckC-Xc-1Ox">
                                            <rect key="frame" x="-16" y="-16" width="74" height="44"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="限时" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QNj-gN-FYp">
                                                    <rect key="frame" x="32" y="21" width="26" height="18"/>
                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                                                    <color key="textColor" red="0.93955045940000004" green="0.49229961630000002" blue="0.2266653776" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.99338430170000003" green="0.96093946699999999" blue="0.66533964869999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="QNj-gN-FYp" secondAttribute="bottom" constant="5" id="4jk-dV-VtE"/>
                                                <constraint firstItem="QNj-gN-FYp" firstAttribute="leading" secondItem="ckC-Xc-1Ox" secondAttribute="leading" constant="32" id="5zC-FL-2tf"/>
                                                <constraint firstItem="QNj-gN-FYp" firstAttribute="top" secondItem="ckC-Xc-1Ox" secondAttribute="top" constant="21" id="EAj-O3-sac"/>
                                                <constraint firstAttribute="height" constant="44" id="Qxr-6K-hEw"/>
                                                <constraint firstAttribute="trailing" secondItem="QNj-gN-FYp" secondAttribute="trailing" constant="16" id="ZhK-8E-9TI"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="16"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bza-ty-vwl">
                                            <rect key="frame" x="0.0" y="5" width="162.33333333333334" height="128"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="amv-xj-E5l">
                                                    <rect key="frame" x="25.333333333333336" y="31" width="111.66666666666666" height="17"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_star_left" translatesAutoresizingMaskIntoConstraints="NO" id="kLh-3J-UBg">
                                                            <rect key="frame" x="0.0" y="0.0" width="28" height="17"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="升级会员" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="amk-Dh-jvt">
                                                            <rect key="frame" x="27.999999999999996" y="0.0" width="55.666666666666657" height="17"/>
                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                            <color key="textColor" red="0.95916825530000005" green="0.41231974960000001" blue="0.2499587238" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_star_right" translatesAutoresizingMaskIntoConstraints="NO" id="M9U-y8-87j">
                                                            <rect key="frame" x="83.666666666666657" y="0.0" width="28" height="17"/>
                                                        </imageView>
                                                    </subviews>
                                                </stackView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="50%sales" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ec4-kD-BI8">
                                                    <rect key="frame" x="43.666666666666657" y="51.000000000000028" width="75" height="20.***************"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qjg-9j-sAi">
                                                    <rect key="frame" x="27.666666666666657" y="81.333333333333343" width="107" height="30"/>
                                                    <color key="backgroundColor" red="0.9664649367" green="0.54917484520000004" blue="0.70012426380000004" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="107" id="4QL-s7-Nde"/>
                                                        <constraint firstAttribute="height" constant="30" id="SUj-Va-QyS"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="13"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="立即领取"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="15"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="titleLocalizeKey" value="person_vip_claim_now"/>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="buyVipButtonAction:" destination="-1" eventType="touchUpInside" id="ECd-TX-uge"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="ec4-kD-BI8" firstAttribute="centerX" secondItem="bza-ty-vwl" secondAttribute="centerX" id="9ZK-JR-wyI"/>
                                                <constraint firstItem="amv-xj-E5l" firstAttribute="top" secondItem="bza-ty-vwl" secondAttribute="top" constant="31" id="Y8Y-hk-I5c"/>
                                                <constraint firstItem="qjg-9j-sAi" firstAttribute="top" secondItem="ec4-kD-BI8" secondAttribute="bottom" constant="10" id="ZT8-lw-mvt"/>
                                                <constraint firstItem="ec4-kD-BI8" firstAttribute="top" secondItem="amv-xj-E5l" secondAttribute="bottom" constant="3" id="ZhZ-Dz-XHc"/>
                                                <constraint firstItem="amv-xj-E5l" firstAttribute="centerX" secondItem="bza-ty-vwl" secondAttribute="centerX" id="bGT-3o-kjC"/>
                                                <constraint firstItem="qjg-9j-sAi" firstAttribute="centerX" secondItem="bza-ty-vwl" secondAttribute="centerX" id="oVS-an-00v"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jAq-0u-9YM">
                                            <rect key="frame" x="170" y="8" width="183" height="122"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="122" id="Y7s-bV-raU"/>
                                                <constraint firstAttribute="width" constant="183" id="mGS-Ct-PPm"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" red="0.98683017490000002" green="0.86235255" blue="0.86077564949999996" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="bza-ty-vwl" secondAttribute="bottom" constant="5" id="6XU-WT-n6v"/>
                                        <constraint firstItem="bza-ty-vwl" firstAttribute="leading" secondItem="nUv-9p-Agl" secondAttribute="leading" id="7a4-Jc-oIE"/>
                                        <constraint firstItem="bza-ty-vwl" firstAttribute="width" secondItem="nUv-9p-Agl" secondAttribute="width" multiplier="0.45" id="MEo-mY-c5e"/>
                                        <constraint firstItem="ckC-Xc-1Ox" firstAttribute="top" secondItem="nUv-9p-Agl" secondAttribute="top" constant="-16" id="SoT-jT-nGv"/>
                                        <constraint firstItem="bza-ty-vwl" firstAttribute="top" secondItem="nUv-9p-Agl" secondAttribute="top" constant="5" id="VDr-cB-xK7"/>
                                        <constraint firstItem="jAq-0u-9YM" firstAttribute="centerY" secondItem="nUv-9p-Agl" secondAttribute="centerY" id="b29-7d-irr"/>
                                        <constraint firstAttribute="trailing" secondItem="jAq-0u-9YM" secondAttribute="trailing" constant="8" id="cOK-7v-q0F"/>
                                        <constraint firstItem="ckC-Xc-1Ox" firstAttribute="leading" secondItem="nUv-9p-Agl" secondAttribute="leading" constant="-16" id="hhO-2U-cRa"/>
                                        <constraint firstAttribute="height" constant="138" id="lwV-XQ-vvM"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="16"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="doS-8d-IzL">
                                    <rect key="frame" x="16" y="301.66666666666669" width="361" height="162"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="750" verticalHuggingPriority="251" text="设置" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EoN-gC-d02">
                                            <rect key="frame" x="16" y="12" width="28" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="YL_Setup"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="OLG-rk-gfs">
                                            <rect key="frame" x="0.0" y="39" width="361" height="123"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ejy-Ga-It6" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="0.0" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_center_music" translatesAutoresizingMaskIntoConstraints="NO" id="XzJ-yx-G8p">
                                                            <rect key="frame" x="16" y="9.3333333333333126" width="22" height="22.***************"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="音效" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vf3-dn-XpL">
                                                            <rect key="frame" x="46" y="11.666666666666629" width="30" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_sounds"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="Sdh-Qz-Xab">
                                                            <rect key="frame" x="333" y="12.***************" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pcb-ad-uDd">
                                                            <rect key="frame" x="46" y="40.666666666666629" width="299" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="tLa-Fk-2fm"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="Vf3-dn-XpL" firstAttribute="leading" secondItem="XzJ-yx-G8p" secondAttribute="trailing" constant="8" id="25w-zK-WCr"/>
                                                        <constraint firstItem="Sdh-Qz-Xab" firstAttribute="centerY" secondItem="ejy-Ga-It6" secondAttribute="centerY" id="Bf4-r0-XLf"/>
                                                        <constraint firstItem="XzJ-yx-G8p" firstAttribute="leading" secondItem="ejy-Ga-It6" secondAttribute="leading" constant="16" id="L54-z1-73N"/>
                                                        <constraint firstAttribute="trailing" secondItem="Sdh-Qz-Xab" secondAttribute="trailing" constant="16" id="O4B-fz-yoE"/>
                                                        <constraint firstItem="pcb-ad-uDd" firstAttribute="leading" secondItem="Vf3-dn-XpL" secondAttribute="leading" id="YOf-PI-Y5e"/>
                                                        <constraint firstAttribute="trailing" secondItem="pcb-ad-uDd" secondAttribute="trailing" constant="16" id="hKG-wt-3db"/>
                                                        <constraint firstAttribute="height" constant="41" id="jwO-mr-imC"/>
                                                        <constraint firstAttribute="bottom" secondItem="pcb-ad-uDd" secondAttribute="bottom" id="k9x-wc-dve"/>
                                                        <constraint firstItem="XzJ-yx-G8p" firstAttribute="centerY" secondItem="ejy-Ga-It6" secondAttribute="centerY" id="pe7-3O-Mgz"/>
                                                        <constraint firstItem="Vf3-dn-XpL" firstAttribute="centerY" secondItem="ejy-Ga-It6" secondAttribute="centerY" id="xMO-d4-I32"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="musicTap:" destination="-1" eventType="touchUpInside" id="hjw-e2-V83"/>
                                                    </connections>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sSZ-GN-hEw" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="41" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_language" translatesAutoresizingMaskIntoConstraints="NO" id="M12-Hc-Q85">
                                                            <rect key="frame" x="16" y="10.666666666666629" width="22" height="20"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="语言" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OoA-6L-z28">
                                                            <rect key="frame" x="46" y="11.666666666666629" width="30" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_center_language"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="J8I-hf-M9E">
                                                            <rect key="frame" x="333" y="12.***************" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J3P-tz-6QA">
                                                            <rect key="frame" x="46" y="40.666666666666629" width="299" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="ssg-Nq-9g1"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="OoA-6L-z28" firstAttribute="centerY" secondItem="sSZ-GN-hEw" secondAttribute="centerY" id="5pK-MO-dir"/>
                                                        <constraint firstItem="J3P-tz-6QA" firstAttribute="leading" secondItem="OoA-6L-z28" secondAttribute="leading" id="9jB-YB-w8n"/>
                                                        <constraint firstItem="OoA-6L-z28" firstAttribute="leading" secondItem="M12-Hc-Q85" secondAttribute="trailing" constant="8" id="Aj1-Z6-674"/>
                                                        <constraint firstAttribute="trailing" secondItem="J3P-tz-6QA" secondAttribute="trailing" constant="16" id="FNK-jb-IIx"/>
                                                        <constraint firstItem="M12-Hc-Q85" firstAttribute="centerY" secondItem="sSZ-GN-hEw" secondAttribute="centerY" id="WXn-0X-mbZ"/>
                                                        <constraint firstAttribute="trailing" secondItem="J8I-hf-M9E" secondAttribute="trailing" constant="16" id="XOE-o8-mac"/>
                                                        <constraint firstItem="J8I-hf-M9E" firstAttribute="centerY" secondItem="sSZ-GN-hEw" secondAttribute="centerY" id="gHT-Qe-vj0"/>
                                                        <constraint firstAttribute="height" constant="41" id="hMp-3c-gaF"/>
                                                        <constraint firstAttribute="bottom" secondItem="J3P-tz-6QA" secondAttribute="bottom" id="nnd-8k-xft"/>
                                                        <constraint firstItem="M12-Hc-Q85" firstAttribute="leading" secondItem="sSZ-GN-hEw" secondAttribute="leading" constant="16" id="wwY-WX-ZvZ"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="languageTap:" destination="-1" eventType="touchUpInside" id="EXK-Jf-Rcd"/>
                                                    </connections>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NRa-xL-bXk" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="82" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="grzx_gywm_img" translatesAutoresizingMaskIntoConstraints="NO" id="KxR-kT-2tI">
                                                            <rect key="frame" x="16" y="9.3333333333333126" width="22" height="22.***************"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="账号&amp;关系" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tLG-kH-KN0">
                                                            <rect key="frame" x="46" y="11.666666666666629" width="70" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_account"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="qxL-7Q-Ioa">
                                                            <rect key="frame" x="333" y="12.***************" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FVo-1l-wls">
                                                            <rect key="frame" x="46" y="40.***************" width="299" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="syq-l6-yNh"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="qxL-7Q-Ioa" firstAttribute="centerY" secondItem="NRa-xL-bXk" secondAttribute="centerY" id="0NL-Ho-zn8"/>
                                                        <constraint firstItem="FVo-1l-wls" firstAttribute="leading" secondItem="tLG-kH-KN0" secondAttribute="leading" id="3Mg-9y-raY"/>
                                                        <constraint firstItem="tLG-kH-KN0" firstAttribute="leading" secondItem="KxR-kT-2tI" secondAttribute="trailing" constant="8" id="3bh-f9-w8Q"/>
                                                        <constraint firstAttribute="bottom" secondItem="FVo-1l-wls" secondAttribute="bottom" id="51U-Vo-x3n"/>
                                                        <constraint firstItem="KxR-kT-2tI" firstAttribute="centerY" secondItem="NRa-xL-bXk" secondAttribute="centerY" id="X8w-0i-Zu6"/>
                                                        <constraint firstItem="tLG-kH-KN0" firstAttribute="centerY" secondItem="NRa-xL-bXk" secondAttribute="centerY" id="b7X-uI-43r"/>
                                                        <constraint firstAttribute="trailing" secondItem="FVo-1l-wls" secondAttribute="trailing" constant="16" id="d61-wH-dFQ"/>
                                                        <constraint firstAttribute="height" constant="41" id="sU7-4b-bnk"/>
                                                        <constraint firstAttribute="trailing" secondItem="qxL-7Q-Ioa" secondAttribute="trailing" constant="16" id="tUF-kJ-AS6"/>
                                                        <constraint firstItem="KxR-kT-2tI" firstAttribute="leading" secondItem="NRa-xL-bXk" secondAttribute="leading" constant="16" id="zJu-on-aiT"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="acountTap:" destination="-1" eventType="touchUpInside" id="TRz-8D-Kgy"/>
                                                    </connections>
                                                </view>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="OLG-rk-gfs" secondAttribute="bottom" id="PAF-dI-ldJ"/>
                                        <constraint firstItem="EoN-gC-d02" firstAttribute="leading" secondItem="doS-8d-IzL" secondAttribute="leading" constant="16" id="THO-Za-i93"/>
                                        <constraint firstItem="OLG-rk-gfs" firstAttribute="top" secondItem="EoN-gC-d02" secondAttribute="bottom" constant="10" id="YcO-wv-ev1"/>
                                        <constraint firstAttribute="trailing" secondItem="OLG-rk-gfs" secondAttribute="trailing" id="a4U-5q-bXa"/>
                                        <constraint firstItem="OLG-rk-gfs" firstAttribute="leading" secondItem="doS-8d-IzL" secondAttribute="leading" id="lX6-y7-sAM"/>
                                        <constraint firstItem="EoN-gC-d02" firstAttribute="top" secondItem="doS-8d-IzL" secondAttribute="top" constant="12" id="wEw-Xu-8EA"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="16"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5oz-J1-vmA">
                                    <rect key="frame" x="16" y="479.66666666666663" width="361" height="203"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="750" verticalHuggingPriority="251" text="关于&amp;支持" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cdO-96-WIt">
                                            <rect key="frame" x="16" y="12" width="65.666666666666671" height="17"/>
                                            <fontDescription key="fontDescription" name=".AppleSystemUIFont" family=".AppleSystemUIFont" pointSize="14"/>
                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_about"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="fKD-6o-HRq">
                                            <rect key="frame" x="0.0" y="39" width="361" height="164"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="t2J-Z2-WWP" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="0.0" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="grzx_xzj_img" translatesAutoresizingMaskIntoConstraints="NO" id="s6c-n2-Ujf">
                                                            <rect key="frame" x="16" y="10.666666666666742" width="22" height="20"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="小组件" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vn3-u7-6Wt">
                                                            <rect key="frame" x="46" y="11.666666666666742" width="44.666666666666657" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_center_widgets"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="fae-AW-KZ9">
                                                            <rect key="frame" x="333" y="12.333333333333369" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ctz-dj-qrs">
                                                            <rect key="frame" x="46" y="40.666666666666742" width="299" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="gg3-wy-LXf"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="ctz-dj-qrs" firstAttribute="leading" secondItem="Vn3-u7-6Wt" secondAttribute="leading" id="2Xr-cq-LnI"/>
                                                        <constraint firstItem="fae-AW-KZ9" firstAttribute="centerY" secondItem="t2J-Z2-WWP" secondAttribute="centerY" id="7BR-ub-xy9"/>
                                                        <constraint firstItem="Vn3-u7-6Wt" firstAttribute="leading" secondItem="s6c-n2-Ujf" secondAttribute="trailing" constant="8" id="7lK-aA-x4F"/>
                                                        <constraint firstAttribute="height" constant="41" id="EOr-Br-MDq"/>
                                                        <constraint firstItem="s6c-n2-Ujf" firstAttribute="centerY" secondItem="t2J-Z2-WWP" secondAttribute="centerY" id="IZi-7r-wDo"/>
                                                        <constraint firstAttribute="trailing" secondItem="ctz-dj-qrs" secondAttribute="trailing" constant="16" id="Shf-kL-u5Z"/>
                                                        <constraint firstItem="s6c-n2-Ujf" firstAttribute="leading" secondItem="t2J-Z2-WWP" secondAttribute="leading" constant="16" id="cL9-v2-Zco"/>
                                                        <constraint firstItem="Vn3-u7-6Wt" firstAttribute="centerY" secondItem="t2J-Z2-WWP" secondAttribute="centerY" id="eT3-kA-7Ky"/>
                                                        <constraint firstAttribute="trailing" secondItem="fae-AW-KZ9" secondAttribute="trailing" constant="16" id="i8F-wh-ZY6"/>
                                                        <constraint firstAttribute="bottom" secondItem="ctz-dj-qrs" secondAttribute="bottom" id="pKc-rh-bI8"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="weightTap:" destination="-1" eventType="touchUpInside" id="qhu-od-5dK"/>
                                                    </connections>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DoM-47-Dll" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="41" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="grzx_yhfk_img" translatesAutoresizingMaskIntoConstraints="NO" id="yjQ-q3-VOx">
                                                            <rect key="frame" x="16" y="10.666666666666742" width="22" height="20"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="意见反馈" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fhj-OU-QJP">
                                                            <rect key="frame" x="46" y="11.666666666666742" width="59.666666666666657" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_center_advice"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="jlu-6M-BkC">
                                                            <rect key="frame" x="333" y="12.333333333333369" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2OE-ed-yrq">
                                                            <rect key="frame" x="46" y="40.666666666666742" width="299" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="mSr-zb-L8q"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="2OE-ed-yrq" secondAttribute="trailing" constant="16" id="29d-3f-Vm7"/>
                                                        <constraint firstItem="yjQ-q3-VOx" firstAttribute="centerY" secondItem="DoM-47-Dll" secondAttribute="centerY" id="2G6-pN-niP"/>
                                                        <constraint firstAttribute="height" constant="41" id="7Kz-XM-pNX"/>
                                                        <constraint firstItem="Fhj-OU-QJP" firstAttribute="centerY" secondItem="DoM-47-Dll" secondAttribute="centerY" id="Mtw-Ia-cGb"/>
                                                        <constraint firstItem="yjQ-q3-VOx" firstAttribute="leading" secondItem="DoM-47-Dll" secondAttribute="leading" constant="16" id="P6x-Ih-ZuS"/>
                                                        <constraint firstItem="Fhj-OU-QJP" firstAttribute="leading" secondItem="yjQ-q3-VOx" secondAttribute="trailing" constant="8" id="Quk-4D-RJh"/>
                                                        <constraint firstItem="jlu-6M-BkC" firstAttribute="centerY" secondItem="DoM-47-Dll" secondAttribute="centerY" id="ZIu-9W-sFC"/>
                                                        <constraint firstAttribute="trailing" secondItem="jlu-6M-BkC" secondAttribute="trailing" constant="16" id="ngI-zk-tyk"/>
                                                        <constraint firstItem="2OE-ed-yrq" firstAttribute="leading" secondItem="Fhj-OU-QJP" secondAttribute="leading" id="nra-IA-rlX"/>
                                                        <constraint firstAttribute="bottom" secondItem="2OE-ed-yrq" secondAttribute="bottom" id="t0B-0a-Vvu"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="feekTap:" destination="-1" eventType="touchUpInside" id="DTC-Q7-LEf"/>
                                                    </connections>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U68-sT-HN0" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="82" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="image_grzx_gywm" translatesAutoresizingMaskIntoConstraints="NO" id="w6u-nY-g8G">
                                                            <rect key="frame" x="16" y="11" width="20.666666666666671" height="19"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="关于我们" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="URW-ic-Spn">
                                                            <rect key="frame" x="44.666666666666671" y="11.666666666666742" width="59.666666666666671" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_center_about"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="5FG-3h-9pA">
                                                            <rect key="frame" x="333" y="12.333333333333369" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YAo-LD-XgV">
                                                            <rect key="frame" x="44.666666666666657" y="40.666666666666742" width="300.33333333333337" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="cfM-qP-Ojh"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="URW-ic-Spn" firstAttribute="leading" secondItem="w6u-nY-g8G" secondAttribute="trailing" constant="8" id="6Oh-bO-BPY"/>
                                                        <constraint firstAttribute="height" constant="41" id="G4p-vR-zuQ"/>
                                                        <constraint firstItem="5FG-3h-9pA" firstAttribute="centerY" secondItem="U68-sT-HN0" secondAttribute="centerY" id="GHo-6o-qrd"/>
                                                        <constraint firstAttribute="bottom" secondItem="YAo-LD-XgV" secondAttribute="bottom" id="JPa-kZ-3aG"/>
                                                        <constraint firstItem="URW-ic-Spn" firstAttribute="centerY" secondItem="U68-sT-HN0" secondAttribute="centerY" id="Mna-K4-KPQ"/>
                                                        <constraint firstItem="w6u-nY-g8G" firstAttribute="centerY" secondItem="U68-sT-HN0" secondAttribute="centerY" id="RAX-hg-7kF"/>
                                                        <constraint firstItem="w6u-nY-g8G" firstAttribute="leading" secondItem="U68-sT-HN0" secondAttribute="leading" constant="16" id="VRc-qw-nYQ"/>
                                                        <constraint firstAttribute="trailing" secondItem="YAo-LD-XgV" secondAttribute="trailing" constant="16" id="dF6-Ev-Bw8"/>
                                                        <constraint firstItem="YAo-LD-XgV" firstAttribute="leading" secondItem="URW-ic-Spn" secondAttribute="leading" id="rs1-yI-JFt"/>
                                                        <constraint firstAttribute="trailing" secondItem="5FG-3h-9pA" secondAttribute="trailing" constant="16" id="syv-Lk-biM"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="abputTap:" destination="-1" eventType="touchUpInside" id="Osy-hF-BvQ"/>
                                                    </connections>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4lS-26-5tW" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="123" width="361" height="41"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="evaluate_img" translatesAutoresizingMaskIntoConstraints="NO" id="Crn-ga-c22">
                                                            <rect key="frame" x="16" y="10.666666666666742" width="22" height="20"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="鼓励我们" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fvZ-U6-RDh">
                                                            <rect key="frame" x="46" y="11.666666666666742" width="59.666666666666657" height="18"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                            <color key="textColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_encourage"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_new_right_jt" translatesAutoresizingMaskIntoConstraints="NO" id="orz-ui-EsJ">
                                                            <rect key="frame" x="333" y="12.333333333333369" width="12" height="16.***************"/>
                                                        </imageView>
                                                        <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8c3-7g-j5a">
                                                            <rect key="frame" x="46" y="40.666666666666742" width="299" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.**********" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="BHj-rS-Tpu"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="8c3-7g-j5a" firstAttribute="leading" secondItem="fvZ-U6-RDh" secondAttribute="leading" id="2Av-Pd-x97"/>
                                                        <constraint firstAttribute="bottom" secondItem="8c3-7g-j5a" secondAttribute="bottom" id="6s2-2O-NwP"/>
                                                        <constraint firstAttribute="trailing" secondItem="8c3-7g-j5a" secondAttribute="trailing" constant="16" id="ddt-vr-dFm"/>
                                                        <constraint firstItem="fvZ-U6-RDh" firstAttribute="centerY" secondItem="4lS-26-5tW" secondAttribute="centerY" id="fnf-K6-bgw"/>
                                                        <constraint firstItem="orz-ui-EsJ" firstAttribute="centerY" secondItem="4lS-26-5tW" secondAttribute="centerY" id="jTB-Y7-oUD"/>
                                                        <constraint firstAttribute="trailing" secondItem="orz-ui-EsJ" secondAttribute="trailing" constant="16" id="oIY-Te-S2u"/>
                                                        <constraint firstItem="Crn-ga-c22" firstAttribute="leading" secondItem="4lS-26-5tW" secondAttribute="leading" constant="16" id="qda-XR-MBP"/>
                                                        <constraint firstItem="Crn-ga-c22" firstAttribute="centerY" secondItem="4lS-26-5tW" secondAttribute="centerY" id="rOd-Cc-cLj"/>
                                                        <constraint firstItem="fvZ-U6-RDh" firstAttribute="leading" secondItem="Crn-ga-c22" secondAttribute="trailing" constant="8" id="xWb-ou-JV0"/>
                                                        <constraint firstAttribute="height" constant="41" id="zWS-Sj-3Xv"/>
                                                    </constraints>
                                                    <connections>
                                                        <action selector="goodTap:" destination="-1" eventType="touchUpInside" id="K98-Zw-JKs"/>
                                                    </connections>
                                                </view>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="fKD-6o-HRq" secondAttribute="bottom" id="5lo-I2-oXx"/>
                                        <constraint firstItem="fKD-6o-HRq" firstAttribute="leading" secondItem="5oz-J1-vmA" secondAttribute="leading" id="Eli-o4-0KE"/>
                                        <constraint firstItem="cdO-96-WIt" firstAttribute="leading" secondItem="5oz-J1-vmA" secondAttribute="leading" constant="16" id="L95-Tb-eE9"/>
                                        <constraint firstItem="fKD-6o-HRq" firstAttribute="top" secondItem="cdO-96-WIt" secondAttribute="bottom" constant="10" id="LEA-60-78Y"/>
                                        <constraint firstAttribute="trailing" secondItem="fKD-6o-HRq" secondAttribute="trailing" id="p8k-Qn-xnb"/>
                                        <constraint firstItem="cdO-96-WIt" firstAttribute="top" secondItem="5oz-J1-vmA" secondAttribute="top" constant="12" id="zbh-9x-o1R"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="16"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rBN-hD-LFl">
                                    <rect key="frame" x="16" y="698.66666666666663" width="361" height="138.66666666666663"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gSw-xl-FjP">
                                            <rect key="frame" x="89.***************" y="16" width="182.33333333333337" height="18"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_bottom_line_left" translatesAutoresizingMaskIntoConstraints="NO" id="toy-U3-gBQ">
                                                    <rect key="frame" x="0.0" y="0.0" width="45" height="18"/>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="关注 Couple2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Smk-MB-dK1">
                                                    <rect key="frame" x="45.000000000000021" y="0.0" width="92.333333333333314" height="18"/>
                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                    <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_follow"/>
                                                    </userDefinedRuntimeAttributes>
                                                </label>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_bottom_line_right" translatesAutoresizingMaskIntoConstraints="NO" id="qv3-WS-iQr">
                                                    <rect key="frame" x="137.33333333333331" y="0.0" width="45" height="18"/>
                                                </imageView>
                                            </subviews>
                                        </stackView>
                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="BjE-4W-duf">
                                            <rect key="frame" x="47.666666666666657" y="44" width="266" height="40"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="c93-fQ-bYZ" customClass="UIControl">
                                                    <rect key="frame" x="0.0" y="0.0" width="123" height="40"/>
                                                    <subviews>
                                                        <stackView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="OSw-S8-azJ">
                                                            <rect key="frame" x="20.333333333333336" y="10" width="82" height="20"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_bottom_ins" translatesAutoresizingMaskIntoConstraints="NO" id="L9X-9X-Qf7">
                                                                    <rect key="frame" x="0.0" y="0.0" width="20.333333333333332" height="20"/>
                                                                </imageView>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Instagram" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TBT-iC-8rn">
                                                                    <rect key="frame" x="25.333333333333325" y="0.0" width="56.666666666666657" height="20"/>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                                    <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                    <color key="backgroundColor" red="0.96441298720000002" green="0.95548266169999996" blue="0.90462189910000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstItem="OSw-S8-azJ" firstAttribute="centerX" secondItem="c93-fQ-bYZ" secondAttribute="centerX" id="6gg-Sl-sVR"/>
                                                        <constraint firstItem="OSw-S8-azJ" firstAttribute="centerY" secondItem="c93-fQ-bYZ" secondAttribute="centerY" id="BFS-Sk-uGQ"/>
                                                        <constraint firstAttribute="width" constant="123" id="JXg-eG-Xlt"/>
                                                        <constraint firstAttribute="height" constant="40" id="YaB-Cq-bF4"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="12"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="insAction:" destination="-1" eventType="touchUpInside" id="43Y-Sa-ZUm"/>
                                                    </connections>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hBH-3y-g4M" customClass="UIControl">
                                                    <rect key="frame" x="143" y="0.0" width="123" height="40"/>
                                                    <subviews>
                                                        <stackView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="dqZ-on-LLF">
                                                            <rect key="frame" x="30.333333333333343" y="10" width="62" height="20"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="person_bottom_tiktok" translatesAutoresizingMaskIntoConstraints="NO" id="zkt-vf-i9b">
                                                                    <rect key="frame" x="0.0" y="0.0" width="20.333333333333332" height="20"/>
                                                                </imageView>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="TikTok" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QYH-67-EQJ">
                                                                    <rect key="frame" x="25.333333333333311" y="0.0" width="36.666666666666657" height="20"/>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                                    <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                    <color key="backgroundColor" red="0.96441298720000002" green="0.95548266169999996" blue="0.90462189910000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="40" id="5ni-rC-xZM"/>
                                                        <constraint firstItem="dqZ-on-LLF" firstAttribute="centerY" secondItem="hBH-3y-g4M" secondAttribute="centerY" id="IbH-vf-HK5"/>
                                                        <constraint firstItem="dqZ-on-LLF" firstAttribute="centerX" secondItem="hBH-3y-g4M" secondAttribute="centerX" id="pbb-B0-hSm"/>
                                                        <constraint firstAttribute="width" constant="123" id="sSK-h7-TgE"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="12"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="tiktokAction:" destination="-1" eventType="touchUpInside" id="Aqt-5Y-mjX"/>
                                                    </connections>
                                                </view>
                                            </subviews>
                                        </stackView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="关注我们的账号，持续获取最新消息，我们将为你提供更优质的服务！🙌💕" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fL6-Ld-vJc">
                                            <rect key="frame" x="16" y="94" width="329" height="28.666666666666671"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                            <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="person_follow_tips"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstItem="fL6-Ld-vJc" firstAttribute="leading" secondItem="rBN-hD-LFl" secondAttribute="leading" constant="16" id="5b8-sO-L9v"/>
                                        <constraint firstItem="BjE-4W-duf" firstAttribute="centerX" secondItem="rBN-hD-LFl" secondAttribute="centerX" id="9cW-gW-BnO"/>
                                        <constraint firstAttribute="bottom" secondItem="fL6-Ld-vJc" secondAttribute="bottom" constant="16" id="BCt-v8-NAi"/>
                                        <constraint firstItem="gSw-xl-FjP" firstAttribute="centerX" secondItem="rBN-hD-LFl" secondAttribute="centerX" id="IBo-Ig-OAV"/>
                                        <constraint firstItem="gSw-xl-FjP" firstAttribute="top" secondItem="rBN-hD-LFl" secondAttribute="top" constant="16" id="Mph-kT-hwh"/>
                                        <constraint firstItem="BjE-4W-duf" firstAttribute="top" secondItem="gSw-xl-FjP" secondAttribute="bottom" constant="10" id="QIZ-PU-yDL"/>
                                        <constraint firstItem="fL6-Ld-vJc" firstAttribute="top" secondItem="BjE-4W-duf" secondAttribute="bottom" constant="10" id="ZYj-xR-RGl"/>
                                        <constraint firstAttribute="trailing" secondItem="fL6-Ld-vJc" secondAttribute="trailing" constant="16" id="u7b-d0-qMF"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="16"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="Os1-RU-LbG" secondAttribute="trailing" constant="16" id="79x-gB-Q5z"/>
                                <constraint firstAttribute="trailing" secondItem="rBN-hD-LFl" secondAttribute="trailing" constant="16" id="BY9-7v-Qzc"/>
                                <constraint firstItem="rBN-hD-LFl" firstAttribute="top" secondItem="5oz-J1-vmA" secondAttribute="bottom" constant="16" id="CUr-9E-vCN"/>
                                <constraint firstAttribute="trailing" secondItem="doS-8d-IzL" secondAttribute="trailing" constant="16" id="LmF-ci-9Lr"/>
                                <constraint firstItem="5oz-J1-vmA" firstAttribute="leading" secondItem="KBZ-ho-kaL" secondAttribute="leading" constant="16" id="Sko-gm-kiu"/>
                                <constraint firstItem="Os1-RU-LbG" firstAttribute="leading" secondItem="KBZ-ho-kaL" secondAttribute="leading" id="VkB-TN-JoC"/>
                                <constraint firstItem="5oz-J1-vmA" firstAttribute="top" secondItem="doS-8d-IzL" secondAttribute="bottom" constant="16" id="koc-wv-f6q"/>
                                <constraint firstItem="doS-8d-IzL" firstAttribute="leading" secondItem="KBZ-ho-kaL" secondAttribute="leading" constant="16" id="lnb-ig-uKS"/>
                                <constraint firstItem="doS-8d-IzL" firstAttribute="top" secondItem="nUv-9p-Agl" secondAttribute="bottom" constant="16" id="nAG-Dy-72G"/>
                                <constraint firstItem="nUv-9p-Agl" firstAttribute="top" secondItem="Os1-RU-LbG" secondAttribute="bottom" constant="5" id="nhr-VE-2OI"/>
                                <constraint firstItem="nUv-9p-Agl" firstAttribute="leading" secondItem="KBZ-ho-kaL" secondAttribute="leading" constant="16" id="njN-8j-3lD"/>
                                <constraint firstAttribute="trailing" secondItem="nUv-9p-Agl" secondAttribute="trailing" constant="16" id="ssc-Uy-mZi"/>
                                <constraint firstAttribute="bottom" secondItem="rBN-hD-LFl" secondAttribute="bottom" constant="30" id="toC-DH-Gqc"/>
                                <constraint firstItem="rBN-hD-LFl" firstAttribute="leading" secondItem="KBZ-ho-kaL" secondAttribute="leading" constant="16" id="waX-yT-iOV"/>
                                <constraint firstAttribute="trailing" secondItem="5oz-J1-vmA" secondAttribute="trailing" constant="16" id="xOd-OQ-z76"/>
                                <constraint firstItem="Os1-RU-LbG" firstAttribute="top" secondItem="KBZ-ho-kaL" secondAttribute="top" id="xZw-WT-5k2"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="KBZ-ho-kaL" firstAttribute="leading" secondItem="yQd-WE-hs1" secondAttribute="leading" id="IEg-J7-qFT"/>
                        <constraint firstAttribute="bottom" secondItem="KBZ-ho-kaL" secondAttribute="bottom" constant="-35" id="RRq-uk-ZX4"/>
                        <constraint firstItem="KBZ-ho-kaL" firstAttribute="top" secondItem="yQd-WE-hs1" secondAttribute="top" id="ZPz-KF-QyC"/>
                        <constraint firstAttribute="trailing" secondItem="KBZ-ho-kaL" secondAttribute="trailing" id="e3q-5W-NvA"/>
                        <constraint firstItem="KBZ-ho-kaL" firstAttribute="trailing" secondItem="yQd-WE-hs1" secondAttribute="trailing" id="mlC-CY-mAz"/>
                    </constraints>
                </scrollView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="xif-Ow-cQr" secondAttribute="bottom" id="ESa-ka-xY1"/>
                <constraint firstItem="yQd-WE-hs1" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="FGt-yU-mCN"/>
                <constraint firstItem="xif-Ow-cQr" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="Mp5-D5-VvV"/>
                <constraint firstItem="KBZ-ho-kaL" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" id="SRo-Ph-blV"/>
                <constraint firstItem="xif-Ow-cQr" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="UJV-96-3M4"/>
                <constraint firstAttribute="bottom" secondItem="yQd-WE-hs1" secondAttribute="bottom" id="Wmz-h0-XzC"/>
                <constraint firstItem="xif-Ow-cQr" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="gNE-SV-ues"/>
                <constraint firstItem="yQd-WE-hs1" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="nCM-49-B0f"/>
                <constraint firstItem="yQd-WE-hs1" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="uBm-lV-1uP"/>
            </constraints>
            <point key="canvasLocation" x="174.80916030534351" y="-15.492957746478874"/>
        </view>
    </objects>
    <resources>
        <image name="evaluate_img" width="22" height="20"/>
        <image name="grzx_gywm_img" width="22" height="22.333333969116211"/>
        <image name="grzx_xzj_img" width="22" height="20"/>
        <image name="grzx_yhfk_img" width="22" height="20"/>
        <image name="image_grzx_gywm" width="20.666666030883789" height="19"/>
        <image name="image_new_person" width="393" height="852"/>
        <image name="person_bottom_ins" width="20.333333969116211" height="20"/>
        <image name="person_bottom_line_left" width="45" height="1"/>
        <image name="person_bottom_line_right" width="45" height="1"/>
        <image name="person_bottom_tiktok" width="20.333333969116211" height="20"/>
        <image name="person_center_music" width="22" height="22.333333969116211"/>
        <image name="person_language" width="22" height="20"/>
        <image name="person_new_right_jt" width="12" height="16.333333969116211"/>
        <image name="person_new_star_left" width="28" height="7.3333334922790527"/>
        <image name="person_new_star_right" width="28" height="7.3333334922790527"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
