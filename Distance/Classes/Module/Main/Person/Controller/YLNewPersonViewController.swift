//
//  YLNewPersonViewController.swift
//  Distance
//
//  Created by Q on 2025/7/17.
//

import UIKit
import SDWebImage
import Lottie
import URLNavigator
import MessageUI

class YLNewPersonViewController: DTBaseViewController, CustomNavigation {
	var customNavStyle: NavStyle = .clear
	override var isNavigationBarNeedHidden: Bool { false }
	//cell1
	@IBOutlet weak var daysLab: UILabel!
	@IBOutlet weak var meIconBGView: UIView!
	@IBOutlet weak var meIconImageView: UIImageView!
	@IBOutlet weak var taIconBGView: UIView!
	@IBOutlet weak var taIconImageView: UIImageView!
	@IBOutlet weak var meNameLab: UILabel!
	@IBOutlet weak var taNameLab: UILabel!
	//cell2
	@IBOutlet weak var topbqBGView: UIView!
	@IBOutlet weak var topbqLab: UILabel!
	@IBOutlet weak var leftStarImageView: UIImageView!
	@IBOutlet weak var vipSmLab: UILabel!
	@IBOutlet weak var rightStarImageView: UIImageView!
	@IBOutlet weak var saleLab: UILabel!
	@IBOutlet weak var vipButton: UIButton!
	@IBOutlet weak var barrarBGView: UIView!
	
	var bannerImages = ["data实时状态","dataVIP装扮","data手机使用","data奖励"]
	
	var vipInfoModel:YLVipInfoModel? {
		didSet {
			laodUIData()
		}
	}
	
	lazy var defaultCycleView: ICycleView = {
		let cycleView = ICycleView(frame: CGRectMake(0, 0, 183, 122))
		cycleView.pageIndicatorTintColor = UIColor.clear
		cycleView.currentPageIndicatorTintColor = UIColor.clear
		cycleView.backgroundColor = .clear
		cycleView.imgViewWidth = 183
		cycleView.autoScrollDelay = 5
		cycleView.delegate = self
		cycleView.pictures = bannerImages
		cycleView.register([UINib(nibName: "GSVipBannerCell", bundle: nil)], identifiers: ["GSVipBannerCell"])
		return cycleView
	}()
	
	//桃心动效
    override func viewDidLoad() {
        super.viewDidLoad()
		
		configUI()
		
		NotificationCenter.default.addObserver(self, selector: #selector(laodUIData), name: .vipStatusChangeNotification, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(getVipInfo), name: .refreshPersonNotification, object: nil)
		
		getVipInfo()
		laodUIData()
    }
	
	func configUI() {
		barrarBGView.addSubview(defaultCycleView)
	}
	
	@objc func laodUIData() {
		
		let user = UserData.shared
		taIconImageView.sd_setImage(with: URL(string: user.avatarOther), placeholderImage: user.avatarOtherPlaceholderImage, options: [.retryFailed, .scaleDownLargeImages])
		meIconImageView.sd_setImage(with: URL(string: user.avatar), placeholderImage: user.avatarPlaceholderImage, options: [.retryFailed, .scaleDownLargeImages])
		
		meIconBGView.layer.borderWidth = 2
		meIconBGView.layer.borderColor = UserData.shared.gender == .male ? UIColor.hexStringColor(hexString: "#9FD5FF").cgColor : UIColor.hexStringColor(hexString: "#FFB6C7").cgColor
		taIconBGView.layer.borderWidth = 2
		taIconBGView.layer.borderColor = UserData.shared.genderOther == .male ? UIColor.hexStringColor(hexString: "#9FD5FF").cgColor : UIColor.hexStringColor(hexString: "#FFB6C7").cgColor
		meNameLab.text = user.nickName
		taNameLab.text = user.nickNameOther
		
		let beginDate = Date.datefromString(string: UserData.shared.loveTime, newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
		let day = (beginDate.distanceDayCount(otherDay: Date()) ?? 0) + UserData.shared.loveTimeAddDay
		daysLab.text = "\(day)"
		
		leftStarImageView.isHidden = user.isVip
		rightStarImageView.isHidden = user.isVip
		vipButton.setTitle(user.isVip ? "person_view_benefit".localized : "person_vip_claim_now".localized, for: .normal)
		if user.isVip {
			topbqBGView.backgroundColor = UIColor.hexStringColor(hexString: "#423C2E")
			let typeVip: String
			switch vipInfoModel?.vipType {
			case .monthType: //月
				typeVip = "month_vip".localized
			case .yearType: //年
				typeVip = "year_vip".localized
			case .tryPayVipYearType: //后段说是周，跟原来不一样
				typeVip = "week_vip".localized
			case .nAddTryVipType: //新增适用
				typeVip = "try_vip".localized
			default:
				typeVip = "map_phone_unknown".localized
			}
			topbqLab.text = typeVip
			vipSmLab.textColor = UIColor.hexStringColor(hexString: "#774B41")
			vipSmLab.font = .systemFont(ofSize: 12)
			vipSmLab.text = "person_vip_valid_until".localized
			saleLab.font = .systemFont(ofSize: 12)
			saleLab.textColor = UIColor.hexStringColor(hexString: "#774B41")
			
			saleLab.text = Date.flStringfromDate(expireDate: vipInfoModel?.expireTime ?? Date(), newformatter: "dd.MM.yyyy")
		} else {
			topbqBGView.backgroundColor = UIColor.hexStringColor(hexString: "#FEF5AA")
			topbqLab.text = "person_vip_time_limit".localized
			vipSmLab.textColor = UIColor.hexStringColor(hexString: "#FF693F")
			vipSmLab.font = .systemFont(ofSize: 14)
			
			vipSmLab.text = "person_go_vip".localized
			saleLab.textColor = UIColor.hexStringColor(hexString: "#F68CB3")
			saleLab.font = .systemFont(ofSize: 20)
			let stayStr = localizedString(key: "person_vip_sale", substitutions: ["1$" : "90"])
			saleLab.text = stayStr
			saleLab.attributedText = stayStr.yl.attributedStringWith(strings: ["90%"], fonts: [UIFont.systemFont(ofSize: 20, weight: .medium)], colors: [UIColor.hexStringColor(hexString: "#583938")])
		}
		
		
	}
	
	@objc func getVipInfo() {
		UserData.shared.getVipInfo { isVip, openType, error in
			self.vipInfoModel = UserData.shared.vipInfo
		}
	}
	// MARK: - cell1
	@IBAction func avatarTap(_ sender: Any) {
		let vc = YLLoveInfoVC()
		vc.from_ui_path = "个人中心"
		navigationController?.pushViewController(vc, animated: true)
	}
	
	// MARK: - cell2
	@IBAction func buyVipButtonAction(_ sender: Any) {
		var type = "会员中心"
		if UserData.shared.isVip {
			if UserData.shared.vipInfo.vipType == .tryThreeType || UserData.shared.vipInfo.vipType == .trySevenType {
				var source = "个人中心"
				switch UserData.shared.vipInfo.vipType {
				case .tryThreeType:
					source = "试用3天"
					break
				case .trySevenType:
					source = "试用7天"
					break
				default:
					break
				}
				Navigator.shared.open(Navigator.vipPurchase + "?lang=\(YLLocalizationManager.shared.ylLocalizedStringC("YL_webLanguage"))&source=\(source)")
				type = "\(source)解锁正式会员"
			} else {
				Navigator.shared.open(Navigator.vipCenter)
			}
		} else {
			Navigator.shared.open(Navigator.vipPurchase + "?lang=\(YLLocalizationManager.shared.ylLocalizedStringC("YL_webLanguage"))&source=个人中心")
			type = "立即开通"
			YLStatisticsHelper.trackEventClick(dic: ["element_path":"个人中心-会员入口"])
		}
		
	}
	
	// MARK: - cell5
	@IBAction func insAction(_ sender: Any) {
		let appURL =  URL(string: "instagram://user?username=couple2_space")!
		let fallbackURL = URL(string: "https://apps.apple.com/cn/app/instagram/id389801252")!
		UIApplication.shared.open(appURL, options: [:]) { success in
			if !success {
				UIApplication.shared.open(fallbackURL, options: [:], completionHandler: nil)
			}
		}
	}
	
	@IBAction func tiktokAction(_ sender: Any) {
		let appURL = URL(string: "snssdk1233://@couple2_space")!
		let fallbackURL = URL(string: "https://apps.apple.com/us/app/tiktok/id835599320")!
		UIApplication.shared.open(appURL, options: [:]) { success in
			if !success {
				UIApplication.shared.open(fallbackURL, options: [:], completionHandler: nil)
			}
		}
	}
	// MARK: - cell3
	@IBAction func musicTap(_ sender: Any) {
		let vc = YLSoundEffectsViewController()
		navigationController?.pushViewController(vc, animated: true)
	}
	
	@IBAction func languageTap(_ sender: Any) {
		Navigator.shared.open(Navigator.language)
	}
	
	@IBAction func acountTap(_ sender: Any) {
		let vc = YLAccountRelationshipViewController()
		navigationController?.pushViewController(vc, animated: true)
	}
	
	// MARK: - cell4
	@IBAction func weightTap(_ sender: Any) {
		Navigator.shared.open(Navigator.widget)
	}
	//意见反馈
	@IBAction func feekTap(_ sender: Any) {

		let email = "<EMAIL>"
		let subject = ""

		// 收集系统信息
		let device = UIDevice.current
		let systemVersion = device.systemVersion
		let model = device.model
		let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
		let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? ""
		let userID = UserData.shared.userId
		let pairID = UserData.shared.pairId
		let region = Locale.current.regionCode ?? "Unknown"
		let network = UserData.shared.netType.desc

		let bodyText = """
		\("email_feedback_default_text1".localized)

		PairID: \(pairID ?? 0)
		UserID: \(userID)
		Model: \(model)
		OS version: \(systemVersion)
		APP version: \(appVersion) (\(build))
		Network: \(network)
		Region: \(region)
		\("email_feedback_default_text2".localized)
		"""

		if MFMailComposeViewController.canSendMail() {
			let mailVC = MFMailComposeViewController()
			mailVC.setToRecipients([email])
			mailVC.setSubject(subject)
			mailVC.setMessageBody(bodyText, isHTML: false)
			present(mailVC, animated: true, completion: nil)
		} else {
			// mailto fallback
			let bodyEncoded = bodyText.replacingOccurrences(of: "\n", with: "%0A")
			let subjectEncoded = subject.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
			let bodyFinalEncoded = bodyEncoded.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

			let urlString = "mailto:\(email)?subject=\(subjectEncoded)&body=\(bodyFinalEncoded)"

			if let url = URL(string: urlString), UIApplication.shared.canOpenURL(url) {
				UIApplication.shared.open(url)
			} else {
				AppDelegate.shared?.window?.yl_makeToast("sorry".localized, position: .center)
			}
		}
		
	}
	
	@IBAction func abputTap(_ sender: Any) {
		Navigator.shared.open(Navigator.aboutUs)
		
	}
	@IBAction func goodTap(_ sender: Any) {
		YLPositiveReviewPopShowManager.shared.protectionPOPAction()
	}
	
}

extension YLNewPersonViewController: ICycleViewDelegate {

	func iCycleView(scrollViewDidScroll cycleView: ICycleView) {
		if let cell = cycleView.collectionView.visibleCells.first as? GSVipBannerCell {
			if cell.animationView.isAnimationPlaying {
				cell.animationView.stop()
			}
		}
	}
	
	func iCycleView(scrollViewDidEndScroll cycleView: ICycleView) {
		if let cell = cycleView.collectionView.visibleCells.first as? GSVipBannerCell{
			cell.animationView.play()
		}
	}
		// 自定义Cell
	func iCycleView(cycleView: ICycleView, collectionView: UICollectionView, cellForItemAt indexPath: IndexPath, picture: String) -> UICollectionViewCell {
		let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GSVipBannerCell", for: indexPath) as! GSVipBannerCell
		cell.name = bannerImages[indexPath.row]
		cell.backgroundColor = .clear
		cell.contentBgView.backgroundColor = .clear
		return cell
	}
		
}
