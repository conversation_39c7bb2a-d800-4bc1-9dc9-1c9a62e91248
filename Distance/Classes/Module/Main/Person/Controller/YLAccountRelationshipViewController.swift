//
//  YLAccountRelationshipViewController.swift
//  Distance
//
//  Created by Q on 2025/7/18.
//

import UIKit
import URLNavigator

class YLAccountRelationshipViewController: DTBaseViewController , CustomNavigation {
	var customNavStyle: NavStyle = .clear

    override func viewDidLoad() {
        super.viewDidLoad()

		title = "person_account".localized
    }

	@IBAction func unmatchTap(_ sender: Any) {
		YLStatisticsHelper.trackEvent("Set.CK", dic: ["pt":"解除匹配"])
		Navigator.shared.open(Navigator.umMatch)
	}
	
	@IBAction func cancelaccountTap(_ sender: Any) {
		//注销账号
		
		//清数据
		YLPositiveReviewPopShowManager.shared.loadCSHData = false
		YLPositiveReviewPopShowManager.shared.clearUserData()
		
		YLStatisticsHelper.trackEvent("Set.CK", dic: ["pt":"账号注销"])
		let controller = YLWebViewController(urlString: YLServerUrlCenter.shared.numberCancelUrl, webTitle: "account_log_off".localized)
		let image = UIImageView.init(frame: CGRect.init(x: 0, y: 0, width: kScreenWidth, height: kScreenHeight))
		image.image = UIImage(named: "loveInfoBg")
		controller.view.insertSubview(image, at: 0)
		navigationController?.pushViewController(controller, animated: true)
	}
	
	@IBAction func loginOutAction(_ sender: Any) {
		YLLoginStatusManager.saveUserInformation(userName: UserData.shared.nickName, userIcon: UserData.shared.avatar, userGender: UserData.shared.gender.rawValue)
		
		YLStatisticsHelper.trackEvent("Set.CK", dic: ["pt":"退出登录"])
		YLStatisticsHelper.trackEvent("Setting.Logout.CK")
		YLStatisticsHelper.trackEventOnlyOnce("Setting.Logout.Alert.IM")
		YLStatisticsHelper.trackEventByShushu("logout", dic: ["action":"确认弹窗展示"])
		let alertController = YLAlertViewController.alertController(title: "notification".localized, message: "person_center_logout_sure".localized)
		let cancelAction = YLAlertAction(title: "think_again".localized, style: .default) { action in
			YLStatisticsHelper.trackEvent("Setting.Logout.Alert.CK", dic: ["action": "取消登出"])
			YLStatisticsHelper.trackEventByShushu("logout", dic: ["action":"再想想"])
		}
		let confirmAction = YLAlertAction(title: "confirm".localized, style: .cancel) { [weak self ] (action) in
			guard let self = self else {
				return
			}
			YLStatisticsHelper.trackEventByShushu("logout", dic: ["action":"确定登出"])
			YLStatisticsHelper.trackEvent("Setting.Logout.Alert.CK", dic: ["action": "登出成功"])
			YLStatisticsHelper.trackEvent("Setting.Logout.Alert.CK", dic: ["action": "confirm"])
			YLHUDView.showLoading(view: self.view)
			UserData.shared.logout(closure: { [weak self] isSuccess in
				guard let self = self else { return }
				YLHUDView.dismiss(view: self.view)
				YLPetManager.default.clearPetInfo()
				getVC(vc: YLHomeViewController.self)?.cancelPreviousFunc()
				let vc = Storyboard.Login.instantiate(YLLoginViewController.self)
				let nv = YLNavigationViewController(navigationBarClass: YLNavigationBar.self, toolbarClass: nil)
				nv.setViewControllers([vc], animated: false)
				AppDelegate.shared?.window?.rootViewController = nv
				//桌面传图还原默认图片
				YLWidgetDeskPhotoData.shared.cleaInfo()
				MNPlayer.removeDiyMusic()
				
			})
		}
		alertController.add(confirmAction)
		alertController.add(cancelAction)
		alertController.isTapBackgroundToDismissEnabled = false
		alertController.show(from: self)
	}
}
