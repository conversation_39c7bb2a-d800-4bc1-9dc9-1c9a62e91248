// Decodable+Helper.swift
import Foundation

extension Data {
	func decode<T: Decodable>(_ type: T.Type) -> T? {
		return try? JSONDecoder().decode(type, from: self)
	}
}

extension String {
	func emojiFromCodePoint() -> String {
		// 支持多个 code point 以空格分隔，或者只处理单个
		let parts = self.split(separator: ",").map { String($0) }
		let scalars = parts.compactMap { part -> UnicodeScalar? in
			guard let codePoint = UInt32(part, radix: 16) else { return nil }
			return UnicodeScalar(codePoint)
		}
		guard !scalars.isEmpty else { return "?" }
		return String(String.UnicodeScalarView(scalars))
	}
}
