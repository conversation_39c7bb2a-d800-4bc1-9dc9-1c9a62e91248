//
//  EmojiCategory.swift
//  Distance
//
//  Created by ke wen on 7/17/25.
//


struct EmojiCategory: Decodable {
	let type: String // Corresponds to the "title" in the UI (e.g., "表情")
	let emojis: [String]
	let typeImgUrl: String
	// Custom coding keys if "type" in JSON is different from "title" in UI
	// private enum CodingKeys: String, CodingKey {
	//     case type = "typeNameFromJson" // Example
	//     case emojis
	// }
}

// MARK: - Delegate Protocol

protocol YLEmojiPickerDelegate: AnyObject {
	func emojiPicker(_ picker: YLEmojiPickerViewController, didSelect emoji: String)
}
