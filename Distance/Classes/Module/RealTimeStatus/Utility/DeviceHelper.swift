//
//  DeviceHelper.swift
//  Distance
//
//  Created by ke wen on 7/24/25.
//


struct DeviceHelper {
    
    /// 判断是否为小屏手机（宽度小于等于 375pt，高度小于等于 667pt）
    static var isSmallScreen: Bool {
        let screenSize = UIScreen.main.bounds.size
        let width = min(screenSize.width, screenSize.height)
        let height = max(screenSize.width, screenSize.height)
        return width <= 375 && height <= 667
    }
}
