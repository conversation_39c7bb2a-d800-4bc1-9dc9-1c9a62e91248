//
//  YLEmojiCell.swift
//  Distance
//
//  Created by ke wen on 7/16/25.
//


// MARK: - Emoji Cell
final class YLEmojiCell: UICollectionViewCell {
	static let id = "YLEmojiCell"
	
	private let highlightedBackgroundColor = UIColor(red: 0.98, green: 0.918, blue: 0.773, alpha: 1)
	
	let label = UILabel()
	
	let centerMask: UIImageView = {
		let v = UIImageView()
		v.image = UIImage(named: "RealTimeStatus_main_lightcircle")
		v.layer.cornerRadius = 32
		v.isUserInteractionEnabled = false
		return v
	}()
	
	private var canHighlight: Bool = true
	
	override init(frame: CGRect) {
		super.init(frame: frame)
		contentView.addSubview(centerMask)
		centerMask.snp.makeConstraints {
			$0.size.equalTo(64)
			$0.center.equalToSuperview()
		}
		self.centerMask.alpha = 0
		label.font = .systemFont(ofSize: 36)
		label.textAlignment = .center
		contentView.addSubview(label)
		label.snp.makeConstraints { $0.edges.equalToSuperview() }
	}
	required init?(coder: NSCoder) { fatalError() }
	
	func bind(_ e: String, selected: Bool) {
		label.text = e.emojiFromCodePoint()
		self.centerMask.alpha = selected ?  1 : 0
		label.transform = selected ? CGAffineTransform(scaleX: 1.1, y: 1.1) : .identity
		canHighlight = false
	}
	
	// --- 关键：重写 isHighlighted 属性 ---
	override var isHighlighted: Bool {
		didSet {
			if !canHighlight {return}
			if isHighlighted {
				contentView.backgroundColor = highlightedBackgroundColor
			} else {
				// When highlight ends, revert to normal state
				contentView.backgroundColor = .clear
			}
		}
	}
}
