//
//  AvatarToggleView.swift
//  Distance
//
//  Created by ke wen on 7/16/25.
//


// MARK: - 头像切换视图
final class YLAvatarToggleView: UIView {
	
	enum Role { case mine, partner }
	var current: Role = .mine {
		didSet { updateUI() }
	}
	var onSelect: ((Role) -> Void)?
	
	private let mineBtn  = UIButton(type: .custom)
	private let peerBtn  = UIButton(type: .custom)
	private let mineBorder = UIView()
	private let peerBorder = UIView()
	private let borderW: CGFloat = 2
	private let outerBorderW: CGFloat = 5
	
	init() {
		super.init(frame: .zero)
		setupButtons()
		setupBorders()
		setupLayout()
		updateUI()
	}
	
	private func setupButtons() {
		[mineBtn, peerBtn].forEach {
			$0.layer.borderWidth = borderW
			$0.layer.masksToBounds = true
			$0.addTarget(self, action: #selector(tap(_:)), for: .touchUpInside)
		}
	}
	
	private func setupBorders() {
		[mineBorder, peerBorder].forEach {
			$0.layer.borderWidth = outerBorderW
			$0.backgroundColor = .clear
		}
	}
	
	private func setupLayout() {
		addSubview(mineBorder)
		addSubview(peerBorder)
		addSubview(mineBtn)
		addSubview(peerBtn)
		self.backgroundColor = UIColor(0xF8E3AD)
		
		mineBtn.snp.makeConstraints {
			$0.left.top.bottom.equalToSuperview().inset(6)
			$0.width.equalTo(mineBtn.snp.height)
		}
		peerBtn.snp.makeConstraints {
			$0.left.equalTo(mineBtn.snp.right).offset(26);
			$0.right.top.bottom.equalToSuperview().inset(6)
			$0.width.equalTo(peerBtn.snp.height)
		}
		
		mineBorder.snp.makeConstraints {
			$0.center.equalTo(mineBtn)
			$0.width.height.equalTo(mineBtn).offset(borderW*2 + outerBorderW)
		}
		
		peerBorder.snp.makeConstraints {
			$0.center.equalTo(peerBtn)
			$0.width.height.equalTo(peerBtn).offset(borderW*2 + outerBorderW)
		}
		
		// 在布局完成后设置圆角
		DispatchQueue.main.async {
			self.updateCornerRadius()
		}
	}
	
	private func updateCornerRadius() {
		// 设置按钮圆角为高度的一半
		mineBtn.layer.cornerRadius = mineBtn.frame.height / 2
		peerBtn.layer.cornerRadius = peerBtn.frame.height / 2
		
		// 设置外边框圆角
		mineBorder.layer.cornerRadius = mineBorder.frame.height / 2
		peerBorder.layer.cornerRadius = peerBorder.frame.height / 2
		
		self.layer.cornerRadius = self.frame.height / 2
	}
	
	override func layoutSubviews() {
		super.layoutSubviews()
		updateCornerRadius()
	}
	
	required init?(coder: NSCoder) { fatalError() }
	
	func config(mineURL: String, peerURL: String) {
		mineBtn.sd_setImage(with: URL(string: mineURL), for: .normal, completed: nil)
		peerBtn.sd_setImage(with: URL(string: peerURL), for: .normal, completed: nil)
	}
	
	@objc private func tap(_ s: UIButton) {
		current = (s === mineBtn) ? .mine : .partner
		onSelect?(current)
	}
	
	private func updateUI() {
		mineBtn.layer.borderColor  = (current == .mine)     ? UIColor.white.cgColor : UIColor.clear.cgColor
		peerBtn.layer.borderColor  = (current == .partner)  ? UIColor.white.cgColor : UIColor.clear.cgColor
		
		mineBorder.layer.borderColor = (current == .mine)    ? UIColor(0xF7BD22).cgColor : UIColor.clear.cgColor
		peerBorder.layer.borderColor = (current == .partner) ? UIColor(0xF7BD22).cgColor : UIColor.clear.cgColor
	}
}
