//
//  AddEmojiHeaderView.swift
//  Distance
//

import UIKit

class YLAddEmojiHeaderView: UICollectionReusableView {
	static let reuseIdentifier = "AddEmojiHeaderView"
	
	// 点击回调
	var onAddButtonTapped: (() -> Void)?
	
	// 加号按钮
	private lazy var addButton: UIButton = {
		let button = UIButton(type: .custom)
		button.setImage(UIImage(named: "RealTimeStatus_main_add"), for: .normal)
		button.contentVerticalAlignment = .fill
		button.contentHorizontalAlignment = .fill
		button.imageView?.contentMode = .scaleAspectFit
		button.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)
		return button
	}()
	
	override init(frame: CGRect) {
		super.init(frame: frame)
		setupUI()
	}
	
	required init?(coder: NSCoder) {
		super.init(coder: coder)
		setupUI()
	}
	
	private func setupUI() {
		backgroundColor = .clear
		
		addSubview(addButton)
		addButton.snp.makeConstraints { make in
			make.centerY.equalToSuperview()
			make.right.equalToSuperview().inset(12) // 右边留一点小间距
			make.width.height.equalTo(42)
		}
	}
	
	
	@objc private func addButtonTapped() {
		onAddButtonTapped?()
	}
}
