//
//  YLEmojiSectionHeaderView.swift
//  Distance
//
//  Created by ke wen on 7/17/25.
//


// Header for emoji sections
final class YLEmojiSectionHeaderView: UICollectionReusableView {
	static let reuseIdentifier = "YLEmojiSectionHeaderView"
	
	let titleLabel: UILabel = {
		let label = UILabel()
		label.font = .systemFont(ofSize: 15, weight: .regular)
		label.textColor = UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 0.7)
		return label
	}()
	
	override init(frame: CGRect) {
		super.init(frame: frame)
		addSubview(titleLabel)
		titleLabel.snp.makeConstraints { make in
			make.leading.equalToSuperview().offset(0) // Match section inset
			make.trailing.equalToSuperview().offset(0)
			make.centerY.equalToSuperview()
		}
	}
	
	required init?(coder: NSCoder) {
		fatalError("init(coder:) has not been implemented")
	}
}
