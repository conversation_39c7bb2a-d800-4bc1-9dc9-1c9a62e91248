import UIKit
import SnapKit
import Lottie

/// 没有状态时展示的卡片
final class YLStatusEmptyView: UIView {
	
	/// 类型（用于控制文案 / 动画等）
	var type: YLAvatarToggleView.Role = .mine {
		didSet {
			updateContentForType()
		}
	}
	
	var onAddTapped: (() -> Void)?
	
	private let titleLabel = UILabel()
	private let subTitleLabel = UILabel()
	private let addButton = UIButton(type: .system)
	
	private let animationView = LottieAnimationView()
	
	override init(frame: CGRect) {
		super.init(frame: frame)
		buildUI()
		updateContentForType()
	}
	
	required init?(coder: NSCoder) {
		super.init(coder: coder)
		buildUI()
		updateContentForType()
	}
	
	
	private func buildUI() {
		backgroundColor = .clear
		
		titleLabel.font = .systemFont(ofSize: 20, weight: .medium)
		titleLabel.textColor = UIColor(red: 0.466, green: 0.294, blue: 0.254, alpha: 1)
		titleLabel.textAlignment = .center
		titleLabel.numberOfLines = 0
		addSubview(titleLabel)
		titleLabel.snp.makeConstraints {
			$0.top.equalToSuperview().offset(50)
			$0.left.right.equalToSuperview().inset(16)
			$0.centerX.equalToSuperview()
		}
		
		subTitleLabel.font = .systemFont(ofSize: 14, weight: .medium)
		subTitleLabel.textColor = UIColor(red: 0.18, green: 0.176, blue: 0.208, alpha: 0.4)
		subTitleLabel.textAlignment = .center
		subTitleLabel.numberOfLines = 0
		addSubview(subTitleLabel)
		subTitleLabel.snp.makeConstraints {
			$0.top.equalTo(titleLabel.snp.bottom).offset(4)
			$0.left.right.equalToSuperview().inset(16)
			$0.centerX.equalToSuperview()
		}
		
		animationView.loopMode = .loop
		animationView.contentMode = .scaleAspectFit
		addSubview(animationView)
		animationView.snp.makeConstraints {
			$0.centerX.equalToSuperview()
			$0.top.equalTo(subTitleLabel.snp.bottom).offset(100)
		
			if DeviceHelper.isSmallScreen {
				$0.width.height.equalTo(180)
			}else {
				$0.width.height.equalTo(260)
			}
		}
		
		addButton.setTitleColor(UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 1), for: .normal)
		addButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
		addButton.backgroundColor = UIColor(red: 0.992, green: 0.89, blue: 0.514, alpha: 1)
		addButton.layer.cornerRadius = 24
		addSubview(addButton)
		addButton.snp.makeConstraints {
			$0.top.equalTo(animationView.snp.bottom).offset(64)
			$0.centerX.equalToSuperview()
			$0.height.equalTo(48)
			$0.left.right.equalToSuperview().inset(72)
		}
		addButton.addTarget(self, action: #selector(addAction), for: .touchUpInside)
	}
	
	private func updateContentForType() {
		switch type {
		case .mine:
			titleLabel.text = "status_now_action".localized
			subTitleLabel.text = "status_pair_share_tips".localized
			addButton.setTitle("status_add".localized, for: .normal)
			animationView.animation = LottieAnimation.named("empty_status_mine")
		case .partner:
			titleLabel.text = "status_self_no_yet".localized
			subTitleLabel.text = "status_pair_no_yet".localized
			addButton.setTitle("status_request_pair_update".localized, for: .normal)
			animationView.animation = LottieAnimation.named("empty_status_peer")
		}
		animationView.play()
	}
	
	@objc private func addAction() {
		onAddTapped?()
	}
}
