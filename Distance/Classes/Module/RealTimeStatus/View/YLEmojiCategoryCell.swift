//
//  YLEmojiCategoryCell.swift
//  Distance
//
//  Created by ke wen on 7/17/25.
//


final class YLEmojiCategoryCell: UICollectionViewCell {
	let imageView: UIImageView = {
		let iv = UIImageView()
		iv.contentMode = .scaleAspectFit
		iv.clipsToBounds = true
		return iv
	}()
	
	override var isSelected: Bool {
		didSet {
			contentView.backgroundColor = isSelected ?  UIColor(red: 0.965, green: 0.957, blue: 0.906, alpha: 1) : .clear
			contentView.layer.cornerRadius = 10
		}
	}
	
	override init(frame: CGRect) {
		super.init(frame: frame)
		contentView.addSubview(imageView)
		imageView.snp.makeConstraints {
			$0.edges.equalToSuperview()
		}
		contentView.backgroundColor = .clear
	}
	
	required init?(coder: NSCoder) {
		fatalError("init(coder:) has not been implemented")
	}
}
