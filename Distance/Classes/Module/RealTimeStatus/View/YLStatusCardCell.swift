//
//  StatusCardCell.swift
//  Distance
//
//  Created by ke wen on 7/16/25.
//

import SwiftDate


// MARK: - 卡片 Cell
final class YLStatusCardCell: UICollectionViewCell {
	
	static let id = "StatusCardCell"
	private let emojiLabel = UILabel()
	private let wordsLabel = UILabel()
	private let timeLabel  = UILabel()
	private let deleteBtn  = UIButton(type: .custom)
	
	var onDelete: (() -> Void)?
	
	override init(frame: CGRect) {
		super.init(frame: frame)
		self.layer.cornerRadius = 20
		self.layer.shadowColor = UIColor.black.cgColor
		self.layer.shadowOpacity = 0.1
		self.layer.shadowOffset = CGSize(width: 0, height: 2)
		self.layer.shadowRadius = 4
		self.backgroundColor = .white
		
		
		emojiLabel.font = .systemFont(ofSize: 120)
		emojiLabel.textAlignment = .center
		
		wordsLabel.font = .systemFont(ofSize: 16, weight: .medium)
		wordsLabel.textAlignment = .center
		wordsLabel.numberOfLines = 2
		wordsLabel.textColor = UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 1)
		
		timeLabel.font = .systemFont(ofSize: 14)
		timeLabel.textColor = UIColor(red: 0.18, green: 0.176, blue: 0.208, alpha: 0.4)
		timeLabel.textAlignment = .right
		
		deleteBtn.setImage(UIImage(named: "rt_status_delete"), for: .normal)
		deleteBtn.addTarget(self, action: #selector(del), for: .touchUpInside)
		
		contentView.addSubview(emojiLabel)
		contentView.addSubview(wordsLabel)
		contentView.addSubview(timeLabel)
		contentView.addSubview(deleteBtn)
		
		emojiLabel.snp.makeConstraints {
			$0.centerX.equalToSuperview()
			if DeviceHelper.isSmallScreen {
				$0.top.equalToSuperview().offset(30)
			}else {
				$0.top.equalToSuperview().offset(70)
			}
		
		}
		wordsLabel.snp.makeConstraints {
			
			if DeviceHelper.isSmallScreen {
				$0.top.equalTo(emojiLabel.snp.bottom).offset(50)
			}else {
				$0.top.equalTo(emojiLabel.snp.bottom).offset(80)
			}
			$0.left.right.equalToSuperview().inset(32)
		}
		
		
		let shadowImgView = UIImageView(image: UIImage(named: "RealTimeStatus_main_shadow"))
		contentView.addSubview(shadowImgView)
		shadowImgView.snp.makeConstraints {
			$0.centerX.equalToSuperview()
		
			if DeviceHelper.isSmallScreen {
				$0.top.equalTo(emojiLabel.snp.bottom).offset(18)
			}else {
				$0.top.equalTo(emojiLabel.snp.bottom).offset(28)
			}
		}
		
		let lineView = UIView()
		lineView.backgroundColor = UIColor(0xF5F5F5)
		contentView.addSubview(lineView)
		lineView.snp.makeConstraints {
			$0.left.right.equalToSuperview()
			$0.bottom.equalToSuperview().offset(-55)
			$0.height.equalTo(1)
		}
		
		
		timeLabel.snp.makeConstraints  {
			$0.right.equalToSuperview().inset(20)
			$0.bottom.equalToSuperview().inset(18)
			$0.height.equalTo(20)
		}
		deleteBtn.snp.makeConstraints  { $0.top.right.equalToSuperview().inset(12); $0.size.equalTo(28) }
	}
	
	required init?(coder: NSCoder) { fatalError() }
	
	func bind(_ m: YLStatusModel, isMine: Bool) {
		emojiLabel.text = m.emoji.emojiFromCodePoint()
		wordsLabel.text = m.text
		let date = Date(timeIntervalSinceNow: -m.timestamp)
		if m.timestamp < 60 * 5 {
			timeLabel.text = "just_now".localized
		}else {
			let lannguage = YLLocalizationManager.shared.currentLanguage
			if lannguage.contains("en") {
				timeLabel.text = format(minutes: m.timestamp)
			} else {
				let customStyle = RelativeFormatter.Style(
					flavours: [.short],
					gradation: .canonical(),
					allowedUnits: [.minute, .hour]
				)
				
				timeLabel.text = date.toRelative(style: customStyle,locale: Locales(rawValue: YLLocalizationManager.shared.currentLanguage))
			}
			
		}
		
		deleteBtn.isHidden = !isMine
	}
	
	@objc private func del() { onDelete?() }
	
	private func format(minutes: TimeInterval) -> String {
		let diff = Int(minutes)
		let min = diff / 60
		let hour = min / 60
		switch hour {
		case 0:  return "\(min)min ago"
		default: return "\(hour)h \(min%60)min ago"
		}
	}
	
}
