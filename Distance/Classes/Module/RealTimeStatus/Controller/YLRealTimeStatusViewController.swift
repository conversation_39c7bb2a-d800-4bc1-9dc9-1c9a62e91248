//
//  YLRealTimeStatusViewController.swift
//  Distance
//

import UIKit
import SnapKit
import SwiftyJSON

final class YLRealTimeStatusViewController:  DTBaseViewController, CustomNavigation {
	
	var customNavStyle: NavStyle = .clear
	
	
	// MARK: 数据
	private var mineRecords   = [YLStatusModel]()
	private var peerRecords   = [YLStatusModel]()
	private var currentRole: YLAvatarToggleView.Role = .mine {
		didSet { reloadData() }
	}
	private var currentIndex: Int = 0
	private var isSyncing: Bool = false
	private weak var activeScrollView: UIScrollView?
	private var isSnapAnimating: Bool = false
	private let tapFeedbackGenerator = UISelectionFeedbackGenerator()
	
	private let emptyView = YLStatusEmptyView()
	
	// MARK: UI
	private let toggleView = YLAvatarToggleView()
	private let centerMask: UIImageView = {
		let v = UIImageView()
		v.isUserInteractionEnabled = false
		return v
	}()
	private lazy var cardCV: UICollectionView = {
		let l = UICollectionViewFlowLayout()
		l.scrollDirection = .horizontal
		l.minimumLineSpacing = 28
		l.sectionInset = .zero
		l.itemSize = CGSize(width: view.bounds.width - 80, height: DeviceHelper.isSmallScreen ? 339 : 419)
		l.sectionInset = UIEdgeInsets(top: 10, left: 40, bottom: 10, right: 40)
		let cv = UICollectionView(frame: .zero, collectionViewLayout: l)
		cv.isPagingEnabled = false
		cv.bounces = false
		cv.decelerationRate = .fast
		cv.showsHorizontalScrollIndicator = false
		cv.backgroundColor = .clear
		cv.register(YLStatusCardCell.self, forCellWithReuseIdentifier: YLStatusCardCell.id)
		cv.delegate = self
		cv.dataSource = self
		return cv
	}()
	private lazy var emojiCV: UICollectionView = {
		let l = UICollectionViewFlowLayout()
		l.scrollDirection = .horizontal
		l.minimumLineSpacing = 12
		l.itemSize = CGSize(width: 56, height: 56)
		let sideInset = (UIScreen.main.bounds.width / 2) - (56 / 2)
		
		l.headerReferenceSize = CGSize(width: sideInset, height: 56)
		l.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: sideInset)
		let cv = UICollectionView(frame: .zero, collectionViewLayout: l)
		cv.showsHorizontalScrollIndicator = false
		cv.backgroundColor = .clear
		cv.register(YLEmojiCell.self, forCellWithReuseIdentifier: YLEmojiCell.id)
		cv.register(YLAddEmojiHeaderView.self,
					forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
					withReuseIdentifier: YLAddEmojiHeaderView.reuseIdentifier)
		cv.delegate = self
		cv.dataSource = self
		return cv
	}()
	
	override func viewDidLoad() {
		super.viewDidLoad()
		view.backgroundColor = UIColor(named: "rt_bg") ?? .systemYellow.withAlphaComponent(0.05)
		layoutUI()
		loadData()
		layoutEmptyView()
		// 预热触觉引擎
		tapFeedbackGenerator.prepare()
		
		YLStatisticsHelper.trackEventByShushu("ui_launch",dic: ["ui_path":"实时状态"])
	}
	
	private func layoutEmptyView() {
		view.addSubview(emptyView)
		emptyView.snp.makeConstraints {
			$0.top.equalTo(toggleView.snp.bottom)
			$0.left.right.bottom.equalToSuperview()
		}
		emptyView.isHidden = true
		emptyView.onAddTapped = { [weak self] in
			self?.addStatusTapped()
		}
	}
	
	override func viewDidLayoutSubviews() {
		super.viewDidLayoutSubviews()
		// 确保在布局完成后调整emojiCV的布局，以保证边距正确
		//		let emojiCVlayout = emojiCV.collectionViewLayout as! UICollectionViewFlowLayout
		//		let sideInset = emojiCV.bounds.width / 2 - 28 // 28是emoji宽度的一半
		//		emojiCVlayout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: sideInset)
		//		emojiCV.collectionViewLayout.invalidateLayout()
		//
		//		let cardCVLayout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
		//		cardCVLayout.sectionInset = UIEdgeInsets(top: 0, left: 40, bottom: 0, right: 40)
		//		cardCV.collectionViewLayout.invalidateLayout()
	}
	
	private func layoutUI() {
		
		self.navigationItem.rightBarButtonItem = UIBarButtonItem(image:  #imageLiteral(resourceName: "questions_icon").withRenderingMode(.alwaysOriginal), style: .plain, target: self, action: #selector(rightBarItemClick(_:)))
		
		let bgImgV = UIImageView(image: UIImage(named: "RealTimeStatus_bg"))
		bgImgV.contentMode = .scaleAspectFill
		view.addSubview(bgImgV)
		bgImgV.snp.makeConstraints {
			$0.edges.equalToSuperview()
		}
		
		view.addSubview(toggleView)
		toggleView.snp.makeConstraints {
			$0.top.equalTo(view.safeAreaLayoutGuide.snp.top)
			$0.centerX.equalToSuperview()
			$0.height.equalTo(54)
		}
		toggleView.onSelect = { [weak self] role in self?.currentRole = role }
		
		view.addSubview(cardCV)
		cardCV.snp.makeConstraints {
			$0.top.equalTo(toggleView.snp.bottom).offset(48)
			$0.left.right.equalToSuperview()
			
			if DeviceHelper.isSmallScreen {
				$0.height.equalTo(359)
			}else {
				$0.height.equalTo(439)
			}
		}
		
		view.addSubview(emojiCV)
		emojiCV.snp.makeConstraints {
			$0.top.equalTo(cardCV.snp.bottom).offset(40)
			$0.left.right.equalToSuperview()
			$0.height.equalTo(72)
		}
		
		view.addSubview(centerMask)
		centerMask.snp.makeConstraints {
			$0.centerY.equalTo(emojiCV)
			$0.centerX.equalTo(emojiCV)
			$0.size.equalTo(CGSize(width: 64, height: 64))
		}
		view.bringSubviewToFront(emojiCV)
	}
	
	// MARK: Networking
	private func loadData() {
		showLoadingIndicator()
		statusServiceProvider.request(.pairStatus) { [weak self] res in
			guard let self else { return }
			self.hideLoadingIndicator()
			res.toJSONMapper(success: { json in
				self.handleDataLoadSuccess(json)
			}, failure: { err in
				self.handleDataLoadFailure(err)
			})
		}
	}

	private func showLoadingIndicator() {
		YLHUDView.showLoading(view: view)
	}

	private func hideLoadingIndicator() {
		YLHUDView.dismiss(view: self.view)
	}

	private func handleDataLoadSuccess(_ json: JSON) {
		self.mineRecords = json["data"]["selfRealRecords"].arrayValue.map { self.toModel($0, mine: true) }
		self.peerRecords = json["data"]["otherRealRecords"].arrayValue.map { self.toModel($0, mine: false) }
		self.configureAvatarImages(from: json)
		self.reloadData()
	}

	private func handleDataLoadFailure(_ error: Error) {
		self.view.makeToast(error.localizedDescription)
	}

	private func configureAvatarImages(from json: JSON) {
		self.toggleView.config(mineURL: json["data"]["selfAvatar"].stringValue,
							   peerURL: json["data"]["otherAvatar"].stringValue)
	}
	private func toModel(_ j: JSON, mine: Bool) -> YLStatusModel {
		return YLStatusModel(id: j["id"].stringValue,
							 emoji: j["unicode"].stringValue,
							 text: j["words"].string,
							 timestamp: j["duration"].doubleValue,
							 isMine: mine)
	}
	
	// MARK: helpers
	private var currentData: [YLStatusModel] {
		currentRole == .mine ? mineRecords : peerRecords
	}
	private func reloadData() {
		let isEmpty = currentData.isEmpty
		updateViewVisibility(isEmpty: isEmpty)
		if isEmpty {
			configureEmptyState()
		} else {
			reloadCollectionViews()
			resetToFirstItem()
		}
	}

	private func updateViewVisibility(isEmpty: Bool) {
		emptyView.isHidden = !isEmpty
		cardCV.isHidden = isEmpty
		emojiCV.isHidden = isEmpty
		centerMask.isHidden = isEmpty
	}

	private func configureEmptyState() {
		emptyView.type = currentRole
	}

	private func reloadCollectionViews() {
		cardCV.reloadData()
		emojiCV.reloadData()
	}

	private func resetToFirstItem() {
		syncScrollTo(index: 0, animated: false)
		currentIndex = 0
	}
	// 基于索引同步两个视图的位置
	private func syncScrollTo(index: Int, animated: Bool) {
		guard index < currentData.count else { return }
		// 防止重复设置相同索引导致重复动画
		if index == currentIndex { return }
		currentIndex = index
		
		// 设置同步标记，防止递归调用
		isSyncing = true
		
		let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
		let itemWidth = layout.itemSize.width + 28
		let cardX = CGFloat(index) * itemWidth
		cardCV.setContentOffset(CGPoint(x: cardX, y: 0), animated: animated)
		
		// emoji 吸附，总是无动画以提高性能
		if let attr = emojiCV.layoutAttributesForItem(at: IndexPath(item: index, section: 0)) {
			let offset = attr.center.x - emojiCV.bounds.width / 2
			emojiCV.setContentOffset(CGPoint(x: offset, y: 0), animated: false)
		}
		
		// 更新选中状态
		emojiCV.reloadData()
		
		// 解除同步标记
		isSyncing = false
	}
	
	// 使用比例映射实时同步两个视图的滚动
	private func sync(from source: UICollectionView, to target: UICollectionView) {
		// 计算可滚动范围
		let sourceScrollable = source.contentSize.width - source.bounds.width
		let targetScrollable = target.contentSize.width - target.bounds.width
		guard sourceScrollable > 0, targetScrollable > 0 else { return }
		
		// 计算滚动比例
		let ratio = targetScrollable / sourceScrollable
		let targetX = source.contentOffset.x * ratio
		
		// 边界检查，防止过度滚动
		let maxOffset = max(0, target.contentSize.width - target.bounds.width)
		let safeTargetX = min(maxOffset, max(0, targetX))
		
		// 设置同步标记，防止递归调用
		isSyncing = true
		// 无动画同步，确保实时跟随
		target.setContentOffset(CGPoint(x: safeTargetX, y: 0), animated: false)
		isSyncing = false
	}
	@objc func rightBarItemClick(_ sender: UIButton) {
		YLStatusWidgetIntroSheetVC.showAlert(from: self)
	}
	
	@objc private func addStatusTapped() {
		if currentRole == .partner {
			sendUpdateRequestToPartner()
		} else {
			handleAddEmojiTapped()
		}
	}

	private func sendUpdateRequestToPartner() {
		YLStatisticsHelper.trackEventByShushu("ui_click",dic: ["element_path":"实时状态-求更新"],isOnce: true)
		YLChatManager.shared.send(action: .text(string: "status_request_pair_update_text".localized)){ _ in
		} progress: { progress in } completion: {[weak self] eMessage, error in
			if let _ = error {
			}else {
				self?.view.yl_makeToast("status_request_sended".localized)
			}
		}
	}
}

// MARK: - UICollectionView Delegate/DataSource
extension YLRealTimeStatusViewController: UICollectionViewDataSource, UICollectionViewDelegate, UIScrollViewDelegate {
	
	func collectionView(_ cv: UICollectionView, numberOfItemsInSection section: Int) -> Int {
		currentData.count
	}
	
	// 提供header视图
	func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
		if kind == UICollectionView.elementKindSectionHeader && collectionView === emojiCV {
			let headerView = collectionView.dequeueReusableSupplementaryView(
				ofKind: kind,
				withReuseIdentifier: YLAddEmojiHeaderView.reuseIdentifier,
				for: indexPath) as! YLAddEmojiHeaderView
			
			// 设置点击回调
			headerView.onAddButtonTapped = { [weak self] in
				self?.handleAddEmojiTapped()
			}
			headerView.isHidden = currentRole == .partner
			return headerView
		}
		
		return UICollectionReusableView()
	}
	
	func collectionView(_ cv: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
		if cv === cardCV {
			return configureStatusCardCell(cv, at: indexPath)
		} else {
			return configureEmojiCell(cv, at: indexPath)
		}
	}

	private func configureStatusCardCell(_ collectionView: UICollectionView, at indexPath: IndexPath) -> YLStatusCardCell {
		let cell = collectionView.dequeueReusableCell(withReuseIdentifier: YLStatusCardCell.id, for: indexPath) as! YLStatusCardCell
		let model = currentData[indexPath.item]
		cell.bind(model, isMine: currentRole == .mine)
		cell.onDelete = { [weak self] in
			self?.showDeleteConfirmation(for: model, at: indexPath)
		}
		return cell
	}

	private func configureEmojiCell(_ collectionView: UICollectionView, at indexPath: IndexPath) -> YLEmojiCell {
		let cell = collectionView.dequeueReusableCell(withReuseIdentifier: YLEmojiCell.id, for: indexPath) as! YLEmojiCell
		cell.bind(currentData[indexPath.item].emoji, selected: isEmojiSelected(indexPath))
		return cell
	}

	private func showDeleteConfirmation(for model: YLStatusModel, at indexPath: IndexPath) {
		let alertController = YLAlertViewController.alertController(title: "notification".localized, message: "status_delete_sure_tips".localized)
		let cancelAction = YLAlertAction(title: "delete".localized, style: .default) { action in
			self.delete(record: model, at: indexPath)
		}
		let confirmAction = YLAlertAction(title: "cancel".localized, style: .cancel) { action in

		}
		alertController.add(confirmAction)
		alertController.add(cancelAction)
		if let topViewController = topViewController {
			alertController.show(from: topViewController)
		}
	}
	
	func collectionView(_ cv: UICollectionView, didSelectItemAt indexPath: IndexPath) {
		guard cv === emojiCV else { return }
		let centerIdx = getNearestEmojiIndex()
		if indexPath.item != centerIdx {
			handleEmojiSelection(at: indexPath)
		}
	}

	private func handleEmojiSelection(at indexPath: IndexPath) {
		isSnapAnimating = true
		currentIndex = indexPath.item

		provideTactileFeedback()
		animateEmojiToCenter(at: indexPath)
		animateCardToIndex(indexPath.item)
		emojiCV.reloadData()
	}

	private func provideTactileFeedback() {
		tapFeedbackGenerator.selectionChanged()
		tapFeedbackGenerator.prepare()
	}

	private func animateEmojiToCenter(at indexPath: IndexPath) {
		if let attr = emojiCV.layoutAttributesForItem(at: indexPath) {
			let targetX = attr.center.x - emojiCV.bounds.width / 2
			let maxOffset = max(0, emojiCV.contentSize.width - emojiCV.bounds.width)
			let safeTargetX = min(maxOffset, max(0, targetX))
			emojiCV.setContentOffset(CGPoint(x: safeTargetX, y: 0), animated: true)
		}
	}

	private func animateCardToIndex(_ index: Int) {
		let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
		let itemWidth = layout.itemSize.width + 28
		let targetX = CGFloat(index) * itemWidth
		cardCV.setContentOffset(CGPoint(x: targetX, y: 0), animated: true)
	}
	
	private func isEmojiSelected(_ idx: IndexPath) -> Bool {
		let mid = Int(round(cardCV.contentOffset.x / cardCV.bounds.width))
		return idx.item == mid
	}
	
	func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
		// 记录当前被拖动的滚动视图
		activeScrollView = scrollView
	}
	
	// 用户抬起手指时，立即终止滚动并定位到最近的emoji
	func scrollViewWillEndDragging(_ scrollView: UIScrollView, withVelocity velocity: CGPoint, targetContentOffset: UnsafeMutablePointer<CGPoint>) {
		if scrollView === emojiCV {
			// 取消惯性滚动，计算当前最近的emoji
			if let nearest = getNearestEmojiIndex() {
				// 计算emoji的目标位置
				if let attr = emojiCV.layoutAttributesForItem(at: IndexPath(item: nearest, section: 0)) {
					let targetX = attr.center.x - emojiCV.bounds.width / 2
					
					// 边界检查
					let maxOffset = max(0, emojiCV.contentSize.width - emojiCV.bounds.width)
					let safeTargetX = min(maxOffset, max(0, targetX))
					
					// 直接设置目标位置，取消惯性滚动
					targetContentOffset.pointee.x = safeTargetX
					
					// 如果索引变化，提供触觉反馈
					
					
					// 更新当前索引
					currentIndex = nearest
				}
			}
		} else if scrollView === cardCV {
			// 计算目标页码
			let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
			let itemWidth = layout.itemSize.width + 28
			let idx = Int(round(targetContentOffset.pointee.x / itemWidth))
			
			// 如果索引变化，提供触觉反馈
			currentIndex = idx
		}
	}
	
	func scrollViewDidScroll(_ scrollView: UIScrollView) {
		// 防止递归调用和动画过程中的干扰
		guard !isSyncing && !isSnapAnimating else { return }
		
		// 主动链路: 卡片滚动→驱动Emoji
		if scrollView === cardCV && activeScrollView === cardCV {
			// 实时同步滚动
			sync(from: cardCV, to: emojiCV)
			
			// 顺便计算当前索引（用于状态更新）
			let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
			let itemWidth = layout.itemSize.width + 28
			let idx = Int(round(scrollView.contentOffset.x / itemWidth))
			
			// 只在索引变化时更新状态
			if idx != currentIndex {
				currentIndex = idx
				emojiCV.reloadData() // 更新选中状态
			}
			
			// 始终更新缩放效果
			updateEmojiZoomEffect()
			
			// 检查emoji是否居中并提供触觉反馈
			checkForCenteredEmojiAndProvideFeedback()
		}
		// 主动链路: Emoji滚动→驱动卡片
		else if scrollView === emojiCV && activeScrollView === emojiCV {
			// 实时同步滚动
			sync(from: emojiCV, to: cardCV)
			
			// 更新缩放效果
			updateEmojiZoomEffect()
			
			// 更新当前索引（用于状态更新）
			// 因为实时同步后，cardCV也跟着滚动了，所以直接用cardCV计算索引
			let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
			let itemWidth = layout.itemSize.width + 28
			let idx = Int(round(cardCV.contentOffset.x / itemWidth))
			
			// 只在索引变化时更新状态
			if idx != currentIndex {
				currentIndex = idx
				emojiCV.reloadData() // 更新选中状态
			}
			
			// 检查emoji是否居中并提供触觉反馈
			checkForCenteredEmojiAndProvideFeedback()
		}
	}
	
	
	
	// 动画结束后的回调
	func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
		// 动画真正结束，解除同步锁
		isSnapAnimating = false
		if scrollView === emojiCV {
			updateEmojiZoomEffect()
		}
		
	}
	
	func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
		if !decelerate {
			if scrollView === emojiCV {
				// emoji已经在willEndDragging中处理了目标位置，这里只同步卡片
				// 卡片跟随带动画滚动
				if let nearest = getNearestEmojiIndex() {
					let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
					let itemWidth = layout.itemSize.width + 28
					let targetX = CGFloat(nearest) * itemWidth
					
					// 设置标记防止在动画过程中被干扰
					isSnapAnimating = true
					cardCV.setContentOffset(CGPoint(x: targetX, y: 0), animated: true)
					emojiCV.reloadData() // 更新选中状态
				}
			} else if scrollView === cardCV {
				// 卡片滚动结束且不会减速，执行最终对齐（带动画）
				snapCardToPageWithAnimation()
			}
			
			// 滚动结束后重置活动滚动视图
			activeScrollView = nil
		}
	}
	
	// 卡片滚动结束后，带动画对齐到整页，并同步emoji（带动画）
	private func snapCardToPageWithAnimation() {
		let layout = cardCV.collectionViewLayout as! UICollectionViewFlowLayout
		let itemWidth = layout.itemSize.width + 28
		let idx = Int(round(cardCV.contentOffset.x / itemWidth))
		
		if idx != currentIndex || cardCV.contentOffset.x != CGFloat(idx) * itemWidth {
			// 设置标记防止在动画过程中被干扰
			isSnapAnimating = true
			
			// 更新当前索引
			currentIndex = idx
			
			// 卡片对齐到整页（带动画）
			let targetX = CGFloat(idx) * itemWidth
			cardCV.setContentOffset(CGPoint(x: targetX, y: 0), animated: true)
			
			// 延迟一小段时间再执行emoji的带动画对齐，确保流畅
			DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
				// 如果已经不在动画状态，直接返回
				guard self.isSnapAnimating else { return }
				
				// emoji对齐到中心（带动画）
				if let attr = self.emojiCV.layoutAttributesForItem(at: IndexPath(item: idx, section: 0)) {
					let targetX = attr.center.x - self.emojiCV.bounds.width / 2
					
					// 边界检查，防止过度滚动
					let maxOffset = max(0, self.emojiCV.contentSize.width - self.emojiCV.bounds.width)
					let safeTargetX = min(maxOffset, max(0, targetX))
					
					self.emojiCV.setContentOffset(CGPoint(x: safeTargetX, y: 0), animated: true)
				}
				
				self.emojiCV.reloadData()
			}
		}
	}
	
	// Emoji滚动结束后，带动画吸附到中心，并同步卡片（带动画）
	private func snapEmojiWithAnimation() {
		guard let nearest = getNearestEmojiIndex() else { return }
		
		if nearest != currentIndex {
			// 设置标记防止在动画过程中被干扰
			isSnapAnimating = true
			
			// 更新当前索引
			currentIndex = nearest
			
			// Emoji对齐到中心（带动画）
			if let attr = emojiCV.layoutAttributesForItem(at: IndexPath(item: nearest, section: 0)) {
				let targetX = attr.center.x - emojiCV.bounds.width / 2
				
				// 边界检查，防止过度滚动
				let maxOffset = max(0, emojiCV.contentSize.width - emojiCV.bounds.width)
				let safeTargetX = min(maxOffset, max(0, targetX))
				
				emojiCV.setContentOffset(CGPoint(x: safeTargetX, y: 0), animated: true)
			}
			
			// 延迟一小段时间再执行卡片的带动画对齐，确保流畅
			DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
				// 如果已经不在动画状态，直接返回
				guard self.isSnapAnimating else { return }
				
				// 卡片对齐到整页（带动画）
				let layout = self.cardCV.collectionViewLayout as! UICollectionViewFlowLayout
				let itemWidth = layout.itemSize.width + 28
				let targetX = CGFloat(nearest) * itemWidth
				self.cardCV.setContentOffset(CGPoint(x: targetX, y: 0), animated: true)
			}
			
			emojiCV.reloadData()
		}
	}
	
	func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
		if scrollView === cardCV {
			// 卡片滚动结束，执行最终对齐（带动画）
			snapCardToPageWithAnimation()
		} else if scrollView === emojiCV {
			// 因为我们在willEndDragging中取消了emoji的惯性滚动，所以这里不会被触发
			// 但保留逻辑以防万一
			snapEmojiWithAnimation()
		}
		
		// 滚动结束后重置活动滚动视图
		activeScrollView = nil
	}
	
	private func updateEmojiZoomEffect() {
		guard !isSyncing else { return }
		
		let center = view.convert(centerMask.center, to: emojiCV)
		for cell in emojiCV.visibleCells {
			guard let idx = emojiCV.indexPath(for: cell),
				  let attr = emojiCV.layoutAttributesForItem(at: idx) else { continue }
			let distance = abs(attr.center.x - center.x)
			var alpha = 1 - (distance / 80)
			if alpha < 0.5 {
				alpha = 0
			}
			(cell as? YLEmojiCell)?.centerMask.alpha = alpha
			if alpha == 1 {
				(cell as? YLEmojiCell)?.label.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
			}else {
				(cell as? YLEmojiCell)?.label.transform = .identity
			}
		}
	}
	
	
	// 获取最接近中心的Emoji索引
	private func getNearestEmojiIndex() -> Int? {
		let center = view.convert(centerMask.center, to: emojiCV)
		let attrs = emojiCV.indexPathsForVisibleItems.compactMap {
			emojiCV.layoutAttributesForItem(at: $0)
		}
		guard let nearest = attrs.min(by: { abs($0.center.x - center.x) < abs($1.center.x - center.x) }) else {
			return nil
		}
		return nearest.indexPath.item
	}
	
	// 检查是否有新的emoji居中，并提供触觉反馈
	private func checkForCenteredEmojiAndProvideFeedback() {
		guard !isSyncing && !isSnapAnimating else { return }
		
		if let currentCenteredIndex = getNearestEmojiIndex() {
			// 判断当前居中的emoji是否在"准确居中"的范围内
			if let attr = emojiCV.layoutAttributesForItem(at: IndexPath(item: currentCenteredIndex, section: 0)) {
				let center = view.convert(centerMask.center, to: emojiCV)
				let distance = abs(attr.center.x - center.x)
				
				// 如果emoji几乎居中（允许小误差），且索引发生变化
				if distance < 0.2   {
					// 提供触觉反馈
					tapFeedbackGenerator.selectionChanged()
					tapFeedbackGenerator.prepare() // 重新准备，减少下次触发延迟
					if currentCenteredIndex != currentIndex {
						// 更新当前索引
						currentIndex = currentCenteredIndex
					}
					
				}
			}
		}
	}
	
	private func delete(record: YLStatusModel, at path: IndexPath) {
		showLoadingIndicator()
		statusServiceProvider.request(.deleteStatus(id: record.id)) { [weak self] res in
			guard let self else { return }
			self.hideLoadingIndicator()
			res.toJSONMapper(success: { _ in
				self.handleDeleteSuccess(at: path)
			}, failure: { err in
				self.handleDeleteFailure(err)
			})
		}
	}

	private func handleDeleteSuccess(at path: IndexPath) {
		removeRecordFromDataSource(at: path)
		reloadData()
	}

	private func handleDeleteFailure(_ error: Error) {
		self.view.makeToast(error.localizedDescription)
	}

	private func removeRecordFromDataSource(at path: IndexPath) {
		if self.currentRole == .mine {
			self.mineRecords.remove(at: path.item)
		} else {
			self.peerRecords.remove(at: path.item)
		}
	}
	
	// 处理添加emoji按钮点击
	private func handleAddEmojiTapped() {
		// 这里实现添加emoji的逻辑
		YLEmojiPickerViewController.showAlert(from: self, delegate: self)
		YLStatisticsHelper.trackEventByShushu("ui_click",dic: ["element_path":"实时状态-添加状态"],isOnce: true)
	}
	
}

extension YLRealTimeStatusViewController: YLEmojiPickerDelegate {
	
	func emojiPicker(_ picker: YLEmojiPickerViewController, didSelect emoji: String) {
		let vc = YLStatusEditViewController(emoji: emoji)
		vc.delegate = self
		let navi = YLNavigationViewController(rootViewController: vc)
		navi.modalPresentationStyle = .fullScreen
		picker.present(navi, animated: true)
	}
	
}
extension YLRealTimeStatusViewController: YLStatusEditDelegate {
	func statusEdit(_ controller: YLStatusEditViewController, didPublish status: YLStatusModel) {
		self.dismiss(animated: true)
		self.loadData()
	}
}
