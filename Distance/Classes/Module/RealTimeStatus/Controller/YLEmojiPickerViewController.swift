import UIKit
import SwiftyJSON
import SnapKit
import Jelly
// MARK: - YLEmojiPickerViewController

final class YLEmojiPickerViewController: UIViewController {
	
	weak var delegate: YLEmojiPickerDelegate?
	
	private var categories: [EmojiCategory] = []
	private var lastScrolledSection: Int = 0
	private let fakeNavBar  = UIView()
	private let backBtn     = UIButton(type: .custom)
	static var animator: Animator?
	// MARK: - UI Components
	
	private lazy var categoryCollectionView: UICollectionView = {
		let layout = UICollectionViewFlowLayout()
		layout.scrollDirection = .horizontal
		layout.itemSize = CGSize(width: 46, height: 46)
		layout.minimumLineSpacing = 0
		layout.minimumInteritemSpacing = 16
		layout.sectionInset = .init(top: 10, left: 16, bottom: 10, right: 16)
		let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
		cv.backgroundColor = .clear
		cv.showsHorizontalScrollIndicator = false
		cv.delegate = self
		cv.dataSource = self
		cv.register(YLEmojiCategoryCell.self, forCellWithReuseIdentifier: "YLEmojiCategoryCell")
		return cv
	}()
	
	private lazy var emojiCollectionView: UICollectionView = {
		let cv = UICollectionView(frame: .zero, collectionViewLayout: createEmojiListLayout())
		cv.backgroundColor = .clear
		cv.delegate = self
		cv.dataSource = self
		cv.register(YLEmojiCell.self, forCellWithReuseIdentifier: YLEmojiCell.id) // Renamed for clarity
		cv.register(YLEmojiSectionHeaderView.self,
					forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
					withReuseIdentifier: YLEmojiSectionHeaderView.reuseIdentifier)
		cv.allowsSelection = true
		return cv
	}()
	
	@discardableResult
	static func showAlert(from viewController: UIViewController, delegate: YLEmojiPickerDelegate)-> YLEmojiPickerViewController {
		
		let controller = YLEmojiPickerViewController()
		controller.delegate = delegate
		let size = PresentationSize(width: .fullscreen, height: .custom(value: kScreenHeight - 54))
		let alignment = PresentationAlignment(vertical: .bottom, horizontal: .center)
		let uiConfiguration = PresentationUIConfiguration(cornerRadius: 28, backgroundStyle: .dimmed(alpha: 0.8), isTapBackgroundToDismissEnabled: true, corners: [.layerMaxXMinYCorner, .layerMinXMinYCorner])
		let interaction = InteractionConfiguration(presentingViewController: viewController, completionThreshold: 0.3, dragMode: .canvas, mode: .dismiss)
		let presentation = CoverPresentation(directionShow: .bottom, directionDismiss: .bottom, uiConfiguration: uiConfiguration, size: size, alignment: alignment, interactionConfiguration: interaction)
		let animator = Animator(presentation: presentation)
		animator.prepare(presentedViewController: controller)
		self.animator = animator
		viewController.present(controller, animated: true, completion: nil)
		
		return controller
	}
	
	// MARK: - Lifecycle
	
	override func viewDidLoad() {
		super.viewDidLoad()
		view.backgroundColor = .white
		buildUI()
		loadData()
	}
	
	override func viewWillAppear(_ animated: Bool) {
		super.viewWillAppear(animated)
		if categories.isEmpty {
			loadData()
		}
	}
	
	override func viewDidDisappear(_ animated: Bool) {
		super.viewDidDisappear(animated)
		Self.animator = nil
	}
	
	// MARK: - Data Loading

	private func loadData() {
		showLoadingIndicator()

		statusServiceProvider.request(.allEmoji) { [weak self] result in
			guard let self = self else { return }
			self.hideLoadingIndicator()
			result.toJSONMapper(success: { json in
				self.handleDataLoadSuccess(json)
			}, failure: { error in
				self.handleDataLoadFailure(error)
			})
		}
	}

	private func showLoadingIndicator() {
		YLHUDView.showLoading(view: self.view)
	}

	private func hideLoadingIndicator() {
		YLHUDView.dismiss(view: self.view)
	}

	private func handleDataLoadSuccess(_ json: JSON) {
		let tempCategories = parseEmojiCategories(from: json)
		self.categories = tempCategories
		reloadCollectionViews()
		selectFirstCategoryIfNeeded()
	}

	private func handleDataLoadFailure(_ error: YLError) {
		self.view.yl_makeToast(error.errorDescription)
	}

	private func parseEmojiCategories(from json: JSON) -> [EmojiCategory] {
		var tempCategories: [EmojiCategory] = []
		for typeJson in json["data"]["types"].arrayValue {
			let typeName = typeJson["name"].stringValue
			let typeImgUrl = typeJson["pic"].stringValue
			let emojiArray = typeJson["emojis"].arrayValue.map { $0.stringValue }
			if !typeName.isEmpty && !emojiArray.isEmpty {
				tempCategories.append(EmojiCategory(type: typeName, emojis: emojiArray, typeImgUrl: typeImgUrl))
			}
		}
		return tempCategories
	}

	private func reloadCollectionViews() {
		self.categoryCollectionView.reloadData()
		self.emojiCollectionView.reloadData()
	}

	private func selectFirstCategoryIfNeeded() {
		if !self.categories.isEmpty {
			DispatchQueue.main.async {
				self.categoryCollectionView.selectItem(at: IndexPath(item: 0, section: 0), animated: false, scrollPosition: .centeredHorizontally)
			}
		}
	}
	
	// MARK: - UI Layout
	
	private func buildUI() {
		
		// 顶部小条
		let bar = UIImageView()
		bar.image = UIImage(named: "jkyj_dragbar")
		self.view.addSubview(bar)
		bar.snp.makeConstraints {
			$0.top.equalTo(4)
			$0.centerX.equalToSuperview()
		}
		
		// ——— 1. 自定义导航栏 ———
		fakeNavBar.backgroundColor = .clear
		view.addSubview(fakeNavBar)
		fakeNavBar.snp.makeConstraints {
			$0.top.equalToSuperview().offset(20)
			$0.left.right.equalToSuperview()
			$0.height.equalTo(44)
		}
		let titleLab = UILabel()
		titleLab.text = "status_choose_one_status".localized
		titleLab.font = .systemFont(ofSize: 16, weight: .medium)
		titleLab.textColor = UIColor(0x6C4F3B)
		titleLab.textAlignment = .center
		fakeNavBar.addSubview(titleLab)
		titleLab.snp.makeConstraints {
			$0.centerY.equalToSuperview()
			$0.centerX.equalToSuperview()
		}
		
		// 左：返回
		backBtn.setImage(UIImage(named: "back_bq_icon"), for: .normal)
		backBtn.addTarget(self, action: #selector(onBack), for: .touchUpInside)
		fakeNavBar.addSubview(backBtn)
		backBtn.snp.makeConstraints {
			$0.left.equalTo(10)
			$0.bottom.equalToSuperview().offset(-8)
			$0.size.equalTo(CGSize(width: 32, height: 32))
		}
		
		view.addSubview(categoryCollectionView)
		view.addSubview(emojiCollectionView)
		
		categoryCollectionView.snp.makeConstraints { make in
			make.top.equalTo(fakeNavBar.snp.bottom)
			make.leading.trailing.equalToSuperview()
			make.height.equalTo(66) // Fixed height for category bar
		}
		emojiCollectionView.backgroundColor = UIColor(red: 1, green: 0.992, blue: 0.965, alpha: 1)
		emojiCollectionView.snp.makeConstraints { make in
			make.top.equalTo(categoryCollectionView.snp.bottom)
			make.leading.trailing.bottom.equalToSuperview()
		}
	}
	
	// MARK: - Collection View Layout for Emojis
	
	private func createEmojiListLayout() -> UICollectionViewLayout {
		let layout = UICollectionViewCompositionalLayout { (sectionIndex: Int, layoutEnvironment: NSCollectionLayoutEnvironment) -> NSCollectionLayoutSection? in
			
			// Item
			let itemSize = NSCollectionLayoutSize(widthDimension: .absolute(44), heightDimension: .absolute(44))
			let item = NSCollectionLayoutItem(layoutSize: itemSize)
			
			// Group
			let interItemSpacing: CGFloat = 12
			let sectionInset: CGFloat = 16
			let screenWidth = layoutEnvironment.container.effectiveContentSize.width
			let availableWidthForItems = screenWidth - (2 * sectionInset)
			
			let itemWidth = itemSize.widthDimension.dimension
			let approximateNumberOfItemsPerRow = floor((availableWidthForItems + interItemSpacing) / (itemWidth + interItemSpacing))
			let numberOfItemsPerRow = max(1, Int(approximateNumberOfItemsPerRow))
			
			let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .absolute(44))
			let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitem: item, count: numberOfItemsPerRow)
			group.interItemSpacing = .fixed(interItemSpacing)
			
			// Section
			let section = NSCollectionLayoutSection(group: group)
			section.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: sectionInset, bottom: 0, trailing: sectionInset)
			section.interGroupSpacing = interItemSpacing
			
			// Section Header
			let headerSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .absolute(40))
			let sectionHeader = NSCollectionLayoutBoundarySupplementaryItem(
				layoutSize: headerSize,
				elementKind: UICollectionView.elementKindSectionHeader,
				alignment: .topLeading
			)
			section.boundarySupplementaryItems = [sectionHeader]
			
			return section
		}
		return layout
	}
	
	
	@objc private func onBack() {
		self.dismiss(animated: true)
	}
	
}

// MARK: - UICollectionViewDataSource

extension YLEmojiPickerViewController: UICollectionViewDataSource {
	// MARK: Category Collection View
	
	func numberOfSections(in collectionView: UICollectionView) -> Int {
		if collectionView == emojiCollectionView {
			return categories.count
		}
		return 1
	}
	
	func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
		if collectionView == categoryCollectionView {
			return categories.count
		} else {
			return categories[section].emojis.count
		}
	}
	
	func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
		if collectionView == categoryCollectionView {
			return configureCategoryCell(collectionView, at: indexPath)
		} else {
			return configureEmojiCell(collectionView, at: indexPath)
		}
	}

	private func configureCategoryCell(_ collectionView: UICollectionView, at indexPath: IndexPath) -> YLEmojiCategoryCell {
		guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "YLEmojiCategoryCell", for: indexPath) as? YLEmojiCategoryCell else {
			fatalError("Unable to dequeue YLEmojiCategoryCell")
		}
		if let imgUrl = URL(string: categories[indexPath.item].typeImgUrl) {
			cell.imageView.sd_setImage(with: imgUrl)
		}
		return cell
	}

	private func configureEmojiCell(_ collectionView: UICollectionView, at indexPath: IndexPath) -> YLEmojiCell {
		guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "YLEmojiCell", for: indexPath) as? YLEmojiCell else {
			fatalError("Unable to dequeue YLEmojiCell")
		}
		cell.bind(categories[indexPath.section].emojis[indexPath.item], selected: false)
		return cell
	}
	
	func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
		if kind == UICollectionView.elementKindSectionHeader && collectionView == emojiCollectionView {
			guard let header = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: YLEmojiSectionHeaderView.reuseIdentifier, for: indexPath) as? YLEmojiSectionHeaderView else {
				fatalError("Could not dequeue YLEmojiSectionHeaderView")
			}
			header.titleLabel.text = categories[indexPath.section].type
			return header
		}
		return UICollectionReusableView()
	}
}


// MARK: - UICollectionViewDelegate & UIScrollViewDelegate

extension YLEmojiPickerViewController: UICollectionViewDelegate, UIScrollViewDelegate {
	
	// MARK: Category Collection View Selection
	
	func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
		if collectionView == categoryCollectionView {
			handleCategorySelection(at: indexPath)
		} else {
			handleEmojiSelection(at: indexPath)
		}
	}

	private func handleCategorySelection(at indexPath: IndexPath) {
		let targetSection = indexPath.item
		guard targetSection < categories.count else { return }

		scrollToEmojiSection(targetSection)
	}

	private func handleEmojiSelection(at indexPath: IndexPath) {
		let emoji = categories[indexPath.section].emojis[indexPath.item]
		delegate?.emojiPicker(self, didSelect: emoji)
	}

	private func scrollToEmojiSection(_ targetSection: Int) {
		let headerIndexPath = IndexPath(item: 0, section: targetSection)

		if let layoutAttributes = emojiCollectionView.collectionViewLayout.layoutAttributesForSupplementaryView(ofKind: UICollectionView.elementKindSectionHeader, at: headerIndexPath) {
			let targetY = layoutAttributes.frame.origin.y
			let adjustedY = targetY - emojiCollectionView.adjustedContentInset.top
			emojiCollectionView.setContentOffset(CGPoint(x: 0, y: adjustedY), animated: false)
			lastScrolledSection = targetSection
		} else {
			print("Warning: Could not get layout attributes for header at section \(targetSection). Falling back to scroll to first item.")
			emojiCollectionView.scrollToItem(at: headerIndexPath, at: .top, animated: false)
			lastScrolledSection = targetSection
		}
	}
	
	// MARK: Emoji Collection View Scrolling -> Category Sync
	
	func scrollViewDidScroll(_ scrollView: UIScrollView) {
		guard scrollView == emojiCollectionView else { return }

		let currentVisibleSection = getCurrentVisibleSection()
		updateCategorySelectionIfNeeded(currentVisibleSection)
	}

	private func getCurrentVisibleSection() -> Int {
		let visibleRect = CGRect(origin: emojiCollectionView.contentOffset, size: emojiCollectionView.bounds.size)
		let visiblePoint = CGPoint(x: visibleRect.midX, y: visibleRect.minY + 1)

		if let indexPath = emojiCollectionView.indexPathForItem(at: visiblePoint) {
			return indexPath.section
		} else {
			// 兜底：用可见item中最小的section
			if let firstVisibleIndexPath = emojiCollectionView.indexPathsForVisibleItems.min(by: { $0.section < $1.section }) {
				return firstVisibleIndexPath.section
			}
		}
		return -1
	}

	private func updateCategorySelectionIfNeeded(_ currentVisibleSection: Int) {
		guard currentVisibleSection != -1 && currentVisibleSection != lastScrolledSection else { return }

		deselectPreviousCategory()
		selectNewCategory(currentVisibleSection)
		lastScrolledSection = currentVisibleSection
	}

	private func deselectPreviousCategory() {
		if let previousSelected = categoryCollectionView.indexPathsForSelectedItems?.first {
			categoryCollectionView.deselectItem(at: previousSelected, animated: true)
		}
	}

	private func selectNewCategory(_ section: Int) {
		let newSelectedIndexPath = IndexPath(item: section, section: 0)
		categoryCollectionView.selectItem(at: newSelectedIndexPath, animated: true, scrollPosition: .centeredHorizontally)
	}
	
}
