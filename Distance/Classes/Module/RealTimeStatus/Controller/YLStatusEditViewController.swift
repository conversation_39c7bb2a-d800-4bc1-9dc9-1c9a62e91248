import UIKit
import Snap<PERSON>it
import IQ<PERSON>eyboardManagerSwift
import WidgetKit

protocol YLStatusEditDelegate: AnyObject {
	func statusEdit(_ controller: YLStatusEditViewController, didPublish status: YLStatusModel)
}

final class YLStatusEditViewController: DTBaseViewController,CustomNavigation {
	
	var customNavStyle: NavStyle = .clear
	var customTitleFont: [NSAttributedString.Key : Any] = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium), NSAttributedString.Key.foregroundColor: #colorLiteral(red: 0.4666666667, green: 0.2941176471, blue: 0.2549019608, alpha: 1)]
	
	private let selectedEmoji: String
	private let charLimit = 30
	weak var delegate: YLStatusEditDelegate?
	
	init(emoji: String) {
		self.selectedEmoji = emoji
		super.init(nibName: nil, bundle: nil)
	}
	
	required init?(coder: NSCoder) { fatalError() }
	
	private let emojiLabel: UILabel = {
		let label = UILabel()
		label.font = .systemFont(ofSize: 129)
		label.textAlignment = .center
		return label
	}()
	
	private let textView: UITextView = {
		let tv = UITextView()
		tv.text = "status_add_text".localized
		tv.textColor = UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 0.3)
		tv.font = .systemFont(ofSize: 20, weight: .regular)
		tv.textAlignment = .center
		tv.backgroundColor = .clear
		return tv
	}()
	
	private let publishButton: UIButton = {
		let btn = UIButton(type: .custom)
		btn.setTitle("status_publish".localized, for: .normal)
		btn.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
		btn.setTitleColor(UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 1), for: .normal)
		btn.backgroundColor = UIColor(red: 0.992, green: 0.89, blue: 0.514, alpha: 1)
		btn.layer.cornerRadius = 24
		return btn
	}()
	
	private let stackView: UIStackView = {
		let sv = UIStackView()
		sv.axis = .vertical
		sv.alignment = .center
		sv.spacing = 28
		return sv
	}()
	
	private let activity = UIActivityIndicatorView(style: .medium)
	
	override func viewDidLoad() {
		super.viewDidLoad()
		IQKeyboardManager.shared.enable = false
		title = "status_edit".localized
		view.backgroundColor =  UIColor(red: 1, green: 0.992, blue: 0.965, alpha: 1)
		buildUI()
		registerKeyboard()
	}
	
	override func viewWillDisappear(_ animated: Bool) {
		super.viewWillDisappear(animated)
		IQKeyboardManager.shared.enable = true
	}
	private func buildUI() {
		
		let item = UIBarButtonItem.init(image: #imageLiteral(resourceName: "back_bq_icon").withRenderingMode(.alwaysOriginal), style: .plain, target: self, action: #selector(backBtnTapped))
		
		item.imageInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
		self.navigationItem.leftBarButtonItem = item
		
		let subLabel = UILabel()
		subLabel.textColor = UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 0.5)
		subLabel.font = UIFont(name: "PingFangSC-Regular", size: 13)
		subLabel.text = "status_pair_show_one_day".localized
		view.addSubview(subLabel)
		subLabel.snp.makeConstraints { make in
			make.centerX.equalToSuperview()
			make.height.equalTo(22)
			make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(-8)
		}
		
		emojiLabel.text = selectedEmoji.emojiFromCodePoint()
		let shadowView = UIImageView(image: UIImage(named: "RealTimeStatus_main_shadow"))
		
		// 用 stackView 包裹
		view.addSubview(stackView)
		stackView.addArrangedSubview(emojiLabel)
		stackView.addArrangedSubview(shadowView)
		stackView.addArrangedSubview(textView)
		stackView.setCustomSpacing(100, after: shadowView)
		stackView.snp.makeConstraints { make in
			if DeviceHelper.isSmallScreen {
				make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(100)
			}else {
				make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(170)
			}
			make.left.right.equalToSuperview().inset(60)
		}
		emojiLabel.setContentHuggingPriority(.required, for: .vertical)
		shadowView.setContentHuggingPriority(.required, for: .vertical)
		textView.setContentHuggingPriority(.defaultLow, for: .vertical)
		
		shadowView.snp.makeConstraints { make in
			make.height.equalTo(15)
		}
		textView.snp.makeConstraints { make in
			make.height.equalTo(58)
			make.left.right.equalToSuperview()
		}
		textView.delegate = self
		view.addSubview(publishButton)
		publishButton.snp.makeConstraints { make in
			make.left.right.equalToSuperview().inset(62)
			make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-42)
			make.height.equalTo(48)
		}
		
		publishButton.addTarget(self, action: #selector(publishAction), for: .touchUpInside)
	}
	
	private func registerKeyboard() {
		NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillChange), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
	}
	
	@objc private func keyboardWillChange(_ noti: Notification) {
		guard let info = noti.userInfo,
			  let endFrame = info[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect,
			  let duration = info[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval else { return }
		let keyboardY = endFrame.origin.y
		let screenH = UIScreen.main.bounds.height
		let keyboardHeight = screenH - keyboardY
		
		UIView.animate(withDuration: duration) {
			self.view.layoutIfNeeded()
			let offset = min(120, keyboardHeight - 20) // 至少留点空间
			self.stackView.transform = keyboardHeight > 0 ? CGAffineTransform(translationX: 0, y: -offset) : .identity
			
		}
	}
	
	@objc func backBtnTapped() {
		self.dismiss(animated: true)
	}
	
	
	@objc private func publishAction() {
		var text = textView.text.trimmingCharacters(in: .whitespacesAndNewlines)
		guard text.count <= charLimit else {
			return
		}
		
		view.endEditing(true)
		publishButton.isEnabled = false
		publishButton.setTitle("", for: .normal)
		publishButton.addSubview(activity)
		activity.snp.makeConstraints { $0.center.equalToSuperview() }
		activity.startAnimating()
		if text == "status_add_text".localized {
			text = ""
		}
		// 调用发布接口
		statusServiceProvider.request(.addStatus(unicode: selectedEmoji, words: text)) { [weak self] result in
			guard let self = self else { return }
			self.activity.stopAnimating()
			self.publishButton.isEnabled = true
			self.publishButton.setTitle("status_publish".localized, for: .normal)
			
			result.toJSONMapper(success: { json in
				let status = YLStatusModel(id: "",
										   emoji: self.selectedEmoji,
										   text: text,
										   timestamp: Date().timeIntervalSince1970,
										   isMine: true)
				self.delegate?.statusEdit(self, didPublish: status)
				topWindow?.yl_makeToast("status_publish_success".localized)
				YLStatisticsHelper.trackEventByShushu("post_status_suc", dic: ["is_text":!text.isEmpty])
			}, failure: { error in
				self.view.yl_makeToast(error.errorDescription)
			})
		}
	}
	
	deinit {
		NotificationCenter.default.removeObserver(self)
	}
}
extension YLStatusEditViewController: UITextViewDelegate {
	
	func textViewDidBeginEditing(_ textView: UITextView) {
		if textView.textColor == UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 0.3) {
			textView.text = ""
			textView.font = .systemFont(ofSize: 20, weight: .medium)
			textView.textColor = UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 1)
		}
	}
	
	func textViewDidEndEditing(_ textView: UITextView) {
		if textView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
			textView.text = "status_add_text".localized
			textView.textColor = UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 0.3)
			textView.font = .systemFont(ofSize: 20, weight: .regular)
		}
	}
	
	func textViewDidChange(_ textView: UITextView) {
		// 如果输入法正在联想（如拼音打字中），先不处理
		guard textView.markedTextRange == nil else { return }
		
		let originalText = textView.text ?? ""
		let limitedText = limitedString(from: originalText)
		if originalText != limitedText {
			textView.text = limitedText
		}
	}
	
	func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
		// 阻止输入换行符
		if text == "\n" {
			return false
		}
		return true
	}
	
	private func limitedString(from text: String) -> String {
		var count = 0
		var chineseCount = 0
		var result = ""
		
		for char in text {
			if char.isChinese {
				if chineseCount >= 15 { continue }
				count += 2
				chineseCount += 1
			} else {
				count += 1
			}
			
			if count > 30 {
				break
			}
			result.append(char)
		}
		return result
	}
}

private extension Character {
	var isChinese: Bool {
		guard let scalar = unicodeScalars.first else { return false }
		return (0x4E00...0x9FFF).contains(scalar.value)
	}
	
}
