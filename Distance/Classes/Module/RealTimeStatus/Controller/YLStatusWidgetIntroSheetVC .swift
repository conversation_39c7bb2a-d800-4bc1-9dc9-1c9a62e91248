//
//  YLStatusWidgetIntroSheetVC 2.swift
//  Distance
//
//  Created by ke wen on 7/18/25.
//


import UIKit
import SnapKit
import Jelly
import Lottie

final class YLStatusWidgetIntroSheetVC: UIViewController {
	
	private let titleLab = UILabel()
	private let descLab = UILabel()
	// private let centerImageView = UIImageView()
	private let lottieView = LottieAnimationView(name: "status_widget_intro")
	
	private let okBtn = UIButton(type: .custom)
	let contentView = UIView()
	static var animator: Animator?
	
	@discardableResult
	static func showAlert(from viewController: UIViewController)-> YLStatusWidgetIntroSheetVC {
		
		let controller = YLStatusWidgetIntroSheetVC()
		let size = PresentationSize(width: .fullscreen, height: .fullscreen)
		let alignment = PresentationAlignment(vertical: .bottom, horizontal: .center)
		let uiConfiguration = PresentationUIConfiguration(cornerRadius: 28, backgroundStyle: .dimmed(alpha: 0.4), isTapBackgroundToDismissEnabled: true, corners: [.layerMaxXMinYCorner, .layerMinXMinYCorner])
		let interaction = InteractionConfiguration(presentingViewController: viewController, completionThreshold: 0.3, dragMode: .canvas, mode: .dismiss)
		let presentation = CoverPresentation(directionShow: .bottom, directionDismiss: .bottom, uiConfiguration: uiConfiguration, size: size, alignment: alignment, interactionConfiguration: interaction)
		let animator = Animator(presentation: presentation)
		animator.prepare(presentedViewController: controller)
		self.animator = animator
		viewController.present(controller, animated: true, completion: nil)
		
		return controller
	}
	override func viewDidLoad() {
		super.viewDidLoad()
		view.backgroundColor = .clear  // 整个 view 是透明的，背景靠 Jelly 提供
		let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped(_:)))
		tap.delegate = self  // ⚠️ 注意需要设置 delegate 才能判断区域
		view.addGestureRecognizer(tap)
		buildUI()
		
	}
	
	override func viewDidDisappear(_ animated: Bool) {
		super.viewDidDisappear(animated)
		Self.animator = nil
	}
	
	private func buildUI() {
		// 内容容器
		
		contentView.backgroundColor = UIColor(red: 1, green: 0.992, blue: 0.965, alpha: 1)
		contentView.layer.cornerRadius = 28
		contentView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
		contentView.clipsToBounds = true
		view.addSubview(contentView)
		
		contentView.snp.makeConstraints {
			$0.left.right.bottom.equalToSuperview()
		}
		
		// 顶部小条
		let bar = UIImageView()
		bar.image = UIImage(named: "jkyj_dragbar")
		contentView.addSubview(bar)
		bar.snp.makeConstraints {
			$0.top.equalTo(4)
			$0.centerX.equalToSuperview()
		}
		
		
		// 标题
		titleLab.text = "status_widget_teach_title".localized
		titleLab.font = .systemFont(ofSize: 16, weight: .medium)
		titleLab.textColor = UIColor(red: 0.47, green: 0.29, blue: 0.25, alpha: 1)
		titleLab.textAlignment = .center
		titleLab.numberOfLines = 0
		
		let firstDescLab = UILabel()
		firstDescLab.text = "status_widget_teach_center_content".localized
		firstDescLab.font = .systemFont(ofSize: 14)
		firstDescLab.textColor = UIColor(red: 0.47, green: 0.29, blue: 0.25, alpha: 1)
		firstDescLab.textAlignment = .left
		firstDescLab.numberOfLines = 0
		
		let secondDescLab = UILabel()
		secondDescLab.text = "status_widget_teach_bottom_content".localized
		secondDescLab.font = .systemFont(ofSize: 14,weight: .medium)
		secondDescLab.textColor = UIColor(red: 0.47, green: 0.29, blue: 0.25, alpha: 1)
		secondDescLab.textAlignment = .left
		secondDescLab.numberOfLines = 0
		
		let descStack = UIStackView(arrangedSubviews: [firstDescLab, secondDescLab])
		descStack.axis = .vertical
		descStack.spacing = 16
		
		lottieView.contentMode = .scaleAspectFit
		lottieView.loopMode = .loop
		lottieView.play()
		
		lottieView.snp.makeConstraints {
			$0.height.equalTo(399)
		}
		
		
		okBtn.setTitle("ok".localized, for: .normal)
		okBtn.setBackgroundImage(UIImage(named: "yjdl_bg_btn"), for: .normal)
		
		okBtn.setTitleColor(UIColor(red: 0.468, green: 0.295, blue: 0.257, alpha: 1), for: .normal)
		okBtn.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
		okBtn.addTarget(self, action: #selector(dismissSelf), for: .touchUpInside)
		
		// 全部放入主 stackView
		let mainStack = UIStackView(arrangedSubviews: [titleLab, descStack, lottieView, okBtn])
		mainStack.axis = .vertical
		mainStack.spacing = 16
		mainStack.alignment = .fill
		contentView.addSubview(mainStack)
		mainStack.snp.makeConstraints {
			$0.top.equalToSuperview().offset(28)
			$0.left.right.equalToSuperview().inset(24)
			$0.bottom.equalTo(view.safeAreaLayoutGuide).offset(-24)
		}
	}
	
	@objc private func backgroundTapped(_ sender: UITapGestureRecognizer) {
		let location = sender.location(in: view)
		if !contentView.frame.contains(location) {
			self.dismiss(animated: true)
		}
	}
	
	@objc private func dismissSelf() {
		self.dismiss(animated: true)
	}
}
extension YLStatusWidgetIntroSheetVC: UIGestureRecognizerDelegate {
	func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
		// 如果点到 contentView 内部，不拦截手势
		let location = touch.location(in: view)
		return !contentView.frame.contains(location)
	}
}
