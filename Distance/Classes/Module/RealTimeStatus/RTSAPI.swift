import Foundation
import Moya

/// Real-Time Status backend API 定义
enum RTSAPI {
	/// GET /realState/getAllEmoji  获取全部表情
	case allEmoji
	/// GET /realState/getPairRealState  获取双方实时状态
	case pairStatus
	/// POST /realState/SetRealState  新增实时状态
	case addStatus(unicode: String, words: String?)
	/// GET /realState/deleteRealStateRecord  删除实时状态
	case deleteStatus(id: String)
}

/// 共享 provider
let statusServiceProvider = MoyaProvider<RTSAPI>(session: yl_session, plugins: [YLSignPlugin(), AccessTokenPlugin { _ in UserData.shared.loginToken}])
//let statusServiceProvider = MoyaProvider<RTSAPI>(stubClosure: MoyaProvider.immediatelyStub)

extension RTSAPI: TargetType,AccessTokenAuthorizable {
	var baseURL: URL {
		// 直接复用已有公共域名管理器
		return URL(string: YLTestEnviromentManager.shared.mainDomain)!
	}
	
	var authorizationType: AuthorizationType? { .bearer }
	
	var path: String {
		switch self {
		case .allEmoji:         return "/realState/getAllEmoji"
		case .pairStatus:       return "/realState/getPairRealState"
		case .addStatus:        return "/realState/setRealState"
		case .deleteStatus:     return "/realState/deleteRealStateRecord"
		}
	}
	
	var method: Moya.Method {
		switch self {
		case .allEmoji, .pairStatus, .deleteStatus:
			return .get
		case .addStatus:
			return .post
		}
	}
	
	var task: Task {
		var parameter = YLPublicParameter.publicParameter()
		switch self {
		case .allEmoji, .pairStatus:
			break
		case let .addStatus(unicode, words):
			var body: [String: Any] = ["unicode": unicode]
			body["pairId"] = UserData.shared.pairId
			body["userId"] = UserData.shared.userId
			if let words = words {
				body["words"] = words
			}
			return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: parameter)
		case let .deleteStatus(id):
			parameter["id"] = id
		}
		
		return .requestParameters(parameters: parameter, encoding: URLEncoding.queryString)
	}
	
	var headers: [String : String]? {
		return ["Content-Type": "application/json"]
	}
	
	// 用于本地 UnitTest / Preview
	var sampleData: Data {
		switch self {
		case .allEmoji:
			let json: [String: Any] = [
				"status": 200,
				"data": ["types": []]
			]
			return try! JSONSerialization.data(withJSONObject: json, options: [])
		case .pairStatus:
			return """
	{
	  "status":200,
	  "msg":"Request successful",
	  "data":{
	 "selfRealRecords":[
	   {"id":1001,"unicode":"😀","words":"Good morning!","duration":9.1},
	   {"id":1002,"unicode":"😎","words":"Looking cool today.","duration":7.4},
	   {"id":1003,"unicode":"😍","words":"Miss you already.","duration":10.2},
	   {"id":1004,"unicode":"😂","words":"That was hilarious.","duration":8.6},
	   {"id":1005,"unicode":"🤖","words":"Robot activated.","duration":6.3},
	   {"id":1006,"unicode":"🎉","words":"Celebrating your win!","duration":11.0},
	   {"id":1007,"unicode":"❤️","words":"Love you.","duration":7.8},
	   {"id":1008,"unicode":"🔥","words":"You crushed it!","duration":9.5},
	   {"id":1009,"unicode":"🌟","words":"You're a star!","duration":6.9},
	   {"id":1010,"unicode":"🥳","words":"Party time!","duration":8.0},
	   {"id":1011,"unicode":"📷","words":"Check this photo.","duration":6.4},
	   {"id":1012,"unicode":"🎶","words":"Music to my ears.","duration":7.1},
	   {"id":1013,"unicode":"💡","words":"Here's a bright idea.","duration":5.9},
	   {"id":1014,"unicode":"🐶","words":"My dog says hi.","duration":6.6},
	   {"id":1015,"unicode":"🍕","words":"Lunch was great.","duration":9.0},
	   {"id":1016,"unicode":"🏖️","words":"Beach vibes!","duration":10.1},
	   {"id":1017,"unicode":"🌈","words":"Bright days ahead.","duration":7.3},
	   {"id":1018,"unicode":"🧡","words":"Take care!","duration":5.5},
	   {"id":1019,"unicode":"😴","words":"Time to sleep.","duration":8.2},
	   {"id":1020,"unicode":"🙌","words":"Well done!","duration":6.8}
	 ],
	 "otherRealRecords":[
	   {"id":2001,"pairId":1001,"userId":"user_01","unicode":"🥰","words":"So sweet!","deleteTime":1721111111,"createTime":1721107511,"duration":9.0},
	   {"id":2002,"pairId":1002,"userId":"user_02","unicode":"👀","words":"Watching closely.","deleteTime":1721111211,"createTime":1721107611,"duration":8.1},
	   {"id":2003,"pairId":1003,"userId":"user_03","unicode":"🤩","words":"That's amazing.","deleteTime":1721111311,"createTime":1721107711,"duration":9.3},
	   {"id":2004,"pairId":1004,"userId":"user_04","unicode":"😇","words":"You're the best.","deleteTime":1721111411,"createTime":1721107811,"duration":7.5},
	   {"id":2005,"pairId":1005,"userId":"user_05","unicode":"😜","words":"Just kidding!","deleteTime":1721111511,"createTime":1721107911,"duration":8.7},
	   {"id":2006,"pairId":1006,"userId":"user_06","unicode":"🎂","words":"Happy Birthday!","deleteTime":1721111611,"createTime":1721108011,"duration":10.0},
	   {"id":2007,"pairId":1007,"userId":"user_07","unicode":"🚀","words":"To the moon!","deleteTime":1721111711,"createTime":1721108111,"duration":9.6},
	   {"id":2008,"pairId":1008,"userId":"user_08","unicode":"🧠","words":"Big brain move.","deleteTime":1721111811,"createTime":1721108211,"duration":7.2},
	   {"id":2009,"pairId":1009,"userId":"user_09","unicode":"✈️","words":"Safe travels!","deleteTime":1721111911,"createTime":1721108311,"duration":6.9},
	   {"id":2010,"pairId":1010,"userId":"user_10","unicode":"🥶","words":"It's freezing!","deleteTime":1721112011,"createTime":1721108411,"duration":7.7},
	   {"id":2011,"pairId":1011,"userId":"user_11","unicode":"📚","words":"Study hard.","deleteTime":1721112111,"createTime":1721108511,"duration":8.2},
	   {"id":2012,"pairId":1012,"userId":"user_12","unicode":"💤","words":"Nap time.","deleteTime":1721112211,"createTime":1721108611,"duration":6.0},
	   {"id":2013,"pairId":1013,"userId":"user_13","unicode":"🛒","words":"Shopping done.","deleteTime":1721112311,"createTime":1721108711,"duration":6.5},
	   {"id":2014,"pairId":1014,"userId":"user_14","unicode":"🧊","words":"Cool move.","deleteTime":1721112411,"createTime":1721108811,"duration":7.1},
	   {"id":2015,"pairId":1015,"userId":"user_15","unicode":"💻","words":"Code complete.","deleteTime":1721112511,"createTime":1721108911,"duration":9.9},
	   {"id":2016,"pairId":1016,"userId":"user_16","unicode":"🧋","words":"Milk tea break.","deleteTime":1721112611,"createTime":1721109011,"duration":6.7},
	   {"id":2017,"pairId":1017,"userId":"user_17","unicode":"🦄","words":"Magic moment.","deleteTime":1721112711,"createTime":1721109111,"duration":8.6},
	   {"id":2018,"pairId":1018,"userId":"user_18","unicode":"👑","words":"Royal energy.","deleteTime":1721112811,"createTime":1721109211,"duration":8.8},
	   {"id":2019,"pairId":1019,"userId":"user_19","unicode":"🔔","words":"Reminder set.","deleteTime":1721112911,"createTime":1721109311,"duration":7.4},
	   {"id":2020,"pairId":1020,"userId":"user_20","unicode":"🎁","words":"A little gift.","deleteTime":1721113011,"createTime":1721109411,"duration":9.2}
	 ],
	 "selfAvatar":"https://example.com/avatar/self.png",
	 "otherAvatar":"https://example.com/avatar/other.png"
	  }
	}
	""".data(using: .utf8)!
			
			
		case .addStatus, .deleteStatus:
			let json: [String: Any] = ["status": 200, "data": true]
			return try! JSONSerialization.data(withJSONObject: json, options: [])
		}
	}
}
