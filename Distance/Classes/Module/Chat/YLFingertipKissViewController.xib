<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="YLFingertipKissViewController" customModule="Distance" customModuleProvider="target">
            <connections>
                <outlet property="kissImageView" destination="jRW-Uw-RGL" id="ah7-xn-Egk"/>
                <outlet property="kissTouchView" destination="ge9-j8-uc4" id="CDV-AG-GPA"/>
                <outlet property="promptLab" destination="Hrf-k1-Jqg" id="6Ma-Ub-1Ho"/>
                <outlet property="titleLab" destination="nbN-Vz-egZ" id="77t-5I-NHc"/>
                <outlet property="topTitleLab" destination="4Gn-LQ-fPE" id="eYf-7Y-ilw"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="opU-I1-ATb" customClass="UIControl">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rkf-Pk-zdc">
                            <rect key="frame" x="0.0" y="371.33333333333331" width="393" height="512.66666666666674"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9iB-Gx-DAf">
                                    <rect key="frame" x="178.66666666666666" y="12" width="36" height="5"/>
                                    <color key="backgroundColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="0.3044805463576159" colorSpace="custom" customColorSpace="sRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="5" id="7E1-UC-VjE"/>
                                        <constraint firstAttribute="width" constant="36" id="kwi-5y-NOM"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <real key="value" value="2.5"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="指尖亲吻" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Gn-LQ-fPE">
                                    <rect key="frame" x="164.66666666666666" y="32" width="63.666666666666657" height="19.333333333333329"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="chat_extend_kiss"/>
                                    </userDefinedRuntimeAttributes>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="A65-xI-nBb">
                                    <rect key="frame" x="333" y="23.666666666666686" width="46" height="36"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" image="lt_new_kiss_why"/>
                                    <buttonConfiguration key="configuration" style="plain" image="lt_new_kiss_why"/>
                                    <connections>
                                        <action selector="whyButtonAction:" destination="-1" eventType="touchUpInside" id="0Zo-nC-f3m"/>
                                    </connections>
                                </button>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ge9-j8-uc4">
                                    <rect key="frame" x="16" y="79.666666666666686" width="361" height="361"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="lt_new_kiss_bg" translatesAutoresizingMaskIntoConstraints="NO" id="kew-Zk-aei">
                                            <rect key="frame" x="0.0" y="0.0" width="361" height="361"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="让另一半来到聊天页面" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hrf-k1-Jqg">
                                            <rect key="frame" x="30" y="172" width="301" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="0.18186587100000001" green="0.1774442792" blue="0.2068564594" alpha="0.40000000000000002" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="chat_extend_kiss_hint"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="lt_new_kiss_hc" translatesAutoresizingMaskIntoConstraints="NO" id="jRW-Uw-RGL">
                                            <rect key="frame" x="17" y="18" width="28" height="22"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="28" id="DU2-Ac-0je"/>
                                                <constraint firstAttribute="height" constant="22" id="WMv-Ef-Pmz"/>
                                            </constraints>
                                        </imageView>
                                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="等待中..." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nbN-Vz-egZ">
                                            <rect key="frame" x="299.33333333333331" y="22" width="46.666666666666686" height="14.333333333333336"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                            <color key="textColor" red="0.46795046330000001" green="0.29378396270000001" blue="0.2533094883" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="Hrf-k1-Jqg" firstAttribute="leading" secondItem="ge9-j8-uc4" secondAttribute="leading" constant="30" id="4dz-cd-Jsr"/>
                                        <constraint firstAttribute="trailing" secondItem="nbN-Vz-egZ" secondAttribute="trailing" constant="15" id="FM6-O3-RoC"/>
                                        <constraint firstItem="jRW-Uw-RGL" firstAttribute="top" secondItem="ge9-j8-uc4" secondAttribute="top" constant="18" id="N0o-ms-Fa7"/>
                                        <constraint firstItem="Hrf-k1-Jqg" firstAttribute="centerX" secondItem="ge9-j8-uc4" secondAttribute="centerX" id="SVN-un-7xq"/>
                                        <constraint firstAttribute="trailing" secondItem="kew-Zk-aei" secondAttribute="trailing" id="UTS-RO-9IL"/>
                                        <constraint firstAttribute="bottom" secondItem="kew-Zk-aei" secondAttribute="bottom" id="Yce-fp-8PM"/>
                                        <constraint firstAttribute="width" secondItem="ge9-j8-uc4" secondAttribute="height" multiplier="1:1" id="Ymb-Qv-8Td"/>
                                        <constraint firstItem="Hrf-k1-Jqg" firstAttribute="centerY" secondItem="ge9-j8-uc4" secondAttribute="centerY" id="bha-on-grt"/>
                                        <constraint firstItem="kew-Zk-aei" firstAttribute="top" secondItem="ge9-j8-uc4" secondAttribute="top" id="dkn-ow-muA"/>
                                        <constraint firstItem="nbN-Vz-egZ" firstAttribute="centerY" secondItem="jRW-Uw-RGL" secondAttribute="centerY" id="eZe-bG-Y0n"/>
                                        <constraint firstAttribute="trailing" secondItem="Hrf-k1-Jqg" secondAttribute="trailing" constant="30" id="nx6-NV-bD4"/>
                                        <constraint firstItem="kew-Zk-aei" firstAttribute="leading" secondItem="ge9-j8-uc4" secondAttribute="leading" id="ptr-23-EmH"/>
                                        <constraint firstItem="jRW-Uw-RGL" firstAttribute="leading" secondItem="ge9-j8-uc4" secondAttribute="leading" constant="17" id="tOM-eu-xhL"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstItem="9iB-Gx-DAf" firstAttribute="top" secondItem="Rkf-Pk-zdc" secondAttribute="topMargin" constant="4" id="3h1-lW-bY5"/>
                                <constraint firstItem="A65-xI-nBb" firstAttribute="centerY" secondItem="4Gn-LQ-fPE" secondAttribute="centerY" id="8UA-iC-GKF"/>
                                <constraint firstItem="4Gn-LQ-fPE" firstAttribute="centerX" secondItem="Rkf-Pk-zdc" secondAttribute="centerX" id="9f7-dP-CTC"/>
                                <constraint firstItem="ge9-j8-uc4" firstAttribute="top" secondItem="A65-xI-nBb" secondAttribute="bottom" constant="20" id="BS9-MP-JRf"/>
                                <constraint firstAttribute="bottom" secondItem="ge9-j8-uc4" secondAttribute="bottom" constant="72" id="IPr-wQ-iPS"/>
                                <constraint firstItem="4Gn-LQ-fPE" firstAttribute="top" secondItem="9iB-Gx-DAf" secondAttribute="bottom" constant="15" id="KHL-Xe-Zyq"/>
                                <constraint firstAttribute="trailing" secondItem="A65-xI-nBb" secondAttribute="trailing" constant="14" id="SQh-OD-YMv"/>
                                <constraint firstItem="ge9-j8-uc4" firstAttribute="leading" secondItem="Rkf-Pk-zdc" secondAttribute="leading" constant="16" id="and-o5-k2f"/>
                                <constraint firstAttribute="trailing" secondItem="ge9-j8-uc4" secondAttribute="trailing" constant="16" id="qgg-9q-8wD"/>
                                <constraint firstItem="9iB-Gx-DAf" firstAttribute="centerX" secondItem="Rkf-Pk-zdc" secondAttribute="centerX" id="uf1-W6-XS5"/>
                                <constraint firstItem="ge9-j8-uc4" firstAttribute="centerX" secondItem="Rkf-Pk-zdc" secondAttribute="centerX" id="wtD-oO-6mT"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="32"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="0.20805713383838387" green="0.20805713383838387" blue="0.20805713383838387" alpha="0.29909975165562913" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="Rkf-Pk-zdc" secondAttribute="trailing" id="A47-Vt-VLS"/>
                        <constraint firstAttribute="bottom" secondItem="Rkf-Pk-zdc" secondAttribute="bottom" constant="-32" id="Pnm-TC-CDB"/>
                        <constraint firstItem="Rkf-Pk-zdc" firstAttribute="centerX" secondItem="opU-I1-ATb" secondAttribute="centerX" id="efM-yK-6fd"/>
                        <constraint firstItem="Rkf-Pk-zdc" firstAttribute="leading" secondItem="opU-I1-ATb" secondAttribute="leading" id="fU1-8o-q5N"/>
                    </constraints>
                    <connections>
                        <action selector="touchBackgroundViewAction:" destination="-1" eventType="touchUpInside" id="c6b-m1-yz4"/>
                    </connections>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Utu-Gj-NJ8"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="opU-I1-ATb" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="65d-df-KUU"/>
                <constraint firstAttribute="bottom" secondItem="opU-I1-ATb" secondAttribute="bottom" id="PYH-x0-FLg"/>
                <constraint firstItem="opU-I1-ATb" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="Wla-bS-QNU"/>
                <constraint firstAttribute="trailing" secondItem="opU-I1-ATb" secondAttribute="trailing" id="bYs-J6-GLf"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="lt_new_kiss_bg" width="362.66665649414062" height="362.66665649414062"/>
        <image name="lt_new_kiss_hc" width="28" height="22"/>
        <image name="lt_new_kiss_why" width="22" height="22"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
