//
//  YLChatApi.swift
//  Distance
//
//  Created by chang he on 2021/6/11.
//

import UIKit
import Moya
import QCloudCore

let chatApi = MoyaProvider<YLChatApi>(session: yl_session, plugins: [YLSignPlugin(), AccessTokenPlugin { _ in UserData.shared.loginToken}])

enum YLChatApi {
	case talkInit(userid: String)
	case emojiClassifyList(eId: Int)
	case talkGifList(pageNum:Int, pageSize:Int)
	case useGif(gId:Int)
	case getReportList
	case getActivityList
	case messageReport(date:String)
	case getEnshrineList(userid:String)
	case takeEnshrine(userid:String, imageUrl:String)
	case talkEnshrineDelete(userid:String, ids:[Int])
	case talkEnshrineTop(userid:String, ids:[Int])
	case talkCoverApply(userid:String, cId:Int)
	case talkCoverAdd(userid:String, imgurl:String)
	case reportTalkDisapear
}

extension YLChatApi:TargetType, AccessTokenAuthorizable {
	var authorizationType: AuthorizationType? { .bearer }
	
	var baseURL: URL {
		return URL(string: YLTestEnviromentManager.shared.baseUrl)!
	}
	
	var path: String {
		switch self {
		case .talkInit:
			return "/talk/init"
		case .emojiClassifyList:
			return "/talk/getEmojiClassifyList"
		case .talkGifList:
			return "/talk/getGifList"
		case .getReportList:
			return "/talk/account_list"
		case .useGif:
			return "/talk/useGif"
		case .getActivityList:
			return "/talk/getActivityList"
		case .messageReport:
			return "/talk/message_report"
		case .getEnshrineList:
			return "/talk/enshrine_list"
		case .takeEnshrine:
			return "/talk/enshrine"
		case .talkEnshrineDelete:
			return "/talk/enshrine_delete"
		case .talkEnshrineTop:
			return "/talk/enshrine_top"
		case .talkCoverApply:
			return "/talk/cover_apply"
		case .talkCoverAdd:
			return "/talk/cover_add"
		case .reportTalkDisapear:
			return "/talk/reportTalkDisapear"
		}
		
	}
	
	var method: Moya.Method {
		switch self {
		case .talkInit, .emojiClassifyList, .getReportList, .talkGifList, .useGif, .getActivityList, .getEnshrineList, .takeEnshrine, .messageReport, .talkCoverApply, .talkCoverAdd, .reportTalkDisapear:
			return .get
		default :
			return .post
		}
	}
	
	var sampleData: Data {
		Data()
	}
	
	var task: Task {
		var parameter = YLPublicParameter.publicParameter()
		switch self {
		case let .talkInit(userid):
			parameter["userid"] = userid
		case let .emojiClassifyList(eId):
			parameter["id"] = eId
		case let.talkGifList(pageNum, pageSize):
			parameter["pagenum"] = pageNum
			parameter["pagesize"] = pageSize
		case let .useGif(gId):
			parameter["id"] = gId
		case .getReportList, .getActivityList, .reportTalkDisapear:
			break
		case let .messageReport(date):
			parameter["date"] = date
		case let .getEnshrineList(userid):
			parameter["userid"] = userid
		case let .takeEnshrine(userid, imageUrl):
			parameter["userid"] = userid
			parameter["imgurl"] = imageUrl
			parameter["hash"] = QCloudEncrytMD5String(imageUrl)
		case let .talkEnshrineDelete(userid, ids):
			var body = [String:Any]()
			body["ids"] = ids
			parameter["userid"] = userid
			return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: parameter)
		case let .talkEnshrineTop(userid, ids):
			var body = [String:Any]()
			body["ids"] = ids
			parameter["userid"] = userid
			return .requestCompositeParameters(bodyParameters: body, bodyEncoding: JSONEncoding.default, urlParameters: parameter)
		case let .talkCoverApply(userid, cId):
			parameter["userid"] = userid
			parameter["cid"] = cId
		case let.talkCoverAdd(userid, imgurl):
			parameter["userid"] = userid
			parameter["imgurl"] = imgurl
		}
		
		return .requestParameters(parameters: parameter, encoding: URLEncoding.queryString)
	}
	
	var headers: [String : String]? {
		return nil
	}
	
}
