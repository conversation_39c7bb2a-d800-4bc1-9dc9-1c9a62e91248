//
//  YLHapticManager.swift
//  Distance
//
//  Created by Q on 2025/7/16.
//

import Foundation
import UIKit

//kiss功能震动管理类，考虑到海外用户可能手机差，没用Core Haptics
class YLHapticManager {
	static let shared = YLHapticManager()
	private var feedbackGenerator: UIImpactFeedbackGenerator?
	private var timer: Timer?
	private var pendingWorkItems: [DispatchWorkItem] = []
	private var isHapticAvailable: Bool {
		if #available(iOS 10.0, *) {
			return true
		}
		return false
	}
	
	private init() {
		guard isHapticAvailable else { return }
		feedbackGenerator = UIImpactFeedbackGenerator(style: .medium)
		feedbackGenerator?.prepare()
	}
	
	// MARK: - 震动模式
	// 模式1：双方未重叠/仅一方长按
	func playNotOverlappingPattern() {
			guard isHapticAvailable else { return }
			stopCurrentPattern()
			
			let firstPulse = DispatchWorkItem { [weak self] in
				self?.playHaptic(duration: 0.1)
				
				let secondPulse = DispatchWorkItem { [weak self] in
					self?.playHaptic(duration: 0.12)
					
					let pause = DispatchWorkItem { [weak self] in
						self?.playNotOverlappingPattern() // 递归调用
					}
					self?.pendingWorkItems.append(pause)
					DispatchQueue.main.asyncAfter(deadline: .now() + 0.6, execute: pause)
				}
				
				self?.pendingWorkItems.append(secondPulse)
				DispatchQueue.main.asyncAfter(deadline: .now() + 0.15, execute: secondPulse)
			}
			
			pendingWorkItems.append(firstPulse)
			DispatchQueue.main.asyncAfter(deadline: .now(), execute: firstPulse)
		}
	
	// 模式2：Kiss成功
	func playKissSuccessPattern() {
		guard isHapticAvailable else { return }
		stopCurrentPattern()
		
		timer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { [weak self] _ in
			self?.playHaptic(duration: 0.1)
		}
	}
	
	// 停止当前震动模式
	func stopCurrentPattern() {
		// 取消所有待执行的DispatchWorkItem
		pendingWorkItems.forEach { $0.cancel() }
		pendingWorkItems.removeAll()
		
		// 停止Timer
		timer?.invalidate()
		timer = nil
	}
	
	// MARK: - 私有方法
	private func playHaptic(duration: TimeInterval) {
		feedbackGenerator?.impactOccurred()
	}
}
