//
//  YLChatMoreStatusView.swift
//  Distance
//
//  Created by Q on 2025/7/14.
//

import UIKit
import InputBarAccessoryView

enum YLChatMoreStatusType {
	case more
	case send
	case close
}

class YLChatMoreStatusView: InputBarButtonItem {
	
	let userDefaultsKey = "YLChatMoreStatusView_RedDian"
	
	var buttonActionBlock: ((YLChatMoreStatusType) -> Void)?
	
	lazy var button: UIButton = {
		let button = UIButton()
		button.setImage(UIImage(named: "lt_new_more"), for: .normal)
		return button
	}()
	
	lazy var redDianView: UIView = {
		let redView = UIView()
		redView.backgroundColor = .red
		redView.layer.masksToBounds = true
		redView.layer.borderColor = UIColor.white.cgColor
		redView.layer.cornerRadius = 3
		redView.layer.borderWidth = 1
		return redView
	}()
	
	var statusType: YLChatMoreStatusType? {
		didSet {
			switch statusType {
			case .more:
				button.setImage(UIImage(named: "lt_new_more"), for: .normal)
				redDianSendHiddenChange(hidden: false)
			case .send:
				button.setImage(UIImage(named: "lt_new_send"), for: .normal)
				redDianSendHiddenChange(hidden: true)
			case .close:
				button.setImage(UIImage(named: "lt_new_close"), for: .normal)
				redDianSendHiddenChange(hidden: false)
			default:
				break
			}
		}
	}

	override init(frame: CGRect) {
		super.init(frame: frame)
		setupUI()
	}
	
	required init?(coder: NSCoder) {
		super.init(coder: coder)
		setupUI()
	}

	func setupUI() {
		addSubview(button)
		button.addTarget(self, action: #selector(buttonAction), for: .touchUpInside)
		button.snp.makeConstraints { make in
			make.edges.equalToSuperview()
		}
		
		guard UserDefaults.standard.string(forKey: userDefaultsKey) == nil else { return }
		addSubview(redDianView)
		redDianView.snp.makeConstraints { make in
			make.top.trailing.equalToSuperview().inset(3)
			make.size.equalTo(CGSizeMake(6, 6))
		}
	}
	
	private func redDianSendHiddenChange(hidden: Bool) {
		guard UserDefaults.standard.string(forKey: userDefaultsKey) == nil else { return }
		redDianView.isHidden = hidden
	}
	
	@objc func buttonAction() {
		buttonActionBlock?(self.statusType ?? .more)
	}
	
	func removeRedDian() {
		guard UserDefaults.standard.string(forKey: userDefaultsKey) == nil else { return }
		UserDefaults.standard.set("1", forKey: userDefaultsKey)
		redDianView.removeFromSuperview()
	}
}
