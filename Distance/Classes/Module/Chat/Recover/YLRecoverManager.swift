//
//  YLRecoverManager.swift
//  Distance
//
//  Created by 朱圳 on 2021/8/30.
//

import UIKit
import HyphenateChat
import QCloudCOSXML
import Moya

class YLRecoverManager: NSObject {
	
	var isStop = false
	
	var completion: (() -> Void)?
	
	static let shared = YLRecoverManager()
	
	var loadingView: YLRecoverLoadingAlertView?
	
	var countNum = 0
	
	//MARK: - 备份聊天数据到云端
	
	//备份
	func startBackup(completion: (() -> Void)?)
	{
		self.isStop = false
		self.completion = completion
		
		SQLiteManager.manager.openDB(.em_recover_database)
		
		self.loadingView = YLRecoverLoadingAlertView.show(text: "数据上传至云端中…") {
			
			self.isStop = true
			topWindow?.yl_makeToast("取消该次备份")
			SQLiteManager.manager.dropTable()
			self.completion?()
		}
		countNum = 0
		loadMessagesStart(searchDirection: .up)
		
	}
	
	
	func loadMessagesStart(from fromId: String? = nil, pageSize: Int = 20, searchDirection: EMMessageSearchDirection, completion: (([EMChatMessage]?, EMError?) -> Void)? = nil) {
		
		if self.isStop { return }
		
		let conversation = YLChatManager.shared.conversation
		conversation?.loadMessagesStart(fromId: fromId, count: Int32(pageSize), searchDirection: searchDirection, completion: { messages, error in
			if let messages = messages, error == nil {
				Self.dealMessage(messages: messages, pageSize: pageSize, isStop: &self.isStop, countNum: &self.countNum) { [unowned self] isOver in
					
					if isOver {
						//无数据结束
						print("=====完成")
						self.uploadDBFile()
						
					} else {
						let lastMsg = messages.first!
						self.loadMessagesStart(from: lastMsg.messageId, searchDirection: searchDirection)
					}
				}
				
			} else {
				topWindow?.yl_makeToast("备份出错")
				self.loadingView?.hide()
				self.completion?()
			}
			
		})
	}
	
	static func dealMessage(messages: [EMChatMessage], isWriteText: Bool = false, pageSize:Int, isStop: inout Bool,countNum: inout Int, nextBlock: @escaping (_ isOver: Bool) -> (Void)) {
		let outGroup = DispatchGroup()
		let openID: String = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
		let qiniuUpdateTool = QNUploadTool(exkey: "couple2/closer_chat_backup/\(openID)")
		var currentItems: [EMChatMessage] = []
		for item in messages {
			if isStop { return }
			let group = DispatchGroup()
			if item.body.type ==  .image {
				group.enter()
				#if DEBUG
				print("group.enter()")
				#endif
				
				//上传 图片、缩略图
				let body = item.body as! EMImageMessageBody
				if FileManager.default.fileExists(atPath: body.localPath) {
					qiniuUpdateTool.uploadFile(body.localPath) { url, isSuccess in
						if isSuccess {
							body.remotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				else
				{
					//本地无原图上传data
					qiniuUpdateTool.uploadDataFile(body.localPath, body.remotePath) { url, isSuccess in
						if isSuccess {
							body.remotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				
				
				group.enter()
				#if DEBUG
				print("group.enter()")
				#endif
				if FileManager.default.fileExists(atPath: body.thumbnailLocalPath) {
					qiniuUpdateTool.uploadFile(body.thumbnailLocalPath) { url, isSuccess in
						
						if isSuccess {
							body.thumbnailRemotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				else
				{
					//本地无原图上传data
					qiniuUpdateTool.uploadDataFile(body.thumbnailLocalPath, body.thumbnailRemotePath) { url, isSuccess in
						if isSuccess {
							body.thumbnailRemotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				
			} else if item.body.type ==  .video {
				//上传 视频、视频缩略图
				let body = item.body as! EMVideoMessageBody
				group.enter()
				#if DEBUG
				print("group.enter()")
				#endif
				if FileManager.default.fileExists(atPath: body.localPath) {
					qiniuUpdateTool.uploadFile(body.localPath) { url, isSuccess in
						
						if isSuccess {
							body.remotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				else
				{
					//本地无原图上传data
					qiniuUpdateTool.uploadDataFile(body.localPath, body.remotePath) { url, isSuccess in
						if isSuccess {
							body.remotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				group.enter()
				#if DEBUG
				print("group.enter()")
				#endif
				if FileManager.default.fileExists(atPath: body.thumbnailLocalPath ?? "") {
					qiniuUpdateTool.uploadFile(body.thumbnailLocalPath ?? "") { url, isSuccess in
						if isSuccess {
							body.thumbnailRemotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
				else
				{
					//本地无原图上传data
					qiniuUpdateTool.uploadDataFile(body.thumbnailLocalPath ?? "", body.thumbnailRemotePath ?? "") { url, isSuccess in
						if isSuccess {
							body.thumbnailRemotePath = url
						}
						group.leave()
						#if DEBUG
						print("group.leave()")
						#endif
					}
				}
			}
			else if item.body.type ==  .voice {
				//上传 音频
				let body = item.body as! EMVoiceMessageBody
				group.enter()
				#if DEBUG
				print("group.enter()")
				#endif
				qiniuUpdateTool.uploadFile(body.localPath) { url, isSuccess in
					if isSuccess {
						body.remotePath = url
					}
					group.leave()
					#if DEBUG
					print("group.leave()")
					#endif
				}
				
			}
			else if item.body.type ==  .custom {
				
				if let body = item.body as? EMCustomMessageBody {
					if body.event == CustomMessageRelationLevelUp || body.event == CustomMessageRelationLevelNotice || body.event == CustomMessagePlaceholder || body.event == CustomMessageRelationDisappear || body.event == CustomMessageCashToast  || body.event == CustomMessageLinkToast || body.event == CustomMessageHighRiskAppToast {
						//恋爱关系 占位 不处理
						continue
					}
				}
			}
			else if item.body.type ==  .cmd {
				//cmd消息不处理
				continue
			}
			
			outGroup.enter()
			#if DEBUG
			print("outGroup.enter()")
			#endif
			countNum += 1
			group.notify(queue: DispatchQueue.main) {
				SQLiteManager.manager.insert(item: item)
				
				// 顺便写入txt文件
				if isWriteText {
					currentItems.append(item)

				}
				outGroup.leave()
				#if DEBUG
				print("outGroup.leave()")
				#endif
			}
		}
		outGroup.notify(queue: DispatchQueue.main) {
			let sortItems = currentItems.sorted(by: {$0.timestamp < $1.timestamp})
			for item in sortItems {
				var textContent = "\(item.from == UserData.shared.userId ? UserData.shared.nickName : UserData.shared.nickNameOther) \(Date(timeIntervalSince1970: TimeInterval(item.timestamp / 1000)).yl.toformatterTimeString(formatter: "yyyy-MM-dd HH:mm:ss")) \n"
				let content = messageFomatter(message: YLChatMessage(item))
				textContent += content
				if let body = item.body as? EMImageMessageBody, body.remotePath != nil {
					textContent += body.remotePath
				}
				if let body = item.body as? EMVideoMessageBody, body.remotePath != nil {
					textContent += body.remotePath
				}
				if let body = item.body as? EMVoiceMessageBody, body.remotePath != nil {
					textContent += body.remotePath
				}
				YLChatWriteTextHelper.writeTxtToFile(content: textContent)
			}
			#if DEBUG
			print("outGroup.notify()")
			#endif
			nextBlock(messages.count < pageSize)
		}

	}

	
	//上传sqlite文件到七牛云
	 func uploadDBFile(){
		
		guard let dbPath = SQLiteManager.manager.dbPath else {
		 return
		}
		let path = NSSearchPathForDirectoriesInDomains(
			 .documentDirectory, .userDomainMask, true
		 ).first!
		let  timeStamp =  Int (Date().timeIntervalSince1970)
		let tempDbPath = "\(path)/\(SQDBName.em_recover_database.rawValue)\(timeStamp).db"
		Self.copyFile(fpath: dbPath, tpath: tempDbPath)
		
		if FileManager.default.fileExists(atPath: tempDbPath) {
			let openID: String = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
			let qiniuUpdateTool = QNUploadTool(exkey: "couple2/closer_chat_backup/\(openID)")
			qiniuUpdateTool.uploadFile(tempDbPath) { url, isSuccess in
				
				if isSuccess {
					
					self.chatBackup(backupurl: url)
				}
				else
				{
					topWindow?.yl_makeToast("备份文件上传错误")
					self.loadingView?.hide()
					SQLiteManager.manager.dropTable()
					self.completion?()
				}
				
				try! FileManager.default.removeItem(atPath: tempDbPath)
				
			}
			
		}
		else
		{
			topWindow?.yl_makeToast("备份文件丢失")
			self.loadingView?.hide()
			SQLiteManager.manager.dropTable()
			self.completion?()
		}
		
	}
	
	
	//上传到服务器
	func chatBackup(backupurl:String){
		
		recoverApi.request(YLRecoverService.chatBackup(backupurl: backupurl)) { result in
			
			result.toJSONMapper(success: { [weak self] json in
				guard let self = self else { return }
				
				let data = json["data"]
				
				if(data.boolValue == true)
				{
					topWindow?.yl_makeToast("备份成功")
					self.loadingView?.hide()
					SQLiteManager.manager.dropTable()
					self.completion?()
				}
				else
				{
					topWindow?.yl_makeToast("备份服务器异常")
					self.loadingView?.hide()
					SQLiteManager.manager.dropTable()
					self.completion?()
				}
				
			
				
			}, failure: { error in
				
				topWindow?.yl_makeToast(error.errorDescription)
				self.loadingView?.hide()
				SQLiteManager.manager.dropTable()
				self.completion?()
			})
		}
	}
	
	//MARK: - 恢复聊天记录至手机
	
	//恢复
	func startRestore(backup_url :String, completion: (() -> Void)?)
	{
		self.isStop = false
		self.completion = completion
		
		self.loadingView = YLRecoverLoadingAlertView.show(text: "数据恢复至手机中…") {
			self.isStop = true
			topWindow?.yl_makeToast("取消该次恢复")
			SQLiteManager.manager.dropTable()
			self.completion?()
		}
		
		downLoadDB(backup_url: backup_url)
	}
	
	// 下载请求
	var downloadProvider:MoyaProvider<YLBGMDownloadService> = {
		return MoyaProvider<YLBGMDownloadService>()
	}()
	
	//下载云端DB
	func downLoadDB(backup_url :String) {
		
		let url = backup_url
		
		let path = NSSearchPathForDirectoriesInDomains(
			.documentDirectory, .userDomainMask, true
		).first!
		
		let dbPath = "\(path)/\(SQDBName.em_recover_database.rawValue).db"
		
		downloadProvider.request(.em_recover_database(url: url, filePath: dbPath)) { result in
			
			switch result {
			case .success:
				print("=====下载成功")
				SQLiteManager.manager.openDB(.em_recover_database)
				self.findData()
			case .failure(_):
				topWindow?.yl_makeToast("数据文件下载失败")
				self.loadingView?.hide()
				self.completion?()
			}
		}
		
	}
	
	
	//查
	func findData(limit: Int = 20, offset: Int = 0)  {
		
		if self.isStop { return }
		
		SQLiteManager.manager.find(limit: limit, offset: offset) { messages in
			
			if (messages.count == -1) {
				topWindow?.yl_makeToast("数据恢复失败")
				self.loadingView?.hide()
				SQLiteManager.manager.dropTable()
				self.completion?()
				return
			}
			
			if messages.count < limit {
				
				topWindow?.yl_makeToast("数据恢复完成")
				self.loadingView?.hide()
				SQLiteManager.manager.dropTable()
				self.completion?()
				return
			}
			
			let newOffset = offset + limit
			
			self.findData(limit: limit, offset: newOffset)
			
		}
		
		
	}
	
	static func copyFile(fpath:String,tpath:String) {
		do{
			//如果已存在，先删除，否则拷贝不了
			let fileManager = FileManager.default
			if fileManager.fileExists(atPath: tpath){
				try fileManager.removeItem(atPath: tpath)
			}
			
			try fileManager.copyItem(atPath: fpath, toPath: tpath)
			
		}catch{}
		
	}
	
}


class QNUploadTool: NSObject {
	
	var exkey: String = ""
	override init() {
		super.init()
	}
	convenience init(exkey: String) {
		self.init()
		self.exkey = exkey
		
	}
	
	//上传七牛云
	func uploadFile(_ path: String,isJoinTime:Bool=false, completionHandler: @escaping (String ,Bool) -> Void) {
		qCloudApi.request(.getToken, completion: { result in
			result.toJSONMapper(success: { json in
				
				let data = json["data"]
				guard let token = data["token"].string,
					  let host = data["host"].string
				else {
					completionHandler(path, false)
					return
				}
				
				let fileURL = URL(fileURLWithPath: path)
				let lastPath = isJoinTime ? "\(Int(Date().timeIntervalSince1970))\(fileURL.lastPathComponent)" : fileURL.lastPathComponent
				YLQCloudApi.uploadManger.putFile(path, key: "\(self.exkey)/\(lastPath)", token: token) { info, key, resp in
					guard let key = resp?["key"] as? String else {
						completionHandler(path, false)
						print("=====上传失败")
						return
					}
					
					let urlPath = host + "/" + key
					
					print("=====\(urlPath)")
					
					completionHandler(urlPath, true)
				}
				
			}, failure: { error in
				completionHandler(path, false)
			})
		})
	}
	
	//上传data七牛云
	func uploadDataFile(_ path: String,_ url: String, completionHandler: @escaping (String ,Bool) -> Void) {
		
		qCloudApi.request(.getToken, completion: { result in
			result.toJSONMapper(success: { json in
				
				let data = json["data"]
				guard let token = data["token"].string,
					  let host = data["host"].string
				else {
					completionHandler(path, false)
					return
				}
				
				DispatchQueue.global(qos: .default).async {
					guard let dataUrl = NSURL(string: url)
					else {
						completionHandler(path, false)
						return
					}
					
					let fileData = try? Data(contentsOf: dataUrl as URL, options: NSData.ReadingOptions.mappedIfSafe)
					
					let fileURL = URL(fileURLWithPath: path)
					YLQCloudApi.uploadManger.put(fileData ?? Data(), key: "\(self.exkey)/\(fileURL.lastPathComponent)", token: token) { info, key, resp in
						guard let key = resp?["key"] as? String else {
							completionHandler(path, false)
							print("=====上传失败")
							return
						}
						
						let urlPath = host + "/" + key
						
						print("=====\(urlPath)")
						
						completionHandler(urlPath, true)
						
					}
				}
				
			}, failure: { error in
				completionHandler(path, false)
			})
		})
	}
}
