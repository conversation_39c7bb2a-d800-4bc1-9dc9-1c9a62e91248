//
//  YLFingertipKissViewController.swift
//  Distance
//
//  Created by Q on 2025/7/15.
//

import UIKit
import Jelly
import Lottie

struct YLTouchCoordinates {
	let xRatio: CGFloat
	let yRatio: CGFloat
	let fingerCancel: Bool
	
	init?(dictionary: [String: Any]) {
		guard let x = dictionary["xRatio"] as? CGFloat,
			  let y = dictionary["yRatio"] as? CGFloat,
			  let fingerCancel = dictionary["fingerCancel"] as? Bool else {
			return nil
		}
		self.xRatio = x
		self.yRatio = y
		self.fingerCancel = fingerCancel
	}
}


class YLFingertipKissViewController: UIViewController, CustomNavigation {
	
	static let shared = YLFingertipKissViewController()
	
	static var animator: Animator?

	var customNavStyle: NavStyle = .clear
	
	private var timer: Timer?
	private var kissSNumber: Int = 0
	
	//长按一直发透传
	private var kissRewardTimer: Timer?
	
	@IBOutlet weak var topTitleLab: UILabel!
	@IBOutlet weak var kissImageView: UIImageView!
	@IBOutlet weak var titleLab: UILabel!
	@IBOutlet weak var promptLab: UILabel!
	@IBOutlet weak var kissTouchView: UIView!
	
	static func showAlert(from viewController: UIViewController) -> YLFingertipKissViewController {
		YLStatisticsHelper.trackEventUILaunch(dic: ["ui_path":"指尖亲吻"])
		let controller = YLFingertipKissViewController.shared
		controller.viewShowing = true
		let size = PresentationSize(width: .fullscreen, height: .custom(value: kScreenHeight))
		let alignment = PresentationAlignment(vertical: .bottom, horizontal: .center)
		let uiConfiguration = PresentationUIConfiguration(cornerRadius: 0, backgroundStyle: .dimmed(alpha: 0.8), isTapBackgroundToDismissEnabled: false, corners: [.layerMaxXMinYCorner, .layerMinXMinYCorner])
		let interaction = InteractionConfiguration(presentingViewController: viewController, completionThreshold: 0.3, dragMode: .canvas, mode: .dismiss)
		let presentation = CoverPresentation(directionShow: .bottom, directionDismiss: .bottom, uiConfiguration: uiConfiguration, size: size, alignment: alignment, interactionConfiguration: interaction)
		let animator = Animator(presentation: presentation)
		let nav = YLNavigationViewController(rootViewController: controller)
		animator.prepare(presentedViewController: nav)
		self.animator = animator
		viewController.present(nav, animated: true, completion: nil)
		return controller
	}
	
	var viewShowing: Bool = false {
		didSet {
			guard let promptLab = promptLab else { return }
			promptLab.isHidden = !viewShowing
		}
	}
	
	lazy var taTouchImageView: UIImageView = {
		let taView = UIImageView(image: UserData.shared.genderOther == .female ? UIImage(named: "lt_new_kiss_zw_nv") : UIImage(named: "lt_new_kiss_zw_nan"))
		taView.frame = CGRect(x: 0, y: 0, width: 70, height: 70)
		taView.isHidden = true
		taView.alpha = 0.7
		return taView
	}()

	lazy var meTouchImageView: UIImageView = {
		let meView = UIImageView(image: UserData.shared.gender == .female ? UIImage(named: "lt_new_kiss_zw_nv") : UIImage(named: "lt_new_kiss_zw_nan"))
		meView.frame = CGRect(x: 0, y: 0, width: 70, height: 70)
		meView.isHidden = true
		return meView
	}()
	
	lazy var kissAnimationView: LottieAnimationView = {
		let stampAnimationView = LottieAnimationView()
		let animation = LottieAnimation.named("data嘴唇")
		stampAnimationView.animation = animation
		stampAnimationView.play(fromProgress: 0, toProgress: 1, loopMode: .loop, completion: nil)
		return stampAnimationView
	}()
	
	lazy var loveStarAnimationView: LottieAnimationView = {
		let stampAnimationView = LottieAnimationView()
		let animation = LottieAnimation.named("data满屏爱心")
		stampAnimationView.animation = animation
		stampAnimationView.play(fromProgress: 0, toProgress: 1, loopMode: .loop, completion: nil)
		return stampAnimationView
	}()
	
	var taTouchImageViewVisible: Bool = true {
		didSet {
			hapticAction()
			taTouchImageView.isHidden = taTouchImageViewVisible
		}
	}
	
	var meTouchImageViewVisible: Bool = true {
		didSet {
			hapticAction()
			meTouchImageView.isHidden = meTouchImageViewVisible
		}
	}

    override func viewDidLoad() {
        super.viewDidLoad()
		
		let longPressRecognizer = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
		longPressRecognizer.minimumPressDuration = 0.1
		longPressRecognizer.allowableMovement = 15
		kissTouchView.addGestureRecognizer(longPressRecognizer)
		kissTouchView.isUserInteractionEnabled = true
		
		kissTouchView.addSubview(taTouchImageView)
		kissTouchView.addSubview(meTouchImageView)
    }
	
	override func viewWillAppear(_ animated: Bool) {
		super.viewWillAppear(animated)
		topTitleLab.text = "chat_extend_kiss".localized
		promptLab.text = "chat_extend_kiss_hint".localized
	}
	
	@objc func handleLongPress(_ gestureRecognizer: UILongPressGestureRecognizer) {
		guard let squareView = gestureRecognizer.view else { return }
		let touchPoint = gestureRecognizer.location(in: squareView)
		// 计算横向和纵向比例（相对于正方形视图的宽高）
		let xBl = touchPoint.x / squareView.bounds.width
		let yBl = touchPoint.y / squareView.bounds.height
		switch gestureRecognizer.state {
		case .began:
			YLStatisticsHelper.trackEventByShushu("fingertip_kiss", dic: ["action":"发起互动"], isOnce: true)
			// 长按开始
			meTouchImageView.center = touchPoint
			meTouchImageViewVisible = false
			YLChatCMDManager.defalut().sendChatThumbKissReward(xRatio: xBl, yRatio: yBl)
			
			// 启动定时器，每隔5秒发送一次
			kissRewardTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
				guard let self = self else { return }
				YLChatCMDManager.defalut().sendChatThumbKissReward(xRatio: xBl, yRatio: yBl)
			}

		case .ended, .cancelled, .failed:
			// 长按结束或取消
			kissRewardTimer?.invalidate()
			kissRewardTimer = nil
			YLChatCMDManager.defalut().sendChatThumbKissReward(fingerCancel: true)
			meTouchImageViewVisible = true
			
		default:
			break
			
		}
	}
	
	//透传方法{"xRatio":0.5,"yRatio":0.5}
	func taKissAction(with parameters: [String: Any]) {
		guard viewShowing else { return }
		guard let coordinates = YLTouchCoordinates(dictionary: parameters) else {
			DDLogInfo("指尖kiss透传的数据不对")
			return
		}
		if coordinates.fingerCancel {
			taTouchImageViewVisible = true
			return
		}
		//对方未结束kiss，防止连续的透传导致异常
		if !taTouchImageViewVisible { return }
		YLStatisticsHelper.trackEventByShushu("fingertip_kiss", dic: ["action":"接受互动"], isOnce: true)
		let actualX = coordinates.xRatio
		let actualY = coordinates.yRatio
		let xPosition = kissTouchView.bounds.width * actualX
		let yPosition = kissTouchView.bounds.height * actualY
		
		taTouchImageView.center = CGPointMake(xPosition, yPosition)
		taTouchImageViewVisible = false
	}
	
	func hapticAction() {
		//指纹图片实际显示较小，所以缩减宽度再计算重叠
		let taNewFrame = CGRect(x: taTouchImageView.frame.minX, y: taTouchImageView.frame.minY, width: taTouchImageView.frame.width - 35, height: taTouchImageView.frame.height - 8)
		let meNewFrame = CGRect(x: meTouchImageView.frame.minX, y: meTouchImageView.frame.minY, width: meTouchImageView.frame.width - 35, height: meTouchImageView.frame.height - 8)
		let overlap = taNewFrame.intersects(meNewFrame)
		if !taTouchImageViewVisible && !meTouchImageViewVisible && overlap {//都在按 且重叠
			YLStatisticsHelper.trackEventByShushu("fingertip_kiss", dic: ["action":"亲吻成功"], isOnce: false)
			YLHapticManager.shared.playKissSuccessPattern()
			titleLab.isHidden = false
			showKissTitle()
			showOrHiddenKiss(showKissAnmation: true)
			showOrHiddenLoveStar(showLoveAnmation: true)
			promptLab.isHidden = true
		} else if taTouchImageViewVisible && meTouchImageViewVisible { //都没按
			YLHapticManager.shared.stopCurrentPattern()
			titleLab.isHidden = true
			stopTimer()
			showOrHiddenKiss(showKissAnmation: false)
			showOrHiddenLoveStar(showLoveAnmation: false)
		} else { //单方 或者未重叠
			YLHapticManager.shared.playNotOverlappingPattern()
			titleLab.isHidden = false
			titleLab.text = !taTouchImageViewVisible ? localizedString(key: "chat_extend_kiss_pair_here", substitutions: ["1$":UserData.shared.nickNameOther]) : "chat_extend_kiss_waiting".localized
			stopTimer()
			showOrHiddenKiss(showKissAnmation: true)
			showOrHiddenLoveStar(showLoveAnmation: false)
			promptLab.isHidden = true
		}
	}
	
	func areViewsWithMaskOverlapping(view1: UIView, view2: UIView) -> Bool {
		// 创建路径作为 mask
		let mask1 = CAShapeLayer()
		mask1.path = UIBezierPath(ovalIn: view1.bounds).cgPath
		
		let mask2 = CAShapeLayer()
		mask2.path = UIBezierPath(ovalIn: view2.bounds).cgPath
		
		// 临时应用 mask
		view1.layer.mask = mask1
		view2.layer.mask = mask2
		
		// 获取两视图的可见区域
		let visibleRect1 = view1.convert(view1.bounds, to: nil).intersection(UIScreen.main.bounds)
		let visibleRect2 = view2.convert(view2.bounds, to: nil).intersection(UIScreen.main.bounds)
	
		view1.layer.mask = nil
		view2.layer.mask = nil
		
		// 检查可见区域是否重叠
		return visibleRect1.intersects(visibleRect2)
	}
	
	private func showOrHiddenKiss(showKissAnmation: Bool) {
		kissImageView.isHidden = showKissAnmation
		if !showKissAnmation {
			kissAnimationView.removeFromSuperview()
			return
		}
		kissImageView.superview?.addSubview(kissAnimationView)
		kissAnimationView.snp.makeConstraints { make in
			make.center.equalTo(kissImageView.snp.center)
			make.size.equalTo(CGSizeMake(56, 44))
		}
	}
	
	private func showOrHiddenLoveStar(showLoveAnmation: Bool) {
		if !showLoveAnmation {
			loveStarAnimationView.removeFromSuperview()
			return
		}
		view.addSubview(loveStarAnimationView)
		loveStarAnimationView.snp.makeConstraints { make in
			make.edges.equalToSuperview()
		}
	}
	
	override func viewDidDisappear(_ animated: Bool) {
		super.viewDidDisappear(animated)
		viewShowing = false
		YLHapticManager.shared.stopCurrentPattern()
		taTouchImageViewVisible = true
	}
	
	func showKissTitle() {
		kissSNumber = 0
		stopTimer()
		timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] _ in
			guard let self = self else { return }
			self.kissSNumber += 1
			self.titleLab.text = "\("chat_extend_kissing".localized) \(kissSNumber)s"
		}
	}
	
	func stopTimer() {
		timer?.invalidate()
		timer = nil
	}

	@IBAction func whyButtonAction(_ sender: Any) {
		let alertController = YLAlertViewController.alertController(title: "chat_extend_kiss_is_what".localized, message: "chat_extend_kiss_introduction".localized)
		let cancelAction = YLAlertAction(title: "chat_extend_to_try".localized, style: .default) { action in
		}
		alertController.add(cancelAction)
		alertController.show(from: self)
		alertController.messageLabel.textAlignment = .left
	}
	
	@IBAction func touchBackgroundViewAction(_ sender: Any) {
		dismiss(animated: true) {
			Self.animator = nil
		}
	}
	
}
