//
//  YLLoginViewController.swift
//  Distance
//
//  Created by ke wen on 2020/11/6.
//

import UIKit
import Jelly
import CocoaLumberjack
import ActiveLabel
import URLNavigator

class YLLoginViewController: DTBaseViewController, CustomNavigation {
	
	
	var customNavStyle: NavStyle = .clear
	
	override var isNavigationBarNeedHidden: Bool { false }
	
	var updateTimes = 0//重复上传Tags，失败继续
	///Apple
	@IBOutlet weak var AppleLoginBtn: UIButton!
	
    @IBOutlet weak var GoogleLoginBtn: UIButton!
	
	@IBOutlet weak var bgImgView: UIImageView!
	
	@IBOutlet weak var loginStackView: UIStackView!
	
    @IBOutlet weak var bannerBgView: UIView!
    
    var loginApi: YLLoginApi?
    
    lazy var privicyLabel: ActiveLabel = {
        let lab = ActiveLabel(frame: .zero)
        lab.numberOfLines = 2
		lab.textAlignment = .center
        lab.text = "login_privacy".localized
		
        lab.textColor = UIColor(R: 46, G: 45, B: 53,A:0.5)
        lab.font = UIFont.systemFont(ofSize: 10, weight: .regular)
        return lab
    }()
	
	lazy var recentlyLoggedinView: UIView = {
		let view = UIView()
		view.backgroundColor = UIColor.hexStringColor(hexString: "#FDE383")
		
		let recentlyLoggedinLab = UILabel()
		recentlyLoggedinLab.text = "login_recent".localized
		recentlyLoggedinLab.font = UIFont.systemFont(ofSize: 13, weight: .semibold)
		recentlyLoggedinLab.textColor = UIColor.hexStringColor(hexString: "#774B41")
		recentlyLoggedinLab.textAlignment = .center
		recentlyLoggedinLab.layer.cornerRadius = 6
		recentlyLoggedinLab.layer.masksToBounds = true
		view.addSubview(recentlyLoggedinLab)
		
		recentlyLoggedinLab.snp.makeConstraints { make in
			make.leading.equalToSuperview().offset(10)
			make.trailing.equalToSuperview().offset(-10)
			make.top.equalToSuperview().offset(6)
			make.bottom.equalToSuperview().offset(-6)
			make.height.equalTo(18.0)
//			make.width.equalTo(recentlyLoggedinLab.intrinsicContentSize.width+10)
		}
		return view
	}()
    
	// 默认滚动视图
	lazy var defaultCycleView: ICycleView = {
		let width = kScreenWidth
		let height = width / 393.0 * 591.0
		let cycleView = ICycleView(frame: CGRect(x: 0, y: 0, width: width, height: height))
		cycleView.imgViewWidth = width
		cycleView.pageControlStyle = .none
		cycleView.register([UINib(nibName: "YLLoginBannerCollectionViewCell", bundle: Bundle.main)], identifiers: ["YLLoginBannerCollectionViewCell"])
		cycleView.delegate = self
		cycleView.itemSize =  CGSize(width: width, height: height)
		cycleView.clipsToBounds = true
		return cycleView
	}()
	
	lazy var pageContrl: GSPageControl = {
		let y = kScreenWidth * (420.0/393.0) - 52 - 20
		let page = GSPageControl(frame: CGRect(x: 0, y: Int(y), width: pictures.count * 16 + 6, height: 6))
		page.numberOfPages = pictures.count
		page.otherPointSize = CGSize(width: 6, height: 6)
		page.currentPointSize = CGSize(width: 16, height: 6)
		page.pointCornerRadius = 3
		page.pageAliment = .Left
		page.leftAndRightSpacing = 3
		page.currentColor = UIColor(0xF7BD22)
		page.otherColor = UIColor(0xFDE383)
		return page
	}()
	
	var pictures: [(String, String, String)] {
		var datas = [(String, String, String)]()
		datas.append(("login_banner_title1".localized, "login_banner_desc1".localized, "login_top_bg_1"))
		datas.append(("login_banner_title2".localized, "login_banner_desc2".localized, "login_top_bg_2"))
		datas.append(("login_banner_title3".localized, "login_banner_desc3".localized, "login_top_bg_3"))
		datas.append(("login_banner_title4".localized, "login_banner_desc4".localized, "login_top_bg_4"))
		return datas
	}
	
	override func viewWillAppear(_ animated: Bool) {
		super.viewWillAppear(animated)
		UserData.shared.resetUserIdAndToken()
		YLChatAccessoryView.shared.setHidden(true)
	}

	
	override func viewDidLoad() {
		super.viewDidLoad()
//		navigationItem.rightBarButtonItem = helpItem
		showQuikLoginViewController()
		
		updateUI()
		YLStatisticsHelper.trackEvent("Login.IM")
		
		NotificationCenter.default.addObserver(self, selector: #selector(networkChanged(_:)), name:  Notification.Name("kReachabilityChangedNotification"), object: nil)
		registerNotify()
	}
	
	func showQuikLoginViewController() {
		guard let loginType = YLLoginStatusManager.getLoginType() else {
			return
		}
		switch loginType {
		case .EmailLogin:
			let vc = YLQuickLoginViewController()
			vc.otherLoginBlock = { [weak self] type in
				guard let self = self else {
					return
				}
				self.login(type: type)
			}
			navigationController?.pushViewController(vc, animated: false)
		case .AppleLogin:
			showRecentlyLoggedInLab(isApple: true)
		case .GoogleLogin:
			showRecentlyLoggedInLab(isApple: false)
		case .Logout:
			break
		}
	}
	
	func showRecentlyLoggedInLab(isApple:Bool) {
		self.view.addSubview(recentlyLoggedinView)
		recentlyLoggedinView.snp.makeConstraints { make in
			make.trailing.equalTo(AppleLoginBtn.snp.trailing).offset(15)
			make.top.equalTo(isApple ? AppleLoginBtn.snp.top : GoogleLoginBtn.snp.top).offset(-20)
			make.height.lessThanOrEqualTo(30.0)
		}
		
		view.layoutIfNeeded()
		recentlyLoggedinView.yl.addCorner(conrners: [.topLeft, .topRight, .bottomRight], radius: 12.0)
	}

	
	deinit {
		NotificationCenter.default.removeObserver(self)
	}

}

extension YLLoginViewController {
	
	
	@objc func tokenInvalid(_ notifi: Notification) {
		if let msg = notifi.object as? String, msg.contains(find: "冻结") {
			view.makeToast(msg)
			NotificationCenter.default.removeObserver(self)
		}
	}
	
	func registerNotify() {
		NotificationCenter.default.addObserver(self, selector: #selector(tokenInvalid(_:)), name: .userTokenInvalidNotificationName, object: nil)
	}
	@objc func networkChanged(_ notify: NSNotification) {
		if let reach = notify.object as? Reachability, !reach.isReachable() {
			return
		}
	}
	

	func updateUI() {
		initBannerUI()
		
		GoogleLoginBtn.yl.layer(radius: 27, borderWidth: 1.0, borderColor: .init(0xF0EDD7))
		
		if #available(iOS 13.0, *) {
			AppleLoginBtn.isHidden = false
		} else {
			AppleLoginBtn.isHidden = true
		}
		
		let privacyText = "login_privacy".localized
		let customType = ActiveType.custom(pattern: "\("login_privacy_agreement".localized)|\("login_user_agreement".localized)")
		
		view.addSubview(privicyLabel)
		privicyLabel.snp.makeConstraints { make in
			make.left.right.equalToSuperview().inset(45)
			make.bottom.equalToSuperview().offset(-22)
		}
        privicyLabel.enabledTypes = [customType]
        privicyLabel.text = privacyText
        privicyLabel.customColor[customType] = UIColor(0xAA5A2B)
        privicyLabel.lineSpacing = 0
        privicyLabel.font = UIFont.systemFont(ofSize: 11)
        privicyLabel.handleCustomTap(for: customType) { [weak self] element in
            let controller: UIViewController
			if element.contains("login_privacy_agreement".localized) {
                controller = YLWebViewController(urlString: YLServerUrlCenter.shared.privacy, webTitle: "login_privacy_agreement".localized)
            } else {
				controller = YLWebViewController(urlString: YLServerUrlCenter.shared.userPrivacy, webTitle: "login_user_agreement".localized)
            }
            self?.navigationController?.pushViewController(controller, animated: true)
        }
		
	}
}

extension YLLoginViewController {


	@IBAction func AppleLoginBtnTapped(_ sender: Any) {
		#if DEBUG
		let selectDate = Date.datefromString(string:"2008-01-01T00:00:00Z", newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
		let picker = YLSelectDatePickerView(frame: .zero, date: selectDate, isLoveTime: false)
		picker.show()
		#endif
		
		YLStatisticsHelper.trackEvent("Login.CK", dic: ["pt" : "苹果登录"])
		guard checkNetworkStatus() else {
			return
		}
		
		login(type: .apple)
	}
    @IBAction func GoogleLoginBtnTapped(_ sender: Any) {
        YLStatisticsHelper.trackEvent("Login.CK", dic: ["pt" : "谷歌登录"])
        guard checkNetworkStatus() else {
            return
        }
        
        login(type: .google)
    }
    
    @IBAction func MetaLoginBtnTapped(_ sender: Any) {
        YLStatisticsHelper.trackEvent("Login.CK", dic: ["pt" : "脸书登录"])
        guard checkNetworkStatus() else {
            return
        }
        
		login(type: .Meta)
    }
    
    @IBAction func MailLoginBtnTapped(_ sender: Any) {
        YLStatisticsHelper.trackEvent("Login.CK", dic: ["pt" : "邮箱登录"])
        guard checkNetworkStatus() else {
            return
        }
		let emailViewController = YLEmailLoginViewController()
		navigationController?.pushViewController(emailViewController, animated: true)
    }
    
	func checkNetworkStatus() -> Bool {
		if let appDelegate = AppDelegate.shared, !appDelegate.reach.isReachable() {
//			YLDistanceAlertView.show(superView: view!, describ: "请检查网络连接后，重新尝试登录", mainTitle: "当前网络不可用", leftTitle: "cancel".localized, rightTitle: "去设置") { [weak self] in
//				guard let _ = self else { return }
//				UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
//			} cancelAction: {
//				
//			}
			topWindow?.yl_makeToast("YL_NetworkConnectionFailed".localized)
			return false
		}
		return true
	}
	
	
}

extension YLLoginViewController {
	
	func login(type: YLLoginPlatformType) {
//		print("本地密码验证", manager.ownerAuthentication())
//		manager.authentication()
//		DDLogInfo("点击登录")
//		return
		registerNotify()
		UserData.shared.loginType = type.rawValue
		loginStackView.isUserInteractionEnabled = false
		DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(3), execute: { [weak self] in
			self?.loginStackView.isUserInteractionEnabled = true
		})
		loginApi = YLLoginApi(type: type)
		loginApi?.startSelfLogin = { isShow in
			YLHUDView.showLoading(view: topWindow)
		}
		loginApi?.login(completion: { [weak self](result) in
			guard let self = self else {
				return
			}
			YLHUDView.dismiss(view: topWindow)

			switch result {
			case let .success(response):
				switch response {
				case let .success(userData):
					Self.onLoginSuccess(userData: userData, viewController: self, type: type)
				case let .mutipleUsers(users):
					let controller = YLAccountSelectionViewController()
					controller.accounts = users
					controller.loginPlatformType = .PNS
					self.navigationController?.pushViewController(controller, animated: true)
					break
				}
				
			case let .failure(error):
				Self.onLoginFailed(error, viewController: self)
			}
		})
	}
	
	static func onLoginFailed(_ error: YLError, viewController: UIViewController) {
		DDLogInfo(error)
		switch error {
		case .userCanceled:
			break
		case let .invalideStatusCode(code, message):
			if code != 20901 {
				AppDelegate.shared?.window?.yl_makeToast(message, position: .center)
				YLStatisticsHelper.trackEvent("Login.Toast.IM", dic: ["toast" : message])
			}
			
		case .moyaError:
			AppDelegate.shared?.window?.yl_makeToast("NET ERROR", position: .center)
			YLStatisticsHelper.trackEvent("Login.Toast.IM", dic: ["toast" : "网络异常，请检查网络"])
		default:
			AppDelegate.shared?.window?.yl_makeToast("YL_Loginfailed".localized, position: .center)
			YLStatisticsHelper.trackEvent("Login.Toast.IM", dic: ["toast" : "登录失败"])
			break
		}
	}
	
	static func onLoginSuccess(userData:UserData, viewController: UIViewController, type: YLLoginPlatformType) {
		DDLogInfo("登录成功")
		var trackDic = [String: Any]()
		trackDic["pt"] = type.str
		trackDic["type"] = type.str
		trackDic["infoComplete"] = userData.matchStatus != .none
		YLStatisticsHelper.trackEvent("Login.SUC", dic: trackDic)
		userData.onUserLogin()
		
		switch type {
		case .apple:
			YLLoginStatusManager.saveLoginType(loginType: .AppleLogin)
		case .google:
			YLLoginStatusManager.saveLoginType(loginType: .GoogleLogin)
		case .Email:
			YLLoginStatusManager.saveLoginType(loginType: .EmailLogin)
		default:
			break
		}
		
		
		/*取消弹绑定手机号
		//是否绑定手机号
		let bindPhone = WCOnlieParameter.default.parameterMap?["bindPhone"].bool ?? false
		if userData.phone == nil && bindPhone {
			let bindVc = UIStoryboard(name: "login", bundle: nil).instantiateViewController(withIdentifier: "ylPhoneInputViewController")!
			bindVc.form = .login
			let nv = YLNavigationViewController(navigationBarClass: YLNavigationBar.self, toolbarClass: nil)
			nv.setViewControllers([bindVc], animated: false)
			nv.modalPresentationStyle = .fullScreen
			viewController.present(nv, animated: true, completion: nil)

		} else {
			AppDelegate.shared?.chooseRootVCWith(userData: userData)
		}
		 */
//		AppDelegate.shared?.chooseRootVCWith(userData: userData)
		serviceProvider.request(.userDesk) { result in
			result.toJSONMapper { json in
				UserData.shared.homeViewType = json["data"]["desk_type"].intValue
				AppDelegate.shared?.chooseRootVCWith(userData: userData)
				UserDefaults.standard.setValue(type.rawValue, forKey: "kLoginPlatformType")
				UserDefaults.standard.synchronize()
			} failure: { error in

			}
		}
	}
	
}

//MARK: 顶部Banner
extension YLLoginViewController: ICycleViewDelegate {
	
	private func initBannerUI() {
		bannerBgView.addSubview(defaultCycleView)
		defaultCycleView.snp.makeConstraints { make in
			make.edges.equalToSuperview()
		}
		defaultCycleView.pictures = pictures.map { $0.2 }
		
		bannerBgView.addSubview(pageContrl)
		pageContrl.snp.makeConstraints { make in
			make.leading.equalToSuperview().offset(40)
			make.trailing.equalToSuperview()
			make.height.equalTo(6)
			make.bottom.equalToSuperview().offset(-80.0)
		}
		pageContrl.numberOfPages = pictures.count
		pageContrl.currentPage = 0
	}
	
	func iCycleView(cycleView: ICycleView, autoScrollingItemAt index: Int) {
		pageContrl.currentPage = index
	}
	
	// 自定义Cell
	func iCycleView(cycleView: ICycleView, collectionView: UICollectionView, cellForItemAt indexPath: IndexPath, picture: String) -> UICollectionViewCell {
		let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "YLLoginBannerCollectionViewCell", for: indexPath) as! YLLoginBannerCollectionViewCell
		let (title, subTitle, imagName) = pictures[indexPath.row]
		cell.imageView.image = UIImage(named: imagName)
		cell.titleLab.text = title
		cell.descriptionLab.text = subTitle
		return cell
	}
	
}
