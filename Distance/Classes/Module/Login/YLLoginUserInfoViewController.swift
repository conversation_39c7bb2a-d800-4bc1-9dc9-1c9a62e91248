//
//  YLLoginUserInfoViewController.swift
//  Distance
//
//  Created by ke wen on 2020/11/11.
//

import UIKit
import CocoaLumberjack
import SDWebImage
import ThinkingSDK
import URLNavigator

class YLLoginUserInfoViewController: DTBaseViewController, CustomNavigation {
	var customNavStyle: NavStyle = .clear
	
	var isFormMatchPage = false
	
	override var isNavigationBarNeedHidden: Bool {
		if isFormMatchPage {
			return false
		}
		return true
	}
	
    @IBOutlet weak var avatarImgView: UIImageView!
    
    @IBOutlet weak var nickNameBgView: UIView!
    @IBOutlet weak var nickNameTextField: UITextField!
    
    @IBOutlet weak var birthDayTextField: UITextField!
    
    @IBOutlet weak var chooseBoyBtn: UIButton!
    
    @IBOutlet weak var chooseGirlBtn: UIButton!
    
    @IBOutlet weak var nextBtn: UIButton!
	
	@IBOutlet weak var nextBtnY: NSLayoutConstraint!
	var editView: YLPetEditNameView?
    
    var imgUrl: String?
    
	var birthDayDate: Date? //= Date.datefromString(string: UserData.shared.birthDayDate, newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
    
    var gender: Gender = .none
    
	//本地头像
	let malepPicker = YLSequentialPicker(items: ["tx_boy", "tx_badge1", "tx_badge2"])
	let femalPicker = YLSequentialPicker(items: ["tx_girl", "tx_badge3", "tx_badge4"])
	
    override func viewDidLoad() {
        super.viewDidLoad()
		
		let str = NSAttributedString(string: "register_input_nickname".localized, attributes: [NSAttributedString.Key.foregroundColor:UIColor(0x2E2D35, alpha: 0.4)])
		self.nickNameTextField.attributedPlaceholder = str
		nickNameTextField.delegate = self
		
		let birthdayStr = NSAttributedString(string: "register_input_birthday".localized, attributes: [NSAttributedString.Key.foregroundColor:UIColor(0x2E2D35, alpha: 0.4)])
		self.birthDayTextField.attributedPlaceholder = birthdayStr
		
        updateUI()
        // Do any additional setup after loading the view.
		DDLogInfo("进入首次填写个人资料页面")
		
    }

    func updateUI() {
        chooseBoyBtn.setTitle("match_info_male".localized, for: .normal)
		chooseBoyBtn.titleLabel?.font =  UIFont(name: "SF-Pro-Rounded-Heavy", size: 24)
		chooseBoyBtn.setTitleColor(UIColor.init(white: 1.0, alpha: 0.5), for: .normal)
        chooseGirlBtn.setTitleColor(UIColor.init(white: 1.0, alpha: 0.5), for: .normal)
		chooseBoyBtn.setTitleColor(.white, for: .selected)
        chooseGirlBtn.setTitleColor(.white, for: .selected)
        chooseGirlBtn.setTitle("match_info_female".localized, for: .normal)
        chooseGirlBtn.titleLabel?.font =  UIFont(name: "SF-Pro-Rounded-Heavy", size: 24)
        nextBtn.titleLabel?.font = UIFont(name: "PingFangSC-Medium", size: 16)
		nextBtn.setTitle("next_step".localized, for: .normal)
//		self.birthDayTextField.text = "点击选择生日"
		if let birthDayDate = birthDayDate {
			self.birthDayTextField.text = Date.stringfromDate(date: birthDayDate, newformatter: "dd.MM.yyyy")
		}
        avatarImgView.layer.cornerRadius =  avatarImgView.frame.size.width/2
        avatarImgView.layer.borderWidth = 2
        avatarImgView.layer.borderColor = #colorLiteral(red: 1, green: 1, blue: 1, alpha: 1)
        avatarImgView.layer.masksToBounds = true
		
		let userData = UserData.shared
		if userData.tempData.nickName == "" && userData.tempData.avatar == "" && userData.tempData.gender == .none {
			userData.tempData = (nickName: userData.nickName , avatar: userData.avatar , gender: userData.gender)
		}
        
		avatarImgView.sd_setImage(with: URL(string: userData.tempData.avatar), placeholderImage: userData.avatarPlaceholderImage)
        nickNameTextField.text = userData.tempData.nickName
        if userData.tempData.gender == .none {
			userData.tempData.gender = .female
			genderBtnTapped(gender: .female)
		} else {
			genderBtnTapped(gender: userData.tempData.gender)
		}
		
		UserData.shared.birthdaySelfNunar = false
        
		if isFormMatchPage {
			nextBtn.setTitle("提交", for: .normal)
			avatarImgView.sd_setImage(with: URL(string: userData.avatar), placeholderImage: userData.avatarPlaceholderImage)
			nickNameTextField.text = userData.nickName
			
			genderBtnTapped(gender: userData.gender)
		}
    }
    
	fileprivate func nextBtnSelect() {
		self.nextBtn.setBackgroundImage(UIImage(named: "user_info_nextSel"), for: .normal)
		nextBtn.setTitleColor(UIColor(0x774B41), for: .normal)
	}
	
	fileprivate func nextBtnNoraml() {
		self.nextBtn.setBackgroundImage(UIImage(named: "user_info_nextNor"), for: .normal)
		nextBtn.setTitleColor(UIColor(R:46, G:45, B:53, A:0.4), for: .normal)
	}
	
	@IBAction func nickNameTextChanged(_ textView: UITextField) {
        if textView.text!.count > 0 {
            //获得已输出字数与正输入字母数
            let selectRange = textView.markedTextRange
            //获取高亮部分 － 如果有联想词则解包成功
            if let selectRange = selectRange {
                let position =  textView.position(from: (selectRange.start), offset: 0)
                if (position != nil) {
                    return
                }
            }
            let textContent = textView.text!
            let textNum = textContent.count
            if textNum > 10 {
                let str = String(textContent.prefix(10))
                textView.text = str
            }
			
			if let birthDayDate = birthDayDate {
				if chooseBoyBtn.isSelected || chooseGirlBtn.isSelected {
					nextBtnSelect()
				}else {
					nextBtnNoraml()
				}
			}else {
				nextBtnNoraml()
			}
			
		}else {
			nextBtnNoraml()
		}
    }
    
    @IBAction func boyBtnTapped(_ sender: Any) {
        genderBtnTapped(gender: .male)
		DDLogInfo("选中性别男")

    }
    @IBAction func girlBtnTapped(_ sender: Any) {
        genderBtnTapped(gender: .female)
		DDLogInfo("选中性别女")
    }
    
    @IBAction func birthDayChoose(_ sender: Any) {
        nickNameTextField.resignFirstResponder()
		let selectDate = birthDayDate ?? Date.datefromString(string:"2008-01-01T00:00:00Z", newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
		let picker = YLSelectDatePickerView(frame: .zero, date: selectDate, isLoveTime: false)
		picker.show()
		picker.submitActionBlock = { [unowned self] (date, isLunar) in
			UserData.shared.birthdaySelfNunar = isLunar
			self.birthDayDate = date
			if isLunar == true {
				self.birthDayTextField.text = YLChineseCalendar.getCZCalendarYearMonthDayDesc(withYear: date.year, month: date.month, day: date.day)
			} else {
				let dateStr = Date.stringfromDate(date: date, newformatter: "dd.MM.yyyy")
				self.birthDayTextField.text = dateStr
			}
			self.birthDayDate = date
			
			if nickNameTextField.text!.isEmpty || (!chooseBoyBtn.isSelected && !chooseGirlBtn.isSelected) {
				nextBtnNoraml()
			}else {
				nextBtnSelect()
			}
		}
		
    }
    
    @IBAction func nickNameEditTapped(_ sender: Any) {
		showEditView()
		
    }
    func genderBtnTapped(gender: Gender) {
        if gender == .male {
            chooseBoyBtn.isSelected = true
            chooseGirlBtn.isSelected = false
        } else {
            chooseBoyBtn.isSelected = false
            chooseGirlBtn.isSelected = true
        }
        self.gender = gender
		let userData = UserData.shared
		let url = imgUrl ?? userData.tempData.avatar
		let placeholderImage = gender == .male ? UIImage(named: "tx_boy") : UIImage(named: "tx_girl")
		avatarImgView.sd_setImage(with: URL(string: url), placeholderImage: placeholderImage)
		
		guard let birthDayDate = birthDayDate else {
			nextBtnNoraml()
			return
		}
		
		if nickNameTextField.text!.isEmpty {
			nextBtnNoraml()
			return
		}

		nextBtnSelect()
    }
	
	@IBAction func inputNickNameAction(_ sender: UIButton) {
		showEditView()
	}
	
	private func showEditView() {
		let editView = YLPetEditNameView.nickNameShow(superView: navigationController?.view ?? view, nickName: self.nickNameTextField.text!, success: { [weak self] success in
			
			guard
				let self = self,
				let editView = self.editView
				else { return }
			
			self.nickNameTextField.text = editView.nickNameTextField.text
			editView.tapBackground()
			
		})
		self.editView = editView
	}
	
	@IBAction func helpButtonAction(_ sender: UIButton) {
		YLStatisticsHelper.trackEvent("Registered.Help.CK")
//		YLLogTool.uploadErrorFile()
		Navigator.shared.open(Navigator.aboutUs)
	}
    
    @IBAction func nextBtnTapped(_ sender: Any) {
        YLStatisticsHelper.trackEvent("Login.Next.CK")
		DDLogInfo("点击下一步")
        if !chooseBoyBtn.isSelected && !chooseGirlBtn.isSelected {
			view.yl_makeToast("YL_Pleaseselectgender".localized)
            return
        }
        if nickNameTextField.text!.isEmpty {
			view.yl_makeToast("cat_nickname_empty_toast".localized)
            return
        }
        if nickNameTextField.text!.count > 10 {
			view.yl_makeToast("YL_Nicknamemustbecharac".localized)
            return
        }
		
		guard let birthDayDate = birthDayDate else {
			view.yl_makeToast("register_input_birthday".localized)
			return
		}
		
		func summitInfo (avatar:String?) {

			let dateStr = Date.stringfromDate(date: birthDayDate, newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
			userDate.refreshUserData(avatar: avatar, nickName: nickNameTextField.text!, sex: gender.rawValue, birthday: dateStr, is_nunar: UserData.shared.birthdaySelfNunar, avatar_verify: self.avatar_verify, promocode: "") { (result) in
				YLHUDView.dismiss(view: self.view)
				switch result {
				case .success:
					DDLogInfo("首次上传个人信息成功")
					let vc = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "YLMatchPageViewController")
					let nv = YLNavigationViewController(navigationBarClass: YLNavigationBar.self, toolbarClass: nil)
					nv.setViewControllers([vc], animated: false)
					AppDelegate.shared?.window?.rootViewController = nv
					self.avatar_verify = 0
				case let .failure(error):
					DDLogInfo("首次上传个人信息失败")
					DDLogInfo(error)
					self.view.yl_makeToast(error.errorDescription)
					break
				}
			}
		}
		
        let userDate = UserData.shared
		
		if let imgUrl = imgUrl {
			YLHUDView.showLoading(view: view)
			summitInfo(avatar: imgUrl)
		}else if(userDate.tempData.avatar.count == 0) {
			YLHUDView.showLoading(view: view)
			summitInfo(avatar: nil)
		} else {
			YLHUDView.showLoading(view: view)
			SDWebImageManager.shared.loadImage(with: URL(string: userDate.tempData.avatar), options: [.retryFailed], progress: nil) { image, data, _, _, _, _ in
				if let image = image {
					YLImageUploadTool.default.uploadImage(image: image) { [weak self]result in
						switch result {
						case let .success((imgUrl, _, _)):
							self?.avatar_verify = 0
							self?.imgUrl = imgUrl
							summitInfo(avatar: imgUrl)
						case let .failure(error):
							self?.view.makeToast(error.localizedDescription)
						}
					}
				} else {
					summitInfo(avatar: nil)
				}
				
			}
		}
		
       
    }
    
	var avatar_verify = 0
	
    @IBAction func avatarImgTapped(_ sender: Any) {
		DDLogInfo("点击修改头像")

        YLImageUploadTool.default.chooseImgAndGetImgUrl { (result) in
            switch result {
            case let .success((imgUrl, image, _)):
				DDLogInfo("修改头像成功")
				self.avatarImgView.sd_setImage(with: URL(string: imgUrl), placeholderImage: image)
                self.imgUrl = imgUrl
				self.avatar_verify = 1
                break
            case let .failure(error):
				DDLogInfo("修改头像失败")
				DDLogInfo(error)
                break
            }
        }
    }
    
    @IBAction func randomHeaderImgTapped(_ sender: Any) {
		let imageName: String
		if gender == .male {
			imageName = malepPicker.next() ?? ""
		} else {
			imageName = femalPicker.next() ?? ""
		}
		avatarImgView.image = UIImage(named: imageName)
    }
	
}

extension YLLoginUserInfoViewController: UITextFieldDelegate {
	func textFieldDidBeginEditing(_ textField: UITextField) {
		guard textField == nickNameTextField else { return }
		addNickNameBorder(true)
	}
	
	func textFieldDidEndEditing(_ textField: UITextField) {
		guard textField == nickNameTextField else { return }
		if nickNameTextField.text?.count == 0 {
			addNickNameBorder(false)
		}
	}
	
	func addNickNameBorder(_ have: Bool) {
		nickNameBgView.yl.layer(radius: 16, borderWidth: have ? 1.0 : 0.0, borderColor: have ? UIColor(0xF7ECD1) : .clear)
	}

}
