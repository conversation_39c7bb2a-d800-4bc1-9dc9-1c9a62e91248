<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="Kq8-w8-ROF">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--LoginSMS Code View Controller-->
        <scene sceneID="cpp-Fh-ENa">
            <objects>
                <viewController storyboardIdentifier="YLLoginSMSCodeViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="hPk-m8-WuH" customClass="YLLoginSMSCodeViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="DYe-dX-UsD">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dl_bg_img" translatesAutoresizingMaskIntoConstraints="NO" id="CvY-CZ-qTg">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_img" translatesAutoresizingMaskIntoConstraints="NO" id="l1N-Q0-H0H">
                                <rect key="frame" x="152.66666666666666" y="207" width="88" height="88"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="88" id="BNu-QS-kVT"/>
                                    <constraint firstAttribute="width" constant="88" id="Jdk-Zc-oK2"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iub-bV-AnE">
                                <rect key="frame" x="196.66666666666666" y="398" width="0.0" height="0.0"/>
                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                <color key="textColor" red="0.42745098039215684" green="0.42745098039215684" blue="0.42745098039215684" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="B44-ZI-joX">
                                <rect key="frame" x="50.666666666666657" y="434" width="292" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="292" id="KK8-rS-jO3"/>
                                    <constraint firstAttribute="height" constant="50" id="Usx-nr-0gJ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="重新发送（60s）" backgroundImage="cwxq_tc_qx_icon_51">
                                    <color key="titleColor" red="0.68627450980392157" green="0.65490196078431373" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="onResendButtonPressed:" destination="hPk-m8-WuH" eventType="touchUpInside" id="oQr-00-mVl"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3eL-l8-ZIQ">
                                <rect key="frame" x="157.66666666666666" y="491" width="78" height="27"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="收不到验证码？">
                                    <color key="titleColor" red="0.67450980392156867" green="0.36470588235294116" blue="0.1764705882352941" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="onConnectUsAction:" destination="hPk-m8-WuH" eventType="touchUpInside" id="6x3-bm-bHZ"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="lOe-Qp-Iv8"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="B44-ZI-joX" firstAttribute="centerX" secondItem="DYe-dX-UsD" secondAttribute="centerX" id="5QV-mE-MNV"/>
                            <constraint firstItem="CvY-CZ-qTg" firstAttribute="leading" secondItem="DYe-dX-UsD" secondAttribute="leading" id="89i-59-a7m"/>
                            <constraint firstItem="CvY-CZ-qTg" firstAttribute="top" secondItem="DYe-dX-UsD" secondAttribute="top" id="CSl-K1-WQU"/>
                            <constraint firstItem="l1N-Q0-H0H" firstAttribute="centerX" secondItem="DYe-dX-UsD" secondAttribute="centerX" id="IEG-ih-JQa"/>
                            <constraint firstItem="lOe-Qp-Iv8" firstAttribute="trailing" secondItem="CvY-CZ-qTg" secondAttribute="trailing" id="KSU-HE-0Tg"/>
                            <constraint firstItem="3eL-l8-ZIQ" firstAttribute="top" secondItem="B44-ZI-joX" secondAttribute="bottom" constant="7" id="U9o-Ly-ips"/>
                            <constraint firstItem="l1N-Q0-H0H" firstAttribute="top" secondItem="lOe-Qp-Iv8" secondAttribute="top" constant="45" id="g10-hi-XGs"/>
                            <constraint firstAttribute="bottom" secondItem="CvY-CZ-qTg" secondAttribute="bottom" id="gni-Cm-NmI"/>
                            <constraint firstItem="B44-ZI-joX" firstAttribute="top" secondItem="l1N-Q0-H0H" secondAttribute="bottom" constant="139" id="hgY-rn-SRH"/>
                            <constraint firstItem="3eL-l8-ZIQ" firstAttribute="centerX" secondItem="DYe-dX-UsD" secondAttribute="centerX" id="wPH-En-QSF"/>
                            <constraint firstItem="iub-bV-AnE" firstAttribute="centerX" secondItem="DYe-dX-UsD" secondAttribute="centerX" id="xhX-2z-4i4"/>
                            <constraint firstItem="iub-bV-AnE" firstAttribute="top" secondItem="l1N-Q0-H0H" secondAttribute="bottom" constant="103" id="ze4-2V-GNb"/>
                        </constraints>
                    </view>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="bgImageView" destination="CvY-CZ-qTg" id="oes-BN-1Y2"/>
                        <outlet property="infoLabel" destination="iub-bV-AnE" id="qBq-e0-bvu"/>
                        <outlet property="logoImageView" destination="l1N-Q0-H0H" id="Kgm-QU-8cF"/>
                        <outlet property="resendButton" destination="B44-ZI-joX" id="gF3-Nb-baf"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gva-Q0-1In" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="822" y="854"/>
        </scene>
        <!--Login User Info View Controller-->
        <scene sceneID="gXb-aK-HlG">
            <objects>
                <viewController storyboardIdentifier="YLLoginUserInfoViewController" id="Kq8-w8-ROF" customClass="YLLoginUserInfoViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Jew-hD-zAc">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="register_bg" translatesAutoresizingMaskIntoConstraints="NO" id="aq6-eh-4GI">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="36" translatesAutoresizingMaskIntoConstraints="NO" id="8Be-be-gd3">
                                <rect key="frame" x="30" y="307" width="333" height="292"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Gao-OK-pK5">
                                        <rect key="frame" x="0.0" y="0.0" width="333" height="90"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="a8M-xi-Zaz">
                                                <rect key="frame" x="0.0" y="2.6666666666666856" width="333" height="77.333333333333329"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3tF-0h-YVa">
                                                        <rect key="frame" x="0.0" y="0.0" width="160.66666666666666" height="77.333333333333329"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <inset key="titleEdgeInsets" minX="0.0" minY="10" maxX="0.0" maxY="-10"/>
                                                        <state key="normal" backgroundImage="male_unselected"/>
                                                        <state key="selected" backgroundImage="male_selected"/>
                                                        <connections>
                                                            <action selector="boyBtnTapped:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="y9P-xR-YDl"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sU1-BY-R41">
                                                        <rect key="frame" x="172.66666666666663" y="0.0" width="160.33333333333337" height="77.333333333333329"/>
                                                        <inset key="titleEdgeInsets" minX="0.0" minY="10" maxX="0.0" maxY="-10"/>
                                                        <state key="normal" backgroundImage="female_unselected"/>
                                                        <state key="selected" backgroundImage="female_selected"/>
                                                        <connections>
                                                            <action selector="girlBtnTapped:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="SYo-OL-qt1"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="90" id="LOy-FJ-Dfc"/>
                                            <constraint firstAttribute="trailing" secondItem="a8M-xi-Zaz" secondAttribute="trailing" id="XpP-i3-dTw"/>
                                            <constraint firstAttribute="bottom" secondItem="a8M-xi-Zaz" secondAttribute="bottom" constant="10" id="iC5-JU-tye"/>
                                            <constraint firstItem="a8M-xi-Zaz" firstAttribute="leading" secondItem="Gao-OK-pK5" secondAttribute="leading" id="svy-gm-cfl"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7RD-jh-ZQy">
                                        <rect key="frame" x="0.0" y="126" width="333" height="166"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="lJZ-jW-dh0">
                                                <rect key="frame" x="0.0" y="0.0" width="333" height="166"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="生日" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4UO-Se-e1u">
                                                        <rect key="frame" x="0.0" y="0.0" width="333" height="18"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="18" id="ZNu-kd-hxv"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="match_info_birthday"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a8J-uL-KwS" customClass="UIControl">
                                                        <rect key="frame" x="0.0" y="28" width="333" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请选择你的生日" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="7k4-S1-XyI">
                                                                <rect key="frame" x="12" y="14.666666666666686" width="150" height="21"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="150" id="peK-NA-VxW"/>
                                                                </constraints>
                                                                <color key="textColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="PHlocalizeKey" value="register_input_birthday"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="choose_date_image" translatesAutoresizingMaskIntoConstraints="NO" id="vfu-57-VOl">
                                                                <rect key="frame" x="297" y="13" width="24" height="24"/>
                                                            </imageView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="vfu-57-VOl" firstAttribute="centerY" secondItem="a8J-uL-KwS" secondAttribute="centerY" id="3NO-UM-puS"/>
                                                            <constraint firstItem="7k4-S1-XyI" firstAttribute="centerY" secondItem="a8J-uL-KwS" secondAttribute="centerY" id="6pb-V0-aBR"/>
                                                            <constraint firstAttribute="trailing" secondItem="vfu-57-VOl" secondAttribute="trailing" constant="12" id="K6a-zA-i6P"/>
                                                            <constraint firstAttribute="height" constant="50" id="TAz-VR-P7h"/>
                                                            <constraint firstItem="7k4-S1-XyI" firstAttribute="leading" secondItem="a8J-uL-KwS" secondAttribute="leading" constant="12" id="gyP-BK-I5Z"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                <integer key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="birthDayChoose:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="ZxY-ZW-WfY"/>
                                                        </connections>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="昵称" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zoh-am-6qX">
                                                        <rect key="frame" x="0.0" y="88" width="333" height="18"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="18" id="NOZ-Fh-JTg"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="match_info_nickname"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SA9-7l-3bl">
                                                        <rect key="frame" x="0.0" y="116" width="333" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="输入昵称" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="X5T-0T-ONe">
                                                                <rect key="frame" x="12" y="0.0" width="309" height="50"/>
                                                                <color key="textColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="PHlocalizeKey" value="register_input_nickname"/>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="nickNameTextChanged:" destination="Kq8-w8-ROF" eventType="editingChanged" id="pSn-gs-v26"/>
                                                                </connections>
                                                            </textField>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="X5T-0T-ONe" secondAttribute="bottom" id="6V0-Dc-oto"/>
                                                            <constraint firstAttribute="trailing" secondItem="X5T-0T-ONe" secondAttribute="trailing" constant="12" id="6sZ-sL-eZO"/>
                                                            <constraint firstAttribute="height" constant="50" id="Jit-e4-uGB"/>
                                                            <constraint firstItem="X5T-0T-ONe" firstAttribute="leading" secondItem="SA9-7l-3bl" secondAttribute="leading" constant="12" id="TCc-x9-Wv9"/>
                                                            <constraint firstItem="X5T-0T-ONe" firstAttribute="top" secondItem="SA9-7l-3bl" secondAttribute="top" id="nvu-F7-pMD"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                <integer key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="SA9-7l-3bl" secondAttribute="trailing" id="GZP-NM-MP0"/>
                                                    <constraint firstItem="SA9-7l-3bl" firstAttribute="leading" secondItem="lJZ-jW-dh0" secondAttribute="leading" id="dbh-tw-tzT"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="lJZ-jW-dh0" secondAttribute="bottom" id="6yf-JY-CfN"/>
                                            <constraint firstItem="lJZ-jW-dh0" firstAttribute="top" secondItem="7RD-jh-ZQy" secondAttribute="top" id="VS8-OG-FrJ"/>
                                            <constraint firstItem="lJZ-jW-dh0" firstAttribute="leading" secondItem="7RD-jh-ZQy" secondAttribute="leading" id="dMI-qZ-Lgh"/>
                                            <constraint firstAttribute="trailing" secondItem="lJZ-jW-dh0" secondAttribute="trailing" id="y3j-Br-I5f"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" verticalCompressionResistancePriority="1000" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cUt-g8-PRo">
                                <rect key="frame" x="30" y="715" width="333" height="48"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="48" id="JVw-29-YTu"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                <state key="normal" backgroundImage="user_info_nextNor">
                                    <color key="titleColor" red="0.4941176471" green="0.25882352939999997" blue="0.1137254902" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="titleLocalizeKey" value="next_step"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="nextBtnTapped:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="96v-uz-nab"/>
                                </connections>
                            </button>
                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="z1y-ex-wxf">
                                <rect key="frame" x="329" y="118" width="64" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="64" id="27Y-Is-qb5"/>
                                    <constraint firstAttribute="height" constant="44" id="A1w-ey-nbG"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="userInfo_bz_icon"/>
                                <connections>
                                    <action selector="helpButtonAction:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="fIe-B6-Idc"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="让我们开始吧" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cJs-gu-wp5">
                                <rect key="frame" x="32" y="117.99999999999999" width="168" height="39.333333333333329"/>
                                <fontDescription key="fontDescription" name="PingFangSC-Semibold" family="PingFang SC" pointSize="28"/>
                                <color key="textColor" red="0.73333333333333328" green="0.37647058823529411" blue="0.16862745098039217" alpha="1" colorSpace="deviceRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="register_title"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请填写一下信息" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="b5o-ci-eOO">
                                <rect key="frame" x="32" y="165.33333333333334" width="112" height="22.666666666666657"/>
                                <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="16"/>
                                <color key="textColor" red="0.77647058823529413" green="0.63921568627450975" blue="0.47843137254901957" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizeKey" value="register_sub_title"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="register_wow" translatesAutoresizingMaskIntoConstraints="NO" id="AKT-cX-yhY">
                                <rect key="frame" x="208" y="122.00000000000001" width="15.333333333333343" height="31.666666666666671"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fMf-hv-IxF" customClass="UIControl">
                                <rect key="frame" x="157.66666666666666" y="224" width="78" height="78"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="wenhao" translatesAutoresizingMaskIntoConstraints="NO" id="I0A-UT-l5K">
                                        <rect key="frame" x="0.0" y="0.0" width="78" height="78"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="I0A-UT-l5K" secondAttribute="height" id="5Hh-sg-CTh"/>
                                            <constraint firstAttribute="width" constant="78" id="eSx-g7-LDy"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="I0A-UT-l5K" firstAttribute="centerX" secondItem="fMf-hv-IxF" secondAttribute="centerX" id="8mm-mt-Ody"/>
                                    <constraint firstItem="I0A-UT-l5K" firstAttribute="centerY" secondItem="fMf-hv-IxF" secondAttribute="centerY" id="OFA-bZ-62F"/>
                                    <constraint firstAttribute="trailing" secondItem="I0A-UT-l5K" secondAttribute="trailing" id="Pet-ee-ist"/>
                                    <constraint firstAttribute="bottom" secondItem="I0A-UT-l5K" secondAttribute="bottom" id="Vo0-Gm-jaF"/>
                                    <constraint firstItem="I0A-UT-l5K" firstAttribute="top" secondItem="fMf-hv-IxF" secondAttribute="top" id="fom-4X-9Eb"/>
                                    <constraint firstItem="I0A-UT-l5K" firstAttribute="leading" secondItem="fMf-hv-IxF" secondAttribute="leading" id="yff-0E-SAh"/>
                                </constraints>
                                <connections>
                                    <action selector="avatarImgTapped:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="ihS-bW-72V"/>
                                </connections>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qhB-KM-pmc">
                                <rect key="frame" x="216" y="274" width="28" height="28"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="28" id="1kW-hk-zhp"/>
                                    <constraint firstAttribute="height" constant="28" id="EMN-pr-VSK"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="random_header_image"/>
                                <connections>
                                    <action selector="randomHeaderImgTapped:" destination="Kq8-w8-ROF" eventType="touchUpInside" id="Q8I-1O-qaB"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="U3U-lo-tTG"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="8Be-be-gd3" firstAttribute="leading" secondItem="U3U-lo-tTG" secondAttribute="leading" constant="30" id="1N4-Pp-Fud"/>
                            <constraint firstItem="U3U-lo-tTG" firstAttribute="trailing" secondItem="cUt-g8-PRo" secondAttribute="trailing" constant="30" id="7df-lM-y9p"/>
                            <constraint firstItem="8Be-be-gd3" firstAttribute="top" secondItem="fMf-hv-IxF" secondAttribute="bottom" constant="5" id="8Jj-Hl-Nz7"/>
                            <constraint firstItem="aq6-eh-4GI" firstAttribute="top" secondItem="Jew-hD-zAc" secondAttribute="top" id="8hd-KL-gJC"/>
                            <constraint firstItem="qhB-KM-pmc" firstAttribute="bottom" secondItem="fMf-hv-IxF" secondAttribute="bottom" id="CNw-GS-2LR"/>
                            <constraint firstItem="aq6-eh-4GI" firstAttribute="leading" secondItem="U3U-lo-tTG" secondAttribute="leading" id="Eph-yu-EsC"/>
                            <constraint firstAttribute="bottom" secondItem="aq6-eh-4GI" secondAttribute="bottom" id="Gnr-gA-iKQ"/>
                            <constraint firstItem="z1y-ex-wxf" firstAttribute="top" secondItem="U3U-lo-tTG" secondAttribute="top" id="Ndm-7r-vHS"/>
                            <constraint firstItem="AKT-cX-yhY" firstAttribute="leading" secondItem="cJs-gu-wp5" secondAttribute="trailing" constant="8" id="Qbs-r1-gJ8"/>
                            <constraint firstItem="b5o-ci-eOO" firstAttribute="top" secondItem="cJs-gu-wp5" secondAttribute="bottom" constant="8" id="TVW-I0-37f"/>
                            <constraint firstItem="AKT-cX-yhY" firstAttribute="centerY" secondItem="cJs-gu-wp5" secondAttribute="centerY" id="V8F-xN-R36"/>
                            <constraint firstItem="b5o-ci-eOO" firstAttribute="leading" secondItem="cJs-gu-wp5" secondAttribute="leading" id="efi-sn-7CY"/>
                            <constraint firstItem="fMf-hv-IxF" firstAttribute="top" secondItem="b5o-ci-eOO" secondAttribute="bottom" constant="36" id="esn-P7-E4k"/>
                            <constraint firstItem="8Be-be-gd3" firstAttribute="centerX" secondItem="fMf-hv-IxF" secondAttribute="centerX" id="fzs-0Q-fz8"/>
                            <constraint firstItem="U3U-lo-tTG" firstAttribute="trailing" secondItem="z1y-ex-wxf" secondAttribute="trailing" id="hHs-6E-gbW"/>
                            <constraint firstItem="cUt-g8-PRo" firstAttribute="leading" secondItem="U3U-lo-tTG" secondAttribute="leading" constant="30" id="ibo-IL-t9N"/>
                            <constraint firstItem="U3U-lo-tTG" firstAttribute="trailing" secondItem="8Be-be-gd3" secondAttribute="trailing" constant="30" id="lIR-Uy-gVj"/>
                            <constraint firstItem="cUt-g8-PRo" firstAttribute="top" secondItem="8Be-be-gd3" secondAttribute="bottom" constant="116" id="pAC-Sd-zQC"/>
                            <constraint firstItem="cJs-gu-wp5" firstAttribute="leading" secondItem="U3U-lo-tTG" secondAttribute="leading" constant="32" id="sJ1-8M-bbq"/>
                            <constraint firstItem="U3U-lo-tTG" firstAttribute="top" secondItem="cJs-gu-wp5" secondAttribute="top" id="wg8-pK-Ore"/>
                            <constraint firstItem="U3U-lo-tTG" firstAttribute="trailing" secondItem="aq6-eh-4GI" secondAttribute="trailing" id="xoX-cn-Elv"/>
                            <constraint firstItem="qhB-KM-pmc" firstAttribute="trailing" secondItem="fMf-hv-IxF" secondAttribute="trailing" constant="8.5" id="yGN-bg-iPg"/>
                            <constraint firstItem="fMf-hv-IxF" firstAttribute="centerX" secondItem="Jew-hD-zAc" secondAttribute="centerX" id="zRw-WB-zr1"/>
                            <constraint firstItem="cUt-g8-PRo" firstAttribute="centerX" secondItem="Jew-hD-zAc" secondAttribute="centerX" id="zw2-6I-dEk"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="avatarImgView" destination="I0A-UT-l5K" id="piu-uv-3N6"/>
                        <outlet property="birthDayTextField" destination="7k4-S1-XyI" id="agQ-6B-gBb"/>
                        <outlet property="chooseBoyBtn" destination="3tF-0h-YVa" id="d8t-i9-rgT"/>
                        <outlet property="chooseGirlBtn" destination="sU1-BY-R41" id="utR-pb-buQ"/>
                        <outlet property="nextBtn" destination="cUt-g8-PRo" id="mGy-bP-Sxg"/>
                        <outlet property="nextBtnY" destination="pAC-Sd-zQC" id="xZK-d7-FsE"/>
                        <outlet property="nickNameBgView" destination="SA9-7l-3bl" id="9Z3-9E-o1A"/>
                        <outlet property="nickNameTextField" destination="X5T-0T-ONe" id="Bxm-qL-Lc7"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="MgQ-Qd-5xC" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="51.145038167938928" y="-561.26760563380287"/>
        </scene>
        <!--Help View Controller-->
        <scene sceneID="7Rd-M3-rKB">
            <objects>
                <viewController storyboardIdentifier="YLHelpViewController" modalPresentationStyle="fullScreen" id="hr9-89-ACZ" customClass="YLHelpViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2Fu-pG-P8m">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="loveInfoBg" translatesAutoresizingMaskIntoConstraints="NO" id="3xE-yG-cKp">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iwH-66-gR3">
                                <rect key="frame" x="0.0" y="118" width="393" height="734"/>
                                <color key="backgroundColor" red="1" green="0.9882352941176471" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="oK8-ul-SjG">
                                <rect key="frame" x="0.0" y="134" width="393" height="174"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZYe-U9-cKt" customClass="UIControl">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="58"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="back_icon" translatesAutoresizingMaskIntoConstraints="NO" id="pDx-Tg-GCk">
                                                <rect key="frame" x="370" y="22" width="8" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="8" id="G2Q-06-U2T"/>
                                                    <constraint firstAttribute="height" constant="14" id="SPj-4t-XMg"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_yjfk" translatesAutoresizingMaskIntoConstraints="NO" id="cjV-IA-fhB">
                                                <rect key="frame" x="15" y="15" width="28" height="28"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="28" id="SFE-nP-3Al"/>
                                                    <constraint firstAttribute="width" constant="28" id="zhs-6P-qkA"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="意见反馈" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5ON-63-uhR">
                                                <rect key="frame" x="54" y="20" width="59.666666666666657" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <color key="highlightedColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="5ON-63-uhR" firstAttribute="centerY" secondItem="ZYe-U9-cKt" secondAttribute="centerY" id="0z8-VD-c9a"/>
                                            <constraint firstAttribute="height" constant="58" id="5AF-wA-TJ2"/>
                                            <constraint firstItem="pDx-Tg-GCk" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="5ON-63-uhR" secondAttribute="trailing" constant="8" symbolic="YES" id="DJm-vE-YrX"/>
                                            <constraint firstItem="cjV-IA-fhB" firstAttribute="centerY" secondItem="ZYe-U9-cKt" secondAttribute="centerY" id="U7R-un-GE0"/>
                                            <constraint firstItem="cjV-IA-fhB" firstAttribute="leading" secondItem="ZYe-U9-cKt" secondAttribute="leading" constant="15" id="aRh-0f-rra"/>
                                            <constraint firstAttribute="trailing" secondItem="pDx-Tg-GCk" secondAttribute="trailing" constant="15" id="i42-WZ-jcm"/>
                                            <constraint firstItem="5ON-63-uhR" firstAttribute="leading" secondItem="cjV-IA-fhB" secondAttribute="trailing" constant="11" id="kRw-HI-orj"/>
                                            <constraint firstItem="5ON-63-uhR" firstAttribute="centerY" secondItem="ZYe-U9-cKt" secondAttribute="centerY" id="mUk-Gl-847"/>
                                            <constraint firstItem="pDx-Tg-GCk" firstAttribute="centerY" secondItem="ZYe-U9-cKt" secondAttribute="centerY" id="zMf-1m-DgB"/>
                                        </constraints>
                                        <connections>
                                            <action selector="feedbackButtonAction:" destination="hr9-89-ACZ" eventType="touchUpInside" id="kNS-mf-ZJN"/>
                                        </connections>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rxD-re-FS0">
                                        <rect key="frame" x="0.0" y="58" width="393" height="58"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IPd-gE-jRd">
                                                <rect key="frame" x="0.0" y="0.0" width="393" height="58"/>
                                                <connections>
                                                    <segue destination="KIP-6s-Va7" kind="show" id="5nm-Wl-ErO"/>
                                                </connections>
                                            </button>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="back_icon" translatesAutoresizingMaskIntoConstraints="NO" id="fny-pX-Nel">
                                                <rect key="frame" x="370" y="22" width="8" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="14" id="gUD-MY-Lfr"/>
                                                    <constraint firstAttribute="width" constant="8" id="tXt-bj-CnU"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_lxwm" translatesAutoresizingMaskIntoConstraints="NO" id="JeR-b1-XK1">
                                                <rect key="frame" x="15" y="15" width="28" height="28"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="28" id="BTI-wX-4uD"/>
                                                    <constraint firstAttribute="height" constant="28" id="j1E-9y-a1k"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="联系我们" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s3Y-RU-ka8">
                                                <rect key="frame" x="54" y="20" width="59.666666666666657" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <color key="highlightedColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="s3Y-RU-ka8" firstAttribute="leading" secondItem="JeR-b1-XK1" secondAttribute="trailing" constant="11" id="37e-lV-cdK"/>
                                            <constraint firstItem="JeR-b1-XK1" firstAttribute="leading" secondItem="rxD-re-FS0" secondAttribute="leading" constant="15" id="Jkd-rq-KyZ"/>
                                            <constraint firstAttribute="bottom" secondItem="IPd-gE-jRd" secondAttribute="bottom" id="Ud4-n1-2S3"/>
                                            <constraint firstAttribute="trailing" secondItem="fny-pX-Nel" secondAttribute="trailing" constant="15" id="c9J-n3-xGp"/>
                                            <constraint firstItem="IPd-gE-jRd" firstAttribute="leading" secondItem="rxD-re-FS0" secondAttribute="leading" id="iJW-Z0-kKg"/>
                                            <constraint firstItem="IPd-gE-jRd" firstAttribute="top" secondItem="rxD-re-FS0" secondAttribute="top" id="igg-Tr-9zC"/>
                                            <constraint firstAttribute="trailing" secondItem="IPd-gE-jRd" secondAttribute="trailing" id="ph5-qa-c1Y"/>
                                            <constraint firstItem="s3Y-RU-ka8" firstAttribute="centerY" secondItem="rxD-re-FS0" secondAttribute="centerY" id="qI0-Dg-OV7"/>
                                            <constraint firstAttribute="height" constant="58" id="rXz-bD-m3N"/>
                                            <constraint firstItem="s3Y-RU-ka8" firstAttribute="centerY" secondItem="rxD-re-FS0" secondAttribute="centerY" id="szb-c8-yHY"/>
                                            <constraint firstItem="fny-pX-Nel" firstAttribute="centerY" secondItem="rxD-re-FS0" secondAttribute="centerY" id="tHS-gD-vZU"/>
                                            <constraint firstItem="fny-pX-Nel" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="s3Y-RU-ka8" secondAttribute="trailing" constant="8" symbolic="YES" id="v4c-6Q-eF0"/>
                                            <constraint firstItem="JeR-b1-XK1" firstAttribute="centerY" secondItem="rxD-re-FS0" secondAttribute="centerY" id="wcU-hZ-7OP"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rch-Nc-Yju" customClass="UIControl">
                                        <rect key="frame" x="0.0" y="116" width="393" height="58"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="back_icon" translatesAutoresizingMaskIntoConstraints="NO" id="paD-sb-XUP">
                                                <rect key="frame" x="370" y="22" width="8" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="8" id="7Bf-e2-kDb"/>
                                                    <constraint firstAttribute="height" constant="14" id="rkV-sx-7cj"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_cjwt" translatesAutoresizingMaskIntoConstraints="NO" id="sob-J8-hMs">
                                                <rect key="frame" x="15" y="15" width="28" height="28"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="28" id="0tN-Th-PeP"/>
                                                    <constraint firstAttribute="height" constant="28" id="bzo-Y0-K2v"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="iOS常见问题" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LCb-cY-pZY">
                                                <rect key="frame" x="54.000000000000007" y="20" width="85.666666666666686" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <color key="highlightedColor" red="0.18039215689999999" green="0.1764705882" blue="0.20784313730000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="58" id="0gh-2K-l17"/>
                                            <constraint firstItem="LCb-cY-pZY" firstAttribute="centerY" secondItem="Rch-Nc-Yju" secondAttribute="centerY" id="4e2-Y3-2ME"/>
                                            <constraint firstItem="sob-J8-hMs" firstAttribute="leading" secondItem="Rch-Nc-Yju" secondAttribute="leading" constant="15" id="Wbp-f6-RPM"/>
                                            <constraint firstItem="paD-sb-XUP" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="LCb-cY-pZY" secondAttribute="trailing" constant="8" symbolic="YES" id="ZY2-3H-gHQ"/>
                                            <constraint firstItem="LCb-cY-pZY" firstAttribute="centerY" secondItem="Rch-Nc-Yju" secondAttribute="centerY" id="Zd5-sd-IfS"/>
                                            <constraint firstItem="sob-J8-hMs" firstAttribute="centerY" secondItem="Rch-Nc-Yju" secondAttribute="centerY" id="Zt8-1I-IzF"/>
                                            <constraint firstItem="paD-sb-XUP" firstAttribute="centerY" secondItem="Rch-Nc-Yju" secondAttribute="centerY" id="alt-Vm-LPv"/>
                                            <constraint firstAttribute="trailing" secondItem="paD-sb-XUP" secondAttribute="trailing" constant="15" id="fSV-n8-M3E"/>
                                            <constraint firstItem="LCb-cY-pZY" firstAttribute="leading" secondItem="sob-J8-hMs" secondAttribute="trailing" constant="11" id="s6r-LT-NuZ"/>
                                        </constraints>
                                        <connections>
                                            <action selector="iOSQuestionTapped:" destination="hr9-89-ACZ" eventType="touchUpInside" id="7L1-dv-T9Z"/>
                                        </connections>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="xvB-Nr-oMi"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="3xE-yG-cKp" firstAttribute="top" secondItem="2Fu-pG-P8m" secondAttribute="top" id="0Q3-Yu-7vJ"/>
                            <constraint firstItem="iwH-66-gR3" firstAttribute="top" secondItem="xvB-Nr-oMi" secondAttribute="top" id="20M-d3-q1j"/>
                            <constraint firstItem="3xE-yG-cKp" firstAttribute="leading" secondItem="2Fu-pG-P8m" secondAttribute="leading" id="59b-D6-8cV"/>
                            <constraint firstItem="iwH-66-gR3" firstAttribute="leading" secondItem="xvB-Nr-oMi" secondAttribute="leading" id="8SA-W7-cQ8"/>
                            <constraint firstAttribute="bottom" secondItem="3xE-yG-cKp" secondAttribute="bottom" id="BCM-2C-6yS"/>
                            <constraint firstItem="xvB-Nr-oMi" firstAttribute="trailing" secondItem="iwH-66-gR3" secondAttribute="trailing" id="Dxw-3B-jQb"/>
                            <constraint firstAttribute="trailing" secondItem="3xE-yG-cKp" secondAttribute="trailing" id="M8R-51-tYX"/>
                            <constraint firstItem="xvB-Nr-oMi" firstAttribute="trailing" secondItem="oK8-ul-SjG" secondAttribute="trailing" id="NzL-vj-WxV"/>
                            <constraint firstItem="oK8-ul-SjG" firstAttribute="leading" secondItem="xvB-Nr-oMi" secondAttribute="leading" id="TDB-gx-ucH"/>
                            <constraint firstAttribute="bottom" secondItem="iwH-66-gR3" secondAttribute="bottom" id="TUp-Qk-0pG"/>
                            <constraint firstItem="oK8-ul-SjG" firstAttribute="top" secondItem="xvB-Nr-oMi" secondAttribute="top" constant="16" id="tSo-ga-0h0"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="AYP-xj-zl8"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="9Et-Aa-b0R" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="822" y="175"/>
        </scene>
        <!--Connect Us View Controller-->
        <scene sceneID="qIa-kz-RZy">
            <objects>
                <viewController storyboardIdentifier="YLConnectUsViewController" modalPresentationStyle="fullScreen" useStoryboardIdentifierAsRestorationIdentifier="YES" id="KIP-6s-Va7" customClass="YLConnectUsViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="FmB-kq-Gpm">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="loveInfoBg" translatesAutoresizingMaskIntoConstraints="NO" id="53T-En-XMZ">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pw8-Xe-mNm">
                                <rect key="frame" x="10" y="10" width="373" height="798"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="lxwm_bg_img" translatesAutoresizingMaskIntoConstraints="NO" id="6Ll-mu-HIt">
                                        <rect key="frame" x="0.0" y="0.0" width="373" height="771.66666666666663"/>
                                    </imageView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="nlK-89-6Uh">
                                        <rect key="frame" x="32" y="80" width="309" height="651.66666666666663"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kx1-Ta-UbJ">
                                                <rect key="frame" x="0.0" y="0.0" width="309" height="213"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="lxwm_step1_img-1" translatesAutoresizingMaskIntoConstraints="NO" id="WUe-Ep-NBN">
                                                        <rect key="frame" x="0.0" y="9.9999999999999982" width="263" height="20.333333333333329"/>
                                                    </imageView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="T0r-JQ-Xw2">
                                                        <rect key="frame" x="0.0" y="40.333333333333343" width="309" height="172.66666666666666"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="lxwm_gzh_img" translatesAutoresizingMaskIntoConstraints="NO" id="Oah-Hb-s9C">
                                                                <rect key="frame" x="104" y="12" width="101.33333333333331" height="101"/>
                                                            </imageView>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jZz-Yn-gW9">
                                                                <rect key="frame" x="112" y="124.99999999999999" width="85.333333333333314" height="28.333333333333329"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" image="lxwm_bcbdk_img"/>
                                                                <connections>
                                                                    <action selector="saveAction:" destination="KIP-6s-Va7" eventType="touchUpInside" id="UU4-ne-JT7"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="Oah-Hb-s9C" firstAttribute="centerX" secondItem="T0r-JQ-Xw2" secondAttribute="centerX" id="Ihf-F1-3bu"/>
                                                            <constraint firstItem="Oah-Hb-s9C" firstAttribute="top" secondItem="T0r-JQ-Xw2" secondAttribute="top" constant="12" id="SVP-JI-hU8"/>
                                                            <constraint firstItem="jZz-Yn-gW9" firstAttribute="centerX" secondItem="T0r-JQ-Xw2" secondAttribute="centerX" id="hcK-T7-ve8"/>
                                                            <constraint firstItem="jZz-Yn-gW9" firstAttribute="top" secondItem="Oah-Hb-s9C" secondAttribute="bottom" constant="12" id="rLT-SB-zKn"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                <integer key="value" value="6"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="213" id="E0A-Ve-Vqo"/>
                                                    <constraint firstItem="WUe-Ep-NBN" firstAttribute="top" secondItem="kx1-Ta-UbJ" secondAttribute="top" constant="10" id="Gfl-Yz-do1"/>
                                                    <constraint firstItem="WUe-Ep-NBN" firstAttribute="leading" secondItem="kx1-Ta-UbJ" secondAttribute="leading" id="K0S-wN-FKU"/>
                                                    <constraint firstAttribute="bottom" secondItem="T0r-JQ-Xw2" secondAttribute="bottom" id="NlV-4R-Lgw"/>
                                                    <constraint firstItem="T0r-JQ-Xw2" firstAttribute="leading" secondItem="kx1-Ta-UbJ" secondAttribute="leading" id="WMu-vL-21c"/>
                                                    <constraint firstItem="T0r-JQ-Xw2" firstAttribute="top" secondItem="WUe-Ep-NBN" secondAttribute="bottom" constant="10" id="YeL-D2-Qlo"/>
                                                    <constraint firstAttribute="trailing" secondItem="T0r-JQ-Xw2" secondAttribute="trailing" id="bsA-XN-t7H"/>
                                                </constraints>
                                            </view>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="lxwm_step234_img" translatesAutoresizingMaskIntoConstraints="NO" id="Pl2-bp-HnQ">
                                                <rect key="frame" x="0.0" y="223.00000000000003" width="309" height="428.66666666666674"/>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="nlK-89-6Uh" firstAttribute="top" secondItem="Pw8-Xe-mNm" secondAttribute="top" constant="80" id="KTI-rb-PVr"/>
                                    <constraint firstItem="6Ll-mu-HIt" firstAttribute="top" secondItem="Pw8-Xe-mNm" secondAttribute="top" id="QOJ-cz-nBy"/>
                                    <constraint firstItem="nlK-89-6Uh" firstAttribute="leading" secondItem="Pw8-Xe-mNm" secondAttribute="leading" constant="32" id="THK-Mj-T18"/>
                                    <constraint firstItem="nlK-89-6Uh" firstAttribute="width" secondItem="Pw8-Xe-mNm" secondAttribute="width" constant="-64" id="VHR-Xs-3di"/>
                                    <constraint firstAttribute="trailing" secondItem="6Ll-mu-HIt" secondAttribute="trailing" id="mBG-GU-rtl"/>
                                    <constraint firstAttribute="trailing" secondItem="nlK-89-6Uh" secondAttribute="trailing" constant="32" id="oqA-wh-duC"/>
                                    <constraint firstItem="6Ll-mu-HIt" firstAttribute="leading" secondItem="Pw8-Xe-mNm" secondAttribute="leading" id="py2-dT-gNL"/>
                                    <constraint firstAttribute="bottom" secondItem="6Ll-mu-HIt" secondAttribute="bottom" id="r90-M5-94z"/>
                                    <constraint firstAttribute="bottom" secondItem="nlK-89-6Uh" secondAttribute="bottom" constant="40" id="xQk-k7-RaZ"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="QhI-UY-gwf"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="53T-En-XMZ" firstAttribute="top" secondItem="FmB-kq-Gpm" secondAttribute="top" id="1SA-Bz-gNZ"/>
                            <constraint firstItem="Pw8-Xe-mNm" firstAttribute="top" secondItem="FmB-kq-Gpm" secondAttribute="top" constant="10" id="2o6-SJ-0po"/>
                            <constraint firstAttribute="bottom" secondItem="53T-En-XMZ" secondAttribute="bottom" id="OLI-mI-ThE"/>
                            <constraint firstAttribute="trailing" secondItem="53T-En-XMZ" secondAttribute="trailing" id="W87-D8-h6G"/>
                            <constraint firstItem="Pw8-Xe-mNm" firstAttribute="leading" secondItem="QhI-UY-gwf" secondAttribute="leading" constant="10" id="ogx-Sj-Clw"/>
                            <constraint firstItem="QhI-UY-gwf" firstAttribute="bottom" secondItem="Pw8-Xe-mNm" secondAttribute="bottom" constant="10" id="t6P-B6-6Jw"/>
                            <constraint firstItem="QhI-UY-gwf" firstAttribute="trailing" secondItem="Pw8-Xe-mNm" secondAttribute="trailing" constant="10" id="tya-R5-3LC"/>
                            <constraint firstItem="53T-En-XMZ" firstAttribute="leading" secondItem="FmB-kq-Gpm" secondAttribute="leading" id="vTz-g1-Vte"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="0gJ-Nt-lE9"/>
                    <connections>
                        <outlet property="backImageView" destination="6Ll-mu-HIt" id="R6s-hv-kU5"/>
                        <outlet property="backTop" destination="2o6-SJ-0po" id="fej-QY-wcc"/>
                        <outlet property="qrImgView" destination="Oah-Hb-s9C" id="bGm-Me-6jQ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Sxw-6m-Gqg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1660.8695652173915" y="174.77678571428569"/>
        </scene>
        <!--Phone Input View Controller-->
        <scene sceneID="5Zw-lg-0be">
            <objects>
                <viewController storyboardIdentifier="YLPhoneInputViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="eAj-lL-8G6" customClass="YLPhoneInputViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="wP5-mA-8YQ">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dl_bg_img" translatesAutoresizingMaskIntoConstraints="NO" id="w4O-bc-WOq">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ufm-nW-lcZ">
                                <rect key="frame" x="18" y="333" width="358" height="54"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plus" translatesAutoresizingMaskIntoConstraints="NO" id="ivE-DI-zM7">
                                        <rect key="frame" x="19" y="22" width="10" height="10"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="86" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YKI-jo-CGY">
                                        <rect key="frame" x="30" y="16.666666666666686" width="21" height="21"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="triangle_down" translatesAutoresizingMaskIntoConstraints="NO" id="idG-70-xtO">
                                        <rect key="frame" x="54" y="23" width="10" height="8"/>
                                    </imageView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="type_line" translatesAutoresizingMaskIntoConstraints="NO" id="t7A-4y-Gwr">
                                        <rect key="frame" x="79" y="20" width="3" height="14"/>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UZT-oN-ccO">
                                        <rect key="frame" x="3" y="8" width="73" height="35"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <connections>
                                            <action selector="onSelectCountryCodeButtonPressed:" destination="eAj-lL-8G6" eventType="touchUpInside" id="AyE-kS-mMa"/>
                                        </connections>
                                    </button>
                                    <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="200" horizontalCompressionResistancePriority="500" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请输入手机号" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="hhp-HG-GXv">
                                        <rect key="frame" x="98" y="16" width="244" height="22"/>
                                        <color key="tintColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <textInputTraits key="textInputTraits" keyboardType="phonePad" returnKeyType="done"/>
                                    </textField>
                                </subviews>
                                <color key="backgroundColor" red="0.97647058823529409" green="0.97647058823529409" blue="0.97647058823529409" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="ivE-DI-zM7" firstAttribute="centerY" secondItem="ufm-nW-lcZ" secondAttribute="centerY" id="01n-m8-XRK"/>
                                    <constraint firstAttribute="trailing" secondItem="hhp-HG-GXv" secondAttribute="trailing" constant="16" id="1pQ-u6-6Ka"/>
                                    <constraint firstItem="hhp-HG-GXv" firstAttribute="leading" secondItem="t7A-4y-Gwr" secondAttribute="trailing" constant="16" id="40B-5b-soG"/>
                                    <constraint firstItem="UZT-oN-ccO" firstAttribute="top" secondItem="ufm-nW-lcZ" secondAttribute="top" constant="8" id="6pq-Hi-nHt"/>
                                    <constraint firstItem="t7A-4y-Gwr" firstAttribute="leading" secondItem="UZT-oN-ccO" secondAttribute="trailing" constant="3" id="DrS-CF-QFN"/>
                                    <constraint firstItem="idG-70-xtO" firstAttribute="centerY" secondItem="ufm-nW-lcZ" secondAttribute="centerY" id="P0T-fa-oG8"/>
                                    <constraint firstItem="hhp-HG-GXv" firstAttribute="centerY" secondItem="ufm-nW-lcZ" secondAttribute="centerY" id="PSk-oY-wgy"/>
                                    <constraint firstItem="ivE-DI-zM7" firstAttribute="leading" secondItem="ufm-nW-lcZ" secondAttribute="leading" constant="19" id="TvL-co-Fvo"/>
                                    <constraint firstItem="t7A-4y-Gwr" firstAttribute="leading" secondItem="idG-70-xtO" secondAttribute="trailing" constant="15" id="WRd-gU-nid"/>
                                    <constraint firstAttribute="height" constant="54" id="chD-YF-uvO"/>
                                    <constraint firstAttribute="bottom" secondItem="UZT-oN-ccO" secondAttribute="bottom" constant="11" id="ewU-05-jiO"/>
                                    <constraint firstItem="UZT-oN-ccO" firstAttribute="leading" secondItem="ufm-nW-lcZ" secondAttribute="leading" constant="3" id="jXU-yL-nla"/>
                                    <constraint firstItem="t7A-4y-Gwr" firstAttribute="centerY" secondItem="ufm-nW-lcZ" secondAttribute="centerY" id="nae-lT-1aB"/>
                                    <constraint firstItem="YKI-jo-CGY" firstAttribute="centerY" secondItem="ufm-nW-lcZ" secondAttribute="centerY" id="phF-ZX-50B"/>
                                    <constraint firstItem="YKI-jo-CGY" firstAttribute="leading" secondItem="ivE-DI-zM7" secondAttribute="trailing" constant="1" id="puk-d2-7WK"/>
                                    <constraint firstItem="idG-70-xtO" firstAttribute="leading" secondItem="YKI-jo-CGY" secondAttribute="trailing" constant="3" id="zKv-OE-Ztc"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="27"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_img" translatesAutoresizingMaskIntoConstraints="NO" id="PdJ-j7-Lio">
                                <rect key="frame" x="152.66666666666666" y="207" width="88" height="88"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="88" id="0Lt-Dh-t6G"/>
                                    <constraint firstAttribute="width" constant="88" id="CDk-e0-3lH"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0p2-Os-Hoq">
                                <rect key="frame" x="196.66666666666666" y="398" width="0.0" height="0.0"/>
                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                <color key="textColor" red="0.42745098040000001" green="0.42745098040000001" blue="0.42745098040000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xhd-IY-yLV">
                                <rect key="frame" x="50.666666666666657" y="433" width="292" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="292" id="Vg1-in-mQu"/>
                                    <constraint firstAttribute="height" constant="50" id="zWu-ul-sos"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="获取验证码" backgroundImage="yjdl_bg_btn">
                                    <color key="titleColor" red="0.49411764705882355" green="0.25882352941176467" blue="0.11372549019607843" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="onSendSmsCodeButtonPressed:" destination="eAj-lL-8G6" eventType="touchUpInside" id="3iZ-18-w2g"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bcr-Wc-krM"/>
                        <color key="backgroundColor" red="0.96470588235294119" green="0.95686274509803915" blue="0.90588235294117647" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="PdJ-j7-Lio" firstAttribute="centerX" secondItem="wP5-mA-8YQ" secondAttribute="centerX" id="EaG-zL-uIX"/>
                            <constraint firstItem="bcr-Wc-krM" firstAttribute="trailing" secondItem="w4O-bc-WOq" secondAttribute="trailing" id="Fmz-VB-757"/>
                            <constraint firstItem="ufm-nW-lcZ" firstAttribute="top" secondItem="PdJ-j7-Lio" secondAttribute="bottom" constant="38" id="L4n-O6-ed8"/>
                            <constraint firstItem="0p2-Os-Hoq" firstAttribute="centerX" secondItem="wP5-mA-8YQ" secondAttribute="centerX" id="XNa-It-Eki"/>
                            <constraint firstItem="w4O-bc-WOq" firstAttribute="leading" secondItem="wP5-mA-8YQ" secondAttribute="leading" id="XyF-4w-ET5"/>
                            <constraint firstAttribute="bottom" secondItem="w4O-bc-WOq" secondAttribute="bottom" id="ayD-J3-fdB"/>
                            <constraint firstItem="w4O-bc-WOq" firstAttribute="top" secondItem="wP5-mA-8YQ" secondAttribute="top" id="gCD-OT-Mza"/>
                            <constraint firstItem="bcr-Wc-krM" firstAttribute="trailing" secondItem="ufm-nW-lcZ" secondAttribute="trailing" constant="17" id="i7p-Vy-Zjt"/>
                            <constraint firstItem="PdJ-j7-Lio" firstAttribute="top" secondItem="bcr-Wc-krM" secondAttribute="top" constant="45" id="jWW-qa-s9m"/>
                            <constraint firstItem="ufm-nW-lcZ" firstAttribute="leading" secondItem="bcr-Wc-krM" secondAttribute="leading" constant="18" id="kCx-4Q-qnK"/>
                            <constraint firstItem="0p2-Os-Hoq" firstAttribute="top" secondItem="PdJ-j7-Lio" secondAttribute="bottom" constant="103" id="mgW-V9-H8P"/>
                            <constraint firstItem="Xhd-IY-yLV" firstAttribute="centerX" secondItem="wP5-mA-8YQ" secondAttribute="centerX" id="oZd-jK-s6g"/>
                            <constraint firstItem="Xhd-IY-yLV" firstAttribute="top" secondItem="ufm-nW-lcZ" secondAttribute="bottom" constant="46" id="pwI-HD-pOm"/>
                        </constraints>
                    </view>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="addFlgView" destination="ivE-DI-zM7" id="TTl-s9-Aap"/>
                        <outlet property="bgImgeView" destination="w4O-bc-WOq" id="Fkn-Ds-Vei"/>
                        <outlet property="countryCodeLabel" destination="YKI-jo-CGY" id="9UA-Ot-6Ux"/>
                        <outlet property="downFlgView" destination="idG-70-xtO" id="ZjL-mQ-AEk"/>
                        <outlet property="lineView" destination="t7A-4y-Gwr" id="fmh-Eh-rde"/>
                        <outlet property="phoneNumberField" destination="hhp-HG-GXv" id="cj6-Mf-afR"/>
                        <outlet property="phoneNumberView" destination="ufm-nW-lcZ" id="ceR-DE-uOL"/>
                        <outlet property="smsBtn" destination="Xhd-IY-yLV" id="ynL-c1-sHd"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="SQV-OF-kOe" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="54" y="854"/>
        </scene>
        <!--Not Find Account Controller-->
        <scene sceneID="9tS-sP-BlJ">
            <objects>
                <viewController storyboardIdentifier="YLNotFindAccountController" id="h2x-Jb-gw4" customClass="YLNotFindAccountController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="zwK-Nk-GeE">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="loveInfoBg" translatesAutoresizingMaskIntoConstraints="NO" id="1DO-LG-ATF">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="u1E-zT-iTg">
                                <rect key="frame" x="126.66666666666669" y="622" width="140" height="50"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="xzxckj_button_acc"/>
                                <connections>
                                    <action selector="onGoOnAction:" destination="h2x-Jb-gw4" eventType="touchUpInside" id="hrP-iE-UHQ"/>
                                </connections>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="zhzh_wcxdzh_img" translatesAutoresizingMaskIntoConstraints="NO" id="Kf9-Hu-hcw">
                                <rect key="frame" x="94.666666666666686" y="231.66666666666663" width="204" height="229"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="rLz-vC-z7l"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="1DO-LG-ATF" secondAttribute="bottom" id="6mD-KE-jY8"/>
                            <constraint firstItem="u1E-zT-iTg" firstAttribute="centerX" secondItem="zwK-Nk-GeE" secondAttribute="centerX" id="9NZ-yl-UWp"/>
                            <constraint firstItem="rLz-vC-z7l" firstAttribute="trailing" secondItem="1DO-LG-ATF" secondAttribute="trailing" id="DQ8-fb-BrY"/>
                            <constraint firstItem="Kf9-Hu-hcw" firstAttribute="centerY" secondItem="zwK-Nk-GeE" secondAttribute="centerY" constant="-80" id="aa5-ce-6pO"/>
                            <constraint firstItem="1DO-LG-ATF" firstAttribute="leading" secondItem="zwK-Nk-GeE" secondAttribute="leading" id="pCw-Xq-qZ2"/>
                            <constraint firstItem="Kf9-Hu-hcw" firstAttribute="centerX" secondItem="zwK-Nk-GeE" secondAttribute="centerX" id="sYX-Ye-f7q"/>
                            <constraint firstAttribute="bottom" secondItem="u1E-zT-iTg" secondAttribute="bottom" constant="180" id="z1W-gk-jzh"/>
                            <constraint firstItem="1DO-LG-ATF" firstAttribute="top" secondItem="zwK-Nk-GeE" secondAttribute="top" id="zFg-09-6vL"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="noticeImageView" destination="Kf9-Hu-hcw" id="EAh-XO-u9z"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hU2-lg-7GO" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="54" y="1592"/>
        </scene>
        <!--Login View Controller-->
        <scene sceneID="SwB-FH-4Pt">
            <objects>
                <viewController storyboardIdentifier="YLLoginViewController" id="kwA-e6-Ck9" customClass="YLLoginViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="358-cS-zLZ">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="kp_bg_img" translatesAutoresizingMaskIntoConstraints="NO" id="HZi-v0-Dai">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="812"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yi2-pW-MMe">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="606.33333333333337"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dl_w_bg_img" translatesAutoresizingMaskIntoConstraints="NO" id="ayf-Mx-QWK">
                                <rect key="frame" x="0.0" y="453.33333333333326" width="393" height="398.66666666666674"/>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="fao-uF-1vV">
                                <rect key="frame" x="50.666666666666657" y="581.33333333333337" width="292" height="182"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qQt-nE-wmC">
                                        <rect key="frame" x="0.0" y="0.0" width="292" height="50"/>
                                        <color key="backgroundColor" red="1" green="0.98431372549019602" blue="0.92549019607843142" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="gRC-Ts-pog"/>
                                            <constraint firstAttribute="width" constant="292" id="kk2-CW-8g9"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Sign up with Google">
                                            <backgroundConfiguration key="background"/>
                                            <color key="baseForegroundColor" red="0.070588235294117646" green="0.070588235294117646" blue="0.070588235294117646" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </buttonConfiguration>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="titleLocalizeKey" value="login_google_btn"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="GoogleLoginBtnTapped:" destination="kwA-e6-Ck9" eventType="touchUpInside" id="aK3-RU-skO"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aLw-mW-hdd">
                                        <rect key="frame" x="0.0" y="66" width="292" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="P7J-w0-UqT"/>
                                            <constraint firstAttribute="width" constant="292" id="ifJ-ep-DuP"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Sign up with Apple">
                                            <backgroundConfiguration key="background" image="appleBorder"/>
                                            <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </buttonConfiguration>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="titleLocalizeKey" value="YL_SigninwithApple"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="AppleLoginBtnTapped:" destination="kwA-e6-Ck9" eventType="touchUpInside" id="RyU-SI-jJK"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="z5p-QF-FUb">
                                        <rect key="frame" x="0.0" y="132" width="292" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="8ME-Ln-Zn4"/>
                                            <constraint firstAttribute="width" constant="292" id="afh-hs-RjY"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Sign up with Email">
                                            <backgroundConfiguration key="background" image="mailBorder"/>
                                            <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </buttonConfiguration>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="titleLocalizeKey" value="login_by_email"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="MailLoginBtnTapped:" destination="kwA-e6-Ck9" eventType="touchUpInside" id="dgA-pg-waJ"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="LQk-eQ-gMA"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="ayf-Mx-QWK" secondAttribute="bottom" id="0w5-iT-5Wt"/>
                            <constraint firstItem="yi2-pW-MMe" firstAttribute="leading" secondItem="LQk-eQ-gMA" secondAttribute="leading" id="3IF-Zj-Kj5"/>
                            <constraint firstItem="fao-uF-1vV" firstAttribute="top" secondItem="yi2-pW-MMe" secondAttribute="bottom" constant="-25" id="Efe-9Y-5SL"/>
                            <constraint firstItem="HZi-v0-Dai" firstAttribute="top" secondItem="358-cS-zLZ" secondAttribute="top" id="FJ3-LI-81o"/>
                            <constraint firstItem="fao-uF-1vV" firstAttribute="centerX" secondItem="358-cS-zLZ" secondAttribute="centerX" id="J71-li-Hgf"/>
                            <constraint firstItem="HZi-v0-Dai" firstAttribute="leading" secondItem="LQk-eQ-gMA" secondAttribute="leading" id="PPa-0p-U2i"/>
                            <constraint firstItem="ayf-Mx-QWK" firstAttribute="leading" secondItem="LQk-eQ-gMA" secondAttribute="leading" id="Xbk-uR-5pj"/>
                            <constraint firstItem="ayf-Mx-QWK" firstAttribute="top" secondItem="fao-uF-1vV" secondAttribute="top" constant="-128" id="ZQW-kg-dYR"/>
                            <constraint firstItem="ayf-Mx-QWK" firstAttribute="trailing" secondItem="LQk-eQ-gMA" secondAttribute="trailing" id="a4l-70-8DT"/>
                            <constraint firstItem="yi2-pW-MMe" firstAttribute="top" secondItem="358-cS-zLZ" secondAttribute="top" id="cfL-X3-yhi"/>
                            <constraint firstItem="LQk-eQ-gMA" firstAttribute="trailing" secondItem="yi2-pW-MMe" secondAttribute="trailing" id="m0O-my-Rum"/>
                            <constraint firstAttribute="trailing" secondItem="HZi-v0-Dai" secondAttribute="trailing" id="xE5-lB-blR"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="xp1-BJ-dmW"/>
                    <nil key="simulatedTopBarMetrics"/>
                    <connections>
                        <outlet property="AppleLoginBtn" destination="aLw-mW-hdd" id="la8-Ii-0nZ"/>
                        <outlet property="GoogleLoginBtn" destination="qQt-nE-wmC" id="sHJ-VW-zGx"/>
                        <outlet property="bannerBgView" destination="yi2-pW-MMe" id="I7l-x0-IDJ"/>
                        <outlet property="bgImgView" destination="HZi-v0-Dai" id="85i-50-CyB"/>
                        <outlet property="loginStackView" destination="fao-uF-1vV" id="fKK-yv-F2m"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="OGA-H0-D3P" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="51.145038167938928" y="171.83098591549296"/>
        </scene>
        <!--Privacy Alert View Controller-->
        <scene sceneID="v7j-Ds-FUy">
            <objects>
                <viewController storyboardIdentifier="YLPrivacyAlertViewController" id="aJ4-GI-d0C" customClass="YLPrivacyAlertViewController" customModule="Distance" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="qVa-XO-GZg">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Lpl-FP-2yP">
                                <rect key="frame" x="37" y="199" width="319" height="454"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N4Z-CK-iJA">
                                        <rect key="frame" x="0.0" y="0.0" width="319" height="384"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bqlb_tc_bg_img" translatesAutoresizingMaskIntoConstraints="NO" id="b1I-2d-JIt">
                                                <rect key="frame" x="0.0" y="0.0" width="319" height="384"/>
                                            </imageView>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="确定恢复后，人物服饰和家具将恢复到最近一次会员期间更新的装扮" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t1h-dA-k0n" customClass="ActiveLabel" customModule="ActiveLabel">
                                                <rect key="frame" x="25" y="60" width="269" height="292"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" red="0.18040573600000001" green="0.17672678829999999" blue="0.20951929690000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="lineSpacing">
                                                        <real key="value" value="7"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="隐私政策" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="auH-cd-5tX">
                                                <rect key="frame" x="127.66666666666666" y="25" width="64" height="19"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="19" id="91h-27-aCG"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="PingFangSC-Semibold" family="PingFang SC" pointSize="16"/>
                                                <color key="textColor" red="0.46666666666666667" green="0.29411764705882354" blue="0.25490196078431371" alpha="1" colorSpace="deviceRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="cwxq_cat" translatesAutoresizingMaskIntoConstraints="NO" id="sjb-3i-BIF">
                                                <rect key="frame" x="85.666666666666686" y="-56" width="148" height="67"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="67" id="yDI-CL-LOf"/>
                                                    <constraint firstAttribute="width" constant="148" id="zr4-Wh-xha"/>
                                                </constraints>
                                            </imageView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="B40-0N-pUB">
                                                <rect key="frame" x="15" y="52" width="289" height="300"/>
                                                <subviews>
                                                    <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bXd-TZ-AmY">
                                                        <rect key="frame" x="0.0" y="0.0" width="289" height="300"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <wkWebViewConfiguration key="configuration">
                                                            <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                                            <wkPreferences key="preferences"/>
                                                        </wkWebViewConfiguration>
                                                    </wkWebView>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="300" id="05Z-1K-En7"/>
                                                    <constraint firstItem="bXd-TZ-AmY" firstAttribute="leading" secondItem="B40-0N-pUB" secondAttribute="leading" id="9B4-pZ-9SS"/>
                                                    <constraint firstAttribute="trailing" secondItem="bXd-TZ-AmY" secondAttribute="trailing" id="Ic4-in-cun"/>
                                                    <constraint firstAttribute="bottom" secondItem="bXd-TZ-AmY" secondAttribute="bottom" id="cEK-5v-dC2"/>
                                                    <constraint firstItem="bXd-TZ-AmY" firstAttribute="top" secondItem="B40-0N-pUB" secondAttribute="top" id="ot3-5m-dMa"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="t1h-dA-k0n" secondAttribute="trailing" constant="25" id="2iV-Z2-Itc"/>
                                            <constraint firstAttribute="bottom" secondItem="B40-0N-pUB" secondAttribute="bottom" constant="32" id="4fi-6Z-T8N"/>
                                            <constraint firstItem="B40-0N-pUB" firstAttribute="leading" secondItem="N4Z-CK-iJA" secondAttribute="leading" constant="15" id="BpA-u6-bek"/>
                                            <constraint firstAttribute="bottom" secondItem="t1h-dA-k0n" secondAttribute="bottom" constant="32" id="DU1-8z-Tty"/>
                                            <constraint firstItem="t1h-dA-k0n" firstAttribute="leading" secondItem="N4Z-CK-iJA" secondAttribute="leading" constant="25" id="DnN-Ib-XZn"/>
                                            <constraint firstAttribute="bottom" secondItem="b1I-2d-JIt" secondAttribute="bottom" id="EN2-PO-Y1m"/>
                                            <constraint firstItem="auH-cd-5tX" firstAttribute="top" secondItem="N4Z-CK-iJA" secondAttribute="top" constant="25" id="HDA-1e-Whz"/>
                                            <constraint firstItem="auH-cd-5tX" firstAttribute="centerX" secondItem="N4Z-CK-iJA" secondAttribute="centerX" id="IfV-fi-Kud"/>
                                            <constraint firstItem="sjb-3i-BIF" firstAttribute="centerX" secondItem="N4Z-CK-iJA" secondAttribute="centerX" id="Smb-wG-Cvh"/>
                                            <constraint firstItem="t1h-dA-k0n" firstAttribute="top" secondItem="auH-cd-5tX" secondAttribute="bottom" constant="16" id="TZw-TK-ZOn"/>
                                            <constraint firstItem="t1h-dA-k0n" firstAttribute="centerX" secondItem="N4Z-CK-iJA" secondAttribute="centerX" id="cHe-Fg-mKE"/>
                                            <constraint firstItem="b1I-2d-JIt" firstAttribute="leading" secondItem="N4Z-CK-iJA" secondAttribute="leading" id="jcM-ar-fky"/>
                                            <constraint firstAttribute="trailing" secondItem="B40-0N-pUB" secondAttribute="trailing" constant="15" id="lvC-ro-6b7"/>
                                            <constraint firstItem="b1I-2d-JIt" firstAttribute="top" secondItem="sjb-3i-BIF" secondAttribute="bottom" constant="-11" id="m6Q-i7-tx9"/>
                                            <constraint firstItem="B40-0N-pUB" firstAttribute="top" secondItem="N4Z-CK-iJA" secondAttribute="top" constant="52" id="sfc-0k-ia7"/>
                                            <constraint firstAttribute="trailing" secondItem="b1I-2d-JIt" secondAttribute="trailing" id="t89-Yh-kgX"/>
                                            <constraint firstItem="b1I-2d-JIt" firstAttribute="top" secondItem="N4Z-CK-iJA" secondAttribute="top" id="y2Z-93-Vep"/>
                                        </constraints>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" placeholderIntrinsicWidth="146" placeholderIntrinsicHeight="50" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="U1i-Gy-YI5">
                                        <rect key="frame" x="86.666666666666686" y="406" width="146" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="O3y-Cq-LfI"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="N4Z-CK-iJA" firstAttribute="leading" secondItem="Lpl-FP-2yP" secondAttribute="leading" id="1ac-BZ-AXM"/>
                                    <constraint firstItem="U1i-Gy-YI5" firstAttribute="top" secondItem="N4Z-CK-iJA" secondAttribute="bottom" constant="22" id="FnU-dK-fCj"/>
                                    <constraint firstAttribute="width" constant="319" id="fMS-Oh-7DI"/>
                                    <constraint firstItem="N4Z-CK-iJA" firstAttribute="top" secondItem="Lpl-FP-2yP" secondAttribute="top" id="jUd-q7-pTk"/>
                                    <constraint firstAttribute="bottom" secondItem="U1i-Gy-YI5" secondAttribute="bottom" id="sYc-R1-vdT"/>
                                    <constraint firstAttribute="trailing" secondItem="N4Z-CK-iJA" secondAttribute="trailing" id="vRE-2T-Zcb"/>
                                    <constraint firstItem="U1i-Gy-YI5" firstAttribute="centerX" secondItem="Lpl-FP-2yP" secondAttribute="centerX" id="y7K-K1-Jdx"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="FOR-6L-XBM"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Lpl-FP-2yP" firstAttribute="centerY" secondItem="qVa-XO-GZg" secondAttribute="centerY" id="36g-Hb-hyQ"/>
                            <constraint firstItem="Lpl-FP-2yP" firstAttribute="centerX" secondItem="qVa-XO-GZg" secondAttribute="centerX" id="q4G-ix-XEO"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="messageLabel" destination="t1h-dA-k0n" id="gRZ-Q3-6VX"/>
                        <outlet property="stackView" destination="U1i-Gy-YI5" id="2yq-vF-nBF"/>
                        <outlet property="titleLabel" destination="auH-cd-5tX" id="Rcw-Pe-gSi"/>
                        <outlet property="webView" destination="bXd-TZ-AmY" id="Wpo-dR-wHW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="26e-SZ-7hJ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1661" y="854"/>
        </scene>
    </scenes>
    <resources>
        <image name="appleBorder" width="292" height="50"/>
        <image name="back_icon" width="8" height="15"/>
        <image name="bqlb_tc_bg_img" width="28.333333969116211" height="28.333333969116211"/>
        <image name="choose_date_image" width="24" height="24"/>
        <image name="cwxq_cat" width="148" height="67"/>
        <image name="cwxq_tc_qx_icon_51" width="50.333332061767578" height="50"/>
        <image name="dl_bg_img" width="375" height="667"/>
        <image name="dl_w_bg_img" width="393" height="398.66665649414062"/>
        <image name="female_selected" width="150" height="77.333335876464844"/>
        <image name="female_unselected" width="150" height="77.333335876464844"/>
        <image name="ic_cjwt" width="28" height="28"/>
        <image name="ic_lxwm" width="28" height="28"/>
        <image name="ic_yjfk" width="28" height="28"/>
        <image name="kp_bg_img" width="375" height="812"/>
        <image name="logo_img" width="88" height="88"/>
        <image name="loveInfoBg" width="375" height="812"/>
        <image name="lxwm_bcbdk_img" width="85.333335876464844" height="28.333333969116211"/>
        <image name="lxwm_bg_img" width="355.66665649414062" height="192"/>
        <image name="lxwm_gzh_img" width="101.33333587646484" height="101"/>
        <image name="lxwm_step1_img-1" width="263" height="20.333333969116211"/>
        <image name="lxwm_step234_img" width="302.33334350585938" height="428.66665649414062"/>
        <image name="mailBorder" width="292" height="50"/>
        <image name="male_selected" width="150" height="77.333335876464844"/>
        <image name="male_unselected" width="150" height="77.333335876464844"/>
        <image name="plus" width="10" height="10"/>
        <image name="random_header_image" width="28" height="28"/>
        <image name="register_bg" width="393" height="852"/>
        <image name="register_wow" width="15.333333015441895" height="31.666666030883789"/>
        <image name="triangle_down" width="10" height="8"/>
        <image name="type_line" width="3" height="14"/>
        <image name="userInfo_bz_icon" width="32" height="32"/>
        <image name="user_info_nextNor" width="240" height="48"/>
        <image name="wenhao" width="80" height="80"/>
        <image name="xzxckj_button_acc" width="140" height="50"/>
        <image name="yjdl_bg_btn" width="49" height="50"/>
        <image name="zhzh_wcxdzh_img" width="204" height="229"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
