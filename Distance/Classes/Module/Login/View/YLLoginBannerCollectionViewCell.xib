<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="YLLoginBannerCollectionViewCell" customModule="Distance" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="351" height="447"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="351" height="447"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="3tu-PK-oNX">
                        <rect key="frame" x="0.0" y="0.0" width="351" height="447"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bkv-aa-YFI">
                        <rect key="frame" x="40" y="275" width="311" height="24"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="24" id="Sa5-MW-e6c"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                        <color key="textColor" red="0.46666666670000001" green="0.29411764709999999" blue="0.25490196079999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qog-rA-ghn">
                        <rect key="frame" x="40" y="309" width="311" height="24"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="24" id="obB-VT-kyx"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.46666666670000001" green="0.29411764709999999" blue="0.25490196079999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="qog-rA-ghn" secondAttribute="bottom" constant="114" id="4ZZ-F7-YEN"/>
                <constraint firstAttribute="trailing" secondItem="Bkv-aa-YFI" secondAttribute="trailing" id="6Ja-t9-UXA"/>
                <constraint firstItem="3tu-PK-oNX" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="81o-eI-yVb"/>
                <constraint firstItem="3tu-PK-oNX" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Dz3-eJ-P2v"/>
                <constraint firstItem="qog-rA-ghn" firstAttribute="trailing" secondItem="Bkv-aa-YFI" secondAttribute="trailing" id="MfF-Hj-FpV"/>
                <constraint firstItem="qog-rA-ghn" firstAttribute="top" secondItem="Bkv-aa-YFI" secondAttribute="bottom" constant="10" id="QFO-43-Cqc"/>
                <constraint firstAttribute="trailing" secondItem="3tu-PK-oNX" secondAttribute="trailing" id="aol-Ld-ZyF"/>
                <constraint firstAttribute="bottom" secondItem="3tu-PK-oNX" secondAttribute="bottom" id="lsQ-ti-9fg"/>
                <constraint firstItem="Bkv-aa-YFI" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="40" id="qI1-jU-5o7"/>
                <constraint firstItem="qog-rA-ghn" firstAttribute="leading" secondItem="Bkv-aa-YFI" secondAttribute="leading" id="sAg-v6-0K5"/>
            </constraints>
            <size key="customSize" width="351" height="447"/>
            <connections>
                <outlet property="descriptionLab" destination="qog-rA-ghn" id="XBo-lb-O37"/>
                <outlet property="imageView" destination="3tu-PK-oNX" id="StN-9u-eA4"/>
                <outlet property="titleLab" destination="Bkv-aa-YFI" id="UBc-c4-Mrf"/>
            </connections>
            <point key="canvasLocation" x="359.5419847328244" y="27.112676056338028"/>
        </collectionViewCell>
    </objects>
</document>
