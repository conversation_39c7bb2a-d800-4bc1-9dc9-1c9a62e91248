<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="GSVipBannerCell" customModule="GuardTechSolutions" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="239" height="155"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="239" height="155"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="vip_autoNot_img" translatesAutoresizingMaskIntoConstraints="NO" id="IbN-FB-XKY">
                        <rect key="frame" x="0.0" y="0.0" width="239" height="155"/>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Qs-Hm-68R">
                        <rect key="frame" x="0.0" y="0.0" width="239" height="155"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="IbN-FB-XKY" secondAttribute="bottom" id="3Jx-5V-zW5"/>
                <constraint firstItem="IbN-FB-XKY" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="BtO-tc-I0e"/>
                <constraint firstAttribute="bottom" secondItem="5Qs-Hm-68R" secondAttribute="bottom" id="OBE-iC-HGM"/>
                <constraint firstItem="5Qs-Hm-68R" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="PZL-0Z-Mh8"/>
                <constraint firstAttribute="trailing" secondItem="IbN-FB-XKY" secondAttribute="trailing" id="hf4-CN-D0E"/>
                <constraint firstItem="IbN-FB-XKY" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="l8s-r2-Qg6"/>
                <constraint firstAttribute="trailing" secondItem="5Qs-Hm-68R" secondAttribute="trailing" id="qHF-WT-sFV"/>
                <constraint firstItem="5Qs-Hm-68R" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="t9P-p1-wxV"/>
            </constraints>
            <size key="customSize" width="239" height="155"/>
            <connections>
                <outlet property="contentBgView" destination="5Qs-Hm-68R" id="XWg-sM-9jD"/>
                <outlet property="imgView" destination="IbN-FB-XKY" id="i8o-Vv-gVR"/>
            </connections>
            <point key="canvasLocation" x="161.06870229007632" y="25.704225352112676"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="vip_autoNot_img" width="393" height="348"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
