//
//  GSVipBannerCell.swift
//  GuardTechSolutions
//
//  Created by chang<PERSON> on 2024/9/26.
//

import UIKit
import Lottie

class GSVipBannerCell: UICollectionViewCell {

    @IBOutlet weak var imgView: UIImageView!
    
    @IBOutlet weak var contentBgView: UIView!
    
	let animationView = LottieAnimationView()
	
	var name: String = "" {
		didSet {
			if name.isHTTPUrl {
				if let url = URL(string: name) {
					downloadAnimation(from: url)
				}
			} else {
				let animation = LottieAnimation.named(name)
				play(animation: animation)
			}
		}
	   
	}
	
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
		animationView.isUserInteractionEnabled = false
		animationView.contentMode = .scaleAspectFill
		animationView.loopMode = .loop
		contentBgView.addSubview(animationView)
		animationView.snp.remakeConstraints { make in
			make.edges.equalToSuperview()
		}
    }
	
	func downloadAnimation(from url: URL) {
		
		if let cache = LottieAnimationCache.shared, let animation = cache.animation(forKey: url.absoluteString) {
			play(animation: animation)
			return
		}
		Task {
			let animation = await LottieAnimation.loadedFrom(url: url, animationCache: LottieAnimationCache.shared)
			yl_onMain {
				self.play(animation: animation)
			}
		}
   }
	
	func play(animation: LottieAnimation?) {
		animationView.animation = animation
		animationView.play(fromProgress: 0, toProgress: 1, loopMode: .loop, completion: nil)
	}

}
