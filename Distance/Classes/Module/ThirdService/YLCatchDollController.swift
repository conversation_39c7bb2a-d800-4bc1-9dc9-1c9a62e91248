//
//  YLCatchDoll.swift
//  Distance
//
//  Created by chang he on 2022/5/7.
//

import UIKit
import SwiftyJSON

import SwiftyStoreKit
import CocoaLumberjack

import QCloudCore

//let doll_appid = "12083"
//let doll_AppSecret = "kHN0IXcvYCMwi3ujLpDO"
//
//let doll_appid = "12143"
//let doll_AppSecret = "tzimk8UD7Snl52HGf6rY"

class YLCatchDollController: YLWebViewController {

	init(webTitle: String?, url: String) {
		
		let urlString = WCRequestPublicParameters.fillInfoOfUrl(url, isEncodingURL: true)
		
		let signStr = YLDollTool.sortSignParmeters(urlString)
		
		let brrageUrl = urlString + "&sign=\(signStr)"
		
		let allowedCharacterSet = (CharacterSet(charactersIn: "^!*'();:@+$,/?%#[] ").inverted)
		
		let lastUrlString = brrageUrl.addingPercentEncoding(withAllowedCharacters: allowedCharacterSet)!
		
//		let lastUrlString = "https://jwwmt.loovee.com:1443/live?name=index9&debug=eruda" + "&\(encodingURL)"
		
		let parmeters = urlString.queryParameters
		
		if let isInPurchase = parmeters["isInPurchase"], isInPurchase == "1" {
			self.isInPurchase = true
		} else {
			self.isInPurchase = false
		}
		
		
		DDLogInfo("【抓娃娃】:\(lastUrlString)")
		super.init(urlString: lastUrlString, webTitle: webTitle)
	}
	
	required init?(coder: NSCoder) {
		super.init(coder: coder)
	}
	
	var isInPurchase: Bool = true
	
	override func didMove(toParent parent: UIViewController?) {
		super.didMove(toParent: parent)
		if parent == nil {
			self.webView.configuration.userContentController.removeScriptMessageHandler(forName: "showName")
		}
	}
	
	override func viewDidLoad() {
		super.viewDidLoad()

		sysNaviView.titleLabel.textColor = UIColor(0x774B41)
		sysNaviView.titleLabel.font = UIFont.fzShaoer(size: 19)

		self.webView.configuration.userContentController.add(self, name: "showName")
		
		navigationController?.interactivePopGestureRecognizer?.delegate = self
	}

	override func backButtonClick() {
		if webView.canGoBack {
			webView.goBack()
		} else {
			if isIdleTimerDisabled {
				UIApplication.shared.isIdleTimerDisabled = false
			}
			
			guard let _ = navigationController,
				  isDismiss == false else {
				self.dismiss(animated: true, completion: nil)
				return
			}
			let callBack = "window.onCloseH5Page(1)"
			self.webView.evaluateJavaScript(callBack, completionHandler: { [weak self](response, error) in
				if let _ = error {
					self?.navigationController?.popViewController(animated: true)
				}
			})
		}
	}
	
	override func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
		if message.name == "showName" {
			swiftCallJS(message.body)
		}
	}

	func swiftCallJS(_ body: Any) {
		let data = "\(body)".data(using: .utf8)
		let json = JSON(data)
		let title = json["title"].stringValue
		let content = json["content"].stringValue
		let linkurl = json["linkurl"].stringValue
		let picurl = json["picurl"].stringValue
		let shareList = json["sharelist"].arrayValue
		guard !shareList.isEmpty else { return }
		
		var isImageOnly = false
		if title.isEmpty && content.isEmpty && linkurl.isEmpty {
			isImageOnly = true
		}
		
		let shareItem = ShareItem(shareType: isImageOnly ? .image : .urlLink, title: title, content: content, url: linkurl, urlLink: linkurl, icon: picurl, onlyImg: isImageOnly, showSaveImageButton: false, isHomeImage: false)
		
//		let shareVc = LTShareViewController.show(in: self, shareItem: shareItem) { result in
//			let type: String
//			let code: Int
//			let msg: String
//			switch result {
//			case .success:
//				code = 1
//				msg = "分享成功"
//			case let .failure(error):
//				code = 0
//				msg = error.errorDescription
//			}
//			if let platform = LTShareViewController.platformType {
//				switch platform {
//				case .QQ:
//					type = "qq"
//				case .wechatTimeLine:
//					type = "weixin_friend"
//				case .wechatSession:
//					type = "weixin"
//				case .sina:
//					type = "sina"
//				default:
//					type = "unkonw"
//				}
//			} else {
//				type = "unkonw"
//			}
//			
//			let callBack = "app.share_callback(\(code), '\(type)', '\(msg)')"
//			self.webView.evaluateJavaScript(callBack, completionHandler: { response, error in
//
//			})
//			LTShareViewController.platformType = nil
//		} imClickAction: {_ in
//			
//			
//		}
//		
//		var platform = [LTShareViewController.shareType]()
//		for item in shareList {
//			if item == "weixin" {
//				platform.append(LTShareViewController.shareType.wechat)
//			}
//			
//			if item == "weixin_friend" {
//				platform.append(LTShareViewController.shareType.wechatTimeline)
//			}
//			
//			if item == "qq" {
//				platform.append(LTShareViewController.shareType.qq)
//			}
//			
//			if item == "sina" {
//				platform.append(LTShareViewController.shareType.weibo)
//			}
//		}
//		shareVc.showPlatforms(platform)

	}
	
	override func webView(_ webView: YLWebView, shouldStartLoadWith request: URLRequest, navigationType: YLWebViewNavigationType) -> Bool {
		guard let url = request.url else { return true }
		if url.absoluteString.contains(find: "ylcloser://pay") {
			let dic = url.queryParameters
			let json = JSON(["params":dic["params"]])
			if isInPurchase {
				createOrder(params: "\(json)", type: .apple)
			} else {
				YLThirdPayView.showPayView { [weak self]type in
					self?.createOrder(params: "\(json)", type: type)
				}
			}
			
			return false
		}
		return true
	}
	
	func createOrder(params: String, type: YLPayType) {
		
		func onPayResult(_ success: Bool, _ orderId:String) {
			YLHUDView.dismiss(view: topWindow)
			let code = success ? 1 : 0
			let callBack = "onAndroidPayStatus(\(code), '\(orderId)')"
			self.webView.evaluateJavaScript(callBack, completionHandler: { response, error in
				
			})
		}
		
		
		YLHUDView.showLoading(view: topWindow,disableUserInteraction: false)
		///WKTODO:来源
		///55抓娃娃
		dollApi.request(.createOrder(parameter: params, type: type.rawValue,fromScene: YLPayFromSceneStyle.catchDoll.rawValue)) { [weak self]result in
			guard let self = self else { return }
			
			if type != .apple {
				YLHUDView.dismiss(view: topWindow)
			}
			
			result.toJSONMapper { json in
				var data = json["data"]
				data["productId"] = JSON(data["ProductId"])
				let model = YLPayOrderModel.payModel(json: data, payType: type)
				YLPayManager.onPay(pay: model) { payResult in
					switch payResult {
					case .success:
						if type != .apple {
							YLHUDView.showLoading(view: topWindow, duration: 8, disableUserInteraction: false)
							DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 3) {
								onPayResult(true, model.orderId ?? "")
							}
						} else {
							onPayResult(true, model.orderId ?? "")
						}
					case let .failure(error):
						YLHUDView.dismiss(view: topWindow)
						self.view.yl_makeToast(error.errorDescription, position: .center)
						onPayResult(false, model.orderId ?? "")
					}
				}
			} failure: { error in
				YLHUDView.dismiss(view: topWindow)
				self.view.yl_makeToast(error.errorDescription, position: .center)
			}
		}
		
	}

}

/**
 正式环境抓娃娃发过来IM的key
 甜言蜜语key：FQsXpJ9g50uLYPZfIcSi
 甜言蜜语appid：12215
 */

extension YLCatchDollController {
	class func dollKey()->String {
		if YLTestEnviromentManager.shared.isTestEnviroment {
			return "tzimk8UD7Snl52HGf6rY"
		}
		return "FQsXpJ9g50uLYPZfIcSi"
	}
}

class YLDollTool: NSObject {
	
	static func sortSignParmeters(_ urlString: String) -> String {
		let parmeters = urlString.queryParameters
		
		let key = parmeters["key"] ?? ""
		
		var signParmeters = [String : Any]()
		if let appid = parmeters["appid"] {
			signParmeters["appid"] = appid
		}
		
		if let time = parmeters["time"] {
			signParmeters["time"] = time
		}
		
		if let userId = parmeters["user_id"] {
			signParmeters["user_id"] = userId
		}
		
		if let nickname = parmeters["nickname"] {
			signParmeters["nickname"] = nickname
		}
		
		if let headimageurl = parmeters["headimgurl"] {
			signParmeters["headimgurl"] = headimageurl
		}
		
		let signArr = signParmeters.sorted{ (t1, t2) -> Bool in
			return t1.0 < t2.0
		}

		let signStrArr = signArr.map{ (k:String,v:Any) -> String in
			return String(format:"%@=%@",k,String(describing: v))
		}
		
		let signUrl = signStrArr.joined(separator:"&")
		DDLogInfo("【抓娃娃】加密前的url:\(signUrl),====== key:\(key)")
		let sign = YLDollTool.signUrl(signUrl, key)
		
		return sign
	}
	
	
	static func signUrl(_ signUrlParmetersStr: String, _ key: String) -> String {
		var signStr = signUrlParmetersStr
		signStr += "&key=\(key)"
		let sign = QCloudEncrytMD5String(signStr) ?? ""
		return sign
	}
	
	
	//发现周边加密
	static func fxzbSignParmeters(_ urlString: String) -> String {
				
		let newUrl = WCRequestPublicParameters.fillInfoOfUrl(urlString)
		DDLogInfo("【发现周边加密前】:\(newUrl)")
		var signParmeters = newUrl.queryParameters
		let signKey = signParmeters["signKey"] ?? "41cc00f47cb2d159a5a4a013"
		signParmeters.removeValue(forKey: "signKey")
		signParmeters.removeValue(forKey: "sign")
		
		let arr = signParmeters.sorted{ (t1, t2) -> Bool in
			return t1.0 < t2.0
		}
		
		let strArr = arr.map{ (k:String,v:Any) -> String in
			return String(format:"%@=%@",k,String(describing: v))
		}
		
		var signUrl = strArr.joined(separator:"&")

		let signStr = signUrl + "&\(signKey)"
		let sign = QCloudEncrytMD5String(signStr) ?? ""
		
		signUrl += "&sign=\(sign.uppercased())"
		
		let paths = urlString.components(separatedBy: "?")
		
		let allowedCharacterSet = (CharacterSet(charactersIn: "!*'();:@+$,/?%#[] ").inverted)
		
		let encodingURL = signUrl.addingPercentEncoding(withAllowedCharacters: allowedCharacterSet)!
		
		let lastUrlString = ((paths.first ?? "http://notify.fxzb.vip") + "?\(encodingURL)")
		DDLogInfo("【发现周边加密后】:\(lastUrlString)")
		return lastUrlString
	}
}

extension YLCatchDollController: UIGestureRecognizerDelegate {
	func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
		backButtonClick()
		return false
	}
}
