//
//  YLSelectDatePickerView.swift
//  Distance
//
//  Created by chang he on 2021/5/27.
//

import UIKit
import SwiftyJSON
import SwiftDate

class YLSelectDatePickerView: UIView {
	
	init(frame: CGRect, date:Date? = nil, isLunar:Bool? = nil, isLoveTime:Bool = false,titleStr:String = "we_together_date".localized, displayOrder: OYDatePickerDisplayOrder = .MDY) {
		super.init(frame: .zero)
		
		self.isLoveTime = isLoveTime
		self.titleStr = titleStr
		if let dateValue = date {
			self.currentDate = dateValue
		} else if isLoveTime {
			self.currentDate = Date.datefromString(string: UserData.shared.loveTime, newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
		} else {
			self.currentDate = Date.datefromString(string: UserData.shared.birthDayDate, newformatter: "yyyy-MM-dd'T'HH:mm:ssZ")
		}
		if let isLunarValue = isLunar  {
			self.isLunar = isLunarValue
		} else if isLoveTime {
			self.isLunar = UserData.shared.defaultLoveTimeNunar
		} else {
			self.isLunar = UserData.shared.birthdaySelfNunar ?? false
		}
		self.displayOrder = displayOrder
		configUI()
		
	}

	var year:Int?
	var month:Int?
	var day:Int?
	
	var submitActionBlock:((Date, Bool)->())?
	var birthdayPicker = OYBirthdayPicker()
	
	var currentDate:Date = Date()
	var isLunar:Bool = false
	
	var titleStr = "we_together_date".localized
	var displayOrder: OYDatePickerDisplayOrder = .MDY
	
	lazy var contentView :UIView = {
		let contenView = UIView()
		contenView.backgroundColor = .white
		return contenView
	}()
	
	lazy var jianBianView: UIView = {
		let view = UIView()
		view.isUserInteractionEnabled = false
		view.backgroundColor = .clear
		return view
	}()
	
	func configUI() {
		self.frame = UIScreen.main.bounds
		self.backgroundColor = UIColor.clear
		let backView = UIView()
		backView.backgroundColor = UIColor.black
		backView.alpha = 0.5
		addSubview(backView)
		backView.snp.makeConstraints { (make) in
			make.left.right.bottom.top.equalToSuperview()
		}
		
		let tap = UITapGestureRecognizer.init(target: self, action: #selector(cancelBtnAction))
		backView.addGestureRecognizer(tap)
		
		addSubview(contentView)
		contentView.snp.makeConstraints { (make) in
			make.leading.trailing.equalToSuperview()
			make.bottom.equalToSuperview()
			make.height.equalTo(367+66+kBottomSafeAreaHeight)
		}
		
		if isLoveTime {
			let titleView = UIView()
			titleView.backgroundColor = .clear
			contentView.addSubview(titleView)
			titleView.snp.makeConstraints { make in
				make.top.equalToSuperview().offset(26)
				make.left.right.equalToSuperview()
				make.height.equalTo(26)
			}
			
			
			let titleLab = UILabel()
			titleLab.text = titleStr
			titleLab.font = UIFont.systemFont(ofSize: 15, weight: .semibold)
			titleLab.textColor = UIColor(0x7E421D)
			titleView.addSubview(titleLab)
			titleLab.snp.makeConstraints { make in
				make.center.equalToSuperview()
			}
			
			let rigthImage = UIImageView()
			rigthImage.image = UIImage(named: "labstc_ax_img")
			titleView.addSubview(rigthImage)
			rigthImage.snp.makeConstraints { make in
				make.right.equalTo(titleLab.snp.left)
				make.centerY.equalToSuperview()
			}
			
			let leftImage = UIImageView()
			leftImage.image = UIImage(named: "labstc_ax_imgri")
			titleView.addSubview(leftImage)
			leftImage.snp.makeConstraints { make in
				make.left.equalTo(titleLab.snp.right)
				make.centerY.equalToSuperview()
			}
			
			let isLove = self.titleStr == "we_together_date".localized
			leftImage.isHidden = !isLove
			rigthImage.isHidden = !isLove
			
			contentView.snp.remakeConstraints { (make) in
				make.leading.trailing.equalToSuperview()
				make.bottom.equalToSuperview()
				make.height.equalTo(367+66+56+kBottomSafeAreaHeight)
			}
		}
		
		birthdayPicker.setCotainerViewFrame(false)
		birthdayPicker.isLunar = self.isLunar
		
		//海外1.0隐藏中国的农历
		birthdayPicker.calendarSwitcherNormal.isHidden = true
		
		if birthdayPicker.isLunar == true {
			let dateStr = YLChineseCalendar.getCZCalendarYearMonthDay(withYear: self.currentDate.yl.year, month: self.currentDate.month, day: self.currentDate.day)
			let dateTime = Date.datefromString(string: dateStr, newformatter: "yyyy-MM-dd")
			
			let isLeap = YLChineseCalendar.isLeapYear(self.currentDate.yl.year, month: self.currentDate.month, day: self.currentDate.day)
			
			let leapMonth = JWLunarCalendarDB.indexOfLeapMonth(inYear: dateTime.yl.year)
			var realMonth = dateTime.month
			if leapMonth > 0 && (leapMonth < realMonth || isLeap)  {
				realMonth = realMonth + 1
			}
			
			birthdayPicker.year = dateTime.yl.year
			birthdayPicker.month = realMonth
			birthdayPicker.day = dateTime.day
		} else {
			birthdayPicker.year = self.currentDate.yl.year
			birthdayPicker.month = self.currentDate.month
			birthdayPicker.day = self.currentDate.day
		}
		
		birthdayPicker.showLunar = true
		birthdayPicker.displayOrder = displayOrder
		
		contentView.addSubview(birthdayPicker)
		birthdayPicker.snp.makeConstraints { make in
			make.left.equalToSuperview()
			make.right.equalToSuperview()
			make.top.equalToSuperview().offset(24)
			make.height.equalTo(367)
		}
		
		
		year = self.currentDate.yl.year
		month = self.currentDate.month
		day = self.currentDate.day
		
		birthdayPicker.completionBlock = { [weak self] isLeap in
			
			guard let self = self else {
				return
			}
			if self.birthdayPicker.isLunar == true {
				
				let leapMonth = JWLunarCalendarDB.indexOfLeapMonth(inYear: self.birthdayPicker.year)
				var realMonth = self.birthdayPicker.month
				if (leapMonth > 0 && (leapMonth < realMonth || isLeap)) {
					realMonth = realMonth-1;
				}
				
				let dateStr = YLChineseCalendar.getGLCalendarYear(self.birthdayPicker.year, month:realMonth, day: self.birthdayPicker.day, isLeap: isLeap)
				let dateTime = Date.datefromString(string: dateStr, newformatter: "yyyy-MM-dd")
				self.year =	dateTime.year
				self.month = dateTime.month
				self.day = dateTime.day
			} else {

				self.year = self.birthdayPicker.year
				self.month = self.birthdayPicker.month
				self.day = self.birthdayPicker.day
			}
			
		}
		
		let okBtn = UIButton.init(type: .custom)
		okBtn.setTitle("done".localized, for: .normal)
		okBtn.setTitleColor(UIColor(0x774B41), for: .normal)
		okBtn.backgroundColor = UIColor(0xFDE383)
		contentView.addSubview(okBtn)
		okBtn.addTarget(self, action: #selector(okBtnAction), for: .touchUpInside)
		okBtn.snp.makeConstraints { (make) in
			make.top.equalTo(birthdayPicker.snp.bottom).offset(24)
			make.leading.equalToSuperview().offset(30)
			make.trailing.equalToSuperview().offset(-30)
			make.bottom.equalToSuperview().offset(-(12+kBottomSafeAreaHeight))
			make.height.equalTo(54)
		}
		
		contentView.addSubview(jianBianView)
		jianBianView.snp.makeConstraints { make in
			make.leading.trailing.equalToSuperview()
			make.centerY.equalTo(birthdayPicker.snp.centerY)
			make.height.equalTo(60)
		}
		
		DispatchQueue.main.async {
			okBtn.yl.layer(radius: 27, borderWidth: 1.5, borderColor: UIColor(0x774B41))
			self.contentView.yl.layer(corners: [.topLeft, .topRight], radius: 24)
		}
	}
	
	@objc func okBtnAction () {
		if let block = submitActionBlock {
			
			let region = Region(calendar: Calendar(identifier: .gregorian))
			let date = Date(year: year!, month: month!, day: day!, hour: 0, minute: 0, region: region)
			
			if date.timeIntervalSinceNow > 0 {
				self.yl_makeToast("love_list_before_today".localized, position: .center)
			} else {
				block(date, birthdayPicker.isLunar)
				hiddenView()
			}
		}
	}
	
	@objc func cancelBtnAction () {
		hiddenView()
	}
	
	func hiddenView() {
		DispatchQueue.main.async {
			self.removeFromSuperview()
		}
	}
	
	var isLoveTime = false
	
	func show() {
		if let window = topWindow {
			window.addSubview(self)
		} else {
			topWindow?.addSubview(self)
		}
		
	}
	
	
	required init?(coder: NSCoder) {
		fatalError("init(coder:) has not been implemented")
	}
	
	
	
}
