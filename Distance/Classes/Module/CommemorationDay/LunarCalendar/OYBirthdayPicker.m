//
//  OYBirthdayPicker.m
//  lunarCompute
//
//  Created by ohyeah on 17/8/8.
//  Copyright © 2017年 ohyeah. All rights reserved.
//

#import "OYBirthdayPicker.h"
#import "Masonry.h"
#import "OYCalendarData.h"
#import "YLChineseCalendar.h"
#import "Distance-Swift.h"

#define kScreenW [UIScreen mainScreen].bounds.size.width
#define kScreenH [UIScreen mainScreen].bounds.size.height

typedef NS_ENUM(NSInteger,CalendarType){
    CalendarTypeLunar = 01,
    CalendarTypeNormal = 00,
    CalendarTypeLunarIgnoreYear = 11,
    CalendarTypeNormalIgnoreYear = 10
};


#define HEIGHT 300

#define normalCalendarSelectedImg @"normalC"
#define lunarCalendarSelectedImg @"lunarC"
#define normalCalendarDefaultImg @"normalN"
#define lunarCalendarDefaultImg @"lunarN"

#define yearIgnoredImg @"ignore"
#define yearDefultImg @"normal"


@interface OYBirthdayPicker () <UIPickerViewDelegate, UIPickerViewDataSource>
{
	UIView *_menuView;
}
//控件
@property (strong,nonatomic)UIView *containerView;
@property (strong,nonatomic)UIPickerView *innerPickView;
//@property (strong,nonatomic)UIButton *calendarSwitcherNormal;
@property (strong,nonatomic)UIButton *yearIgnoreButton;
@property (assign,nonatomic)BOOL isShown;

//状态
@property (assign,nonatomic)NSInteger dayComponent;
@property (assign,nonatomic)NSInteger monthComponent;
@property (assign,nonatomic)NSInteger yearComponent;
@property (assign,nonatomic)CalendarType calendarType;
@property (assign,nonatomic)NSInteger endDateYear;
@property (assign,nonatomic)NSInteger endDateMonth;
@property (assign,nonatomic)NSInteger endDateDay;



//数据
@property (strong,nonatomic)NSArray *years;
@property (strong,nonatomic)NSArray *monthLunarStringArray;
@property (strong,nonatomic)NSArray *dayLunarStringArray;

@property (strong,nonatomic)NSArray *dayNormalNumberArray;
@property (strong,nonatomic)NSDictionary *calendarProperties;

//工具
@property (strong,nonatomic)OYCalendarData *data;


@end

@implementation OYBirthdayPicker
{
    OYDate todayNormal;
    OYDate todayLunar;
	
	NSInteger currentMonth;
	NSInteger currentDay;
}


- (instancetype)init
{
	self = [super init];
	if (self) {
		
	   
	}
	return self;
}

-(void)initViews:(BOOL)small {
    self.backgroundColor = [UIColor clearColor];
//    self.frame = CGRectMake(0, 0, kScreenW, HEIGHT);
    self.userInteractionEnabled = YES;
    
    //整个控件的容器
	self.containerView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, small?318:kScreenW, 286)];
    [self addSubview:_containerView];
	[_containerView mas_makeConstraints:^(MASConstraintMaker *make) {
		make.edges.equalTo(self);
	}];
    
    //顶部菜单容器
	UIView *menuView = [[UIView alloc]init];
    menuView.backgroundColor = [UIColor clearColor];
    [self.containerView addSubview:menuView];
	[menuView mas_makeConstraints:^(MASConstraintMaker *make) {
		make.left.offset(0);
		make.right.offset(0);
		make.top.offset(0);
		if (small) {
			make.height.offset(60);
		} else {
			make.height.offset(0);
		}
	}];
	_menuView = menuView;
	
    //公历按钮
    self.calendarSwitcherNormal = [UIButton buttonWithType:UIButtonTypeCustom];
	[self.calendarSwitcherNormal setImage:[UIImage imageNamed:@"xzsj_gl_icon"] forState:0];
	[self.calendarSwitcherNormal setImage:[UIImage imageNamed:@"xzsj_nl_icon"] forState:UIControlStateSelected];
    [menuView addSubview:_calendarSwitcherNormal];
    [self.calendarSwitcherNormal mas_makeConstraints:^(MASConstraintMaker *make) {
		make.centerX.equalTo(menuView);
        make.centerY.equalTo(menuView).offset(8);
    }];
    [self.calendarSwitcherNormal addTarget:self action:@selector(calendarSwitchAction:) forControlEvents:UIControlEventTouchUpInside];
	if (!small) {
		self.calendarSwitcherNormal.hidden = YES;
	}
	self.calendarSwitcherNormal.selected = NO;
	self.isYearIgnored = NO;
	self.isLunar = NO;
	
    //内置picker
    self.innerPickView=[[UIPickerView alloc]init];
	[self clearSpearatorLine:self.innerPickView];
    self.innerPickView.backgroundColor = [UIColor clearColor];
    [self.containerView addSubview:_innerPickView];
    [self.innerPickView mas_makeConstraints:^(MASConstraintMaker *make) {
		make.left.offset(0);
		make.right.offset(0);
        make.bottom.offset(0);
        make.top.equalTo(menuView.mas_bottom).offset(0);
    }];
    _innerPickView.delegate=self;
    _innerPickView.dataSource=self;
    _isShown = NO;

}

- (void)setCotainerViewFrame:(BOOL)small {
	[self initViews:small];
	[self initData];
	//endDateMode与年份信息相关，因此必须初始化
	self.endDateMode = OYEndDateModeDefault;
}

- (void)setShowLunar:(BOOL)showLunar {
	_showLunar = showLunar;
	_menuView.hidden = !showLunar;

	if (!showLunar) {
		[_menuView mas_updateConstraints:^(MASConstraintMaker *make) {
			make.left.offset(0);
			make.right.offset(0);
			make.top.offset(0);
			make.height.offset(30);
		}];
		
		[self.innerPickView mas_updateConstraints:^(MASConstraintMaker *make) {
			make.left.offset(0);
			make.right.offset(0);
			make.bottom.offset(0);
			make.top.equalTo(_menuView.mas_bottom).offset(0);
		}];
	}
	
}

-(void)initData{
	_displayOrder = OYDatePickerDisplayOrderYMD;
	
    _data = [OYCalendarData new];
    
    //这里获取的当天农历日期 是字面日期，在有闰月的时候可能会比实际月份少一个月
    todayLunar = [_data todayLunar];
    todayNormal = [_data todayNormal];

	if (self.isLunar) {
		_year = todayLunar.year;
		_month = todayLunar.month;
		_day = todayLunar.day;
	} else {
		_year = todayNormal.year;
		_month = todayNormal.month;
		_day = todayNormal.day;
	}
    
}



#pragma mark - button actions

-(void)calendarSwitchAction:(UIButton *)button {

	button.selected = !button.selected;
	self.isLunar = button.selected;
	
	[self changeData];
	
//	[self pickerView:self.innerPickView didSelectRow:self.day-1 inComponent:2];
	
}

- (void)changeData {
	if (self.isLunar) {
		NSString *date = [YLChineseCalendar getCZCalendarYearMonthDayWithYear:self.year month:self.month day:currentDay];
		NSArray *arr = [date componentsSeparatedByString:@"-"];
		if (arr.count>=3) {
			self.year = [arr[0] integerValue];
			BOOL isLeap = [YLChineseCalendar isLeapYear:self.year month:self.month day:self.day];
			NSInteger realMonth = [arr[1] integerValue];
			NSInteger leapMonth = [JWLunarCalendarDB indexOfLeapMonthInYear:self.year];
			if (leapMonth > 0 && (leapMonth < realMonth || ((leapMonth == realMonth) && isLeap))) {
				realMonth = realMonth + 1;
			}
			self.month = realMonth;
			self.day = [arr[2] integerValue];
			[_innerPickView selectRow:self.month-1 inComponent:_monthComponent animated:false];
		}
	} else {
		BOOL isLeap = [[NSString stringWithFormat:@"%@", self.monthLunarStringArray[self.month-1]] containsString:@"闰"];
		
		NSInteger month = self.month;
		NSInteger leapMonth = [JWLunarCalendarDB indexOfLeapMonthInYear:self.year];
		if (leapMonth > 0 && (leapMonth < self.month || isLeap)) {
			month = self.month-1;
		}
		
		NSString *date = [YLChineseCalendar getGLCalendarYear:self.year month:month day:currentDay isLeap:isLeap];
		NSArray *arr = [date componentsSeparatedByString:@"-"];
		if (arr.count>=3) {
			self.year = [arr[0] integerValue];
			self.month = [arr[1] integerValue];
			self.day = [arr[2] integerValue];
		}
		
	}
}


-(void)ignoreYearButtonAction{
    self.isYearIgnored = !self.isYearIgnored;
}


-(void)completionAction:(UIButton *)button{
//    if (_isShown) {
//        //下滑消失
//        self.userInteractionEnabled = NO;
//        _isShown = NO;
//        [UIView animateWithDuration:0.4 animations:^{
//            self.containerView.transform = CGAffineTransformTranslate(self.transform,0,HEIGHT);
//        } completion:^(BOOL finished) {
//            if (_completionBlock != nil) {
////                [button isEqual:self.confirmButton]?_completionBlock(YES):_completionBlock(NO);
//            }
//        }];
//    }
}


#pragma mark - getters

-(NSInteger)endDateDay{
    return _isLunar?todayLunar.day:todayNormal.day;
}

-(NSInteger)endDateMonth{
    return _isLunar?todayLunar.month:todayNormal.month;
}

-(NSInteger)endDateYear{
    if (_endDateMode == OYEndDateModeToday) {
        return _isLunar?todayLunar.year:todayNormal.year;
    }else{
        return 2099;
    }
}

//-(NSString *)yearString{
//    return _isLunar?[self.data lunarYearStringOfYear:_year]:[_years[_year - OYFirstYear] stringByAppendingString:@"年"];
//}
//
//-(NSString *)monthString{
//    return _isLunar? _monthLunarStringArray[_month-1]:[NSString stringWithFormat:@"%ld月",(long)_month];
//}
//
//-(NSString *)dayString{
//    return _isLunar? _dayLunarStringArray[_day-1]:[NSString stringWithFormat:@"%ld日",(long)_day];
//}

-(NSString *)isLunarString{
    return _isLunar?@"农历":@"公历";
}

#pragma mark - setters

//以下setter都会导致picker更新（控件状态、数据源）



-(void)setEndDateMode:(OYEndDateMode)endDateMode{
    if (_endDateMode != endDateMode) {
        _endDateMode = endDateMode;
        [self updateYearData];
        [self updateComponentsAfterCalendarChanged];
    }
}


-(void)setIsLunar:(BOOL)isLunar{
    _isLunar = isLunar;
    self.calendarType = _isYearIgnored * 10 + _isLunar;
//    if (_isLunar) {
////        self.calendarSwitcherLunar.enabled = NO;
////        self.calendarSwitcherNormal.enabled = YES;
//    }else{
////        self.calendarSwitcherLunar.enabled = YES;
//        self.calendarSwitcherNormal.enabled = NO;
//    }

	self.calendarSwitcherNormal.selected = _isLunar;
}


-(void)setIsYearIgnored:(BOOL)isYearIgnored{
    _isYearIgnored = isYearIgnored;
    if (_isYearIgnored) {
        _monthComponent = [self monthComponentIndex];
        _dayComponent = [self dayComponentIndex];
        [_yearIgnoreButton setImage:[UIImage imageNamed:yearIgnoredImg] forState:UIControlStateNormal];
    }
    else{
        _monthComponent = [self monthComponentIndex];
        _dayComponent = [self dayComponentIndex];
        [_yearIgnoreButton setImage:[UIImage imageNamed:yearDefultImg] forState:UIControlStateNormal];
    }
    self.calendarType = _isYearIgnored * 10 + _isLunar;
}

-(void)setCalendarType:(enum CalendarType)calendarType{
    if(_calendarType != calendarType){
        _calendarType = calendarType;
        [self updateComponentsAfterCalendarChanged];
    }
}


-(void)setYear:(NSInteger)year{
    if (_year != year && year >= OYFirstYear && year - OYFirstYear < self.years.count ) {
        _year = year;
        if (_innerPickView) {
            //刷新
            [self updateComponentsAfterSelectedYearChanged];
        }
    }
}

-(void)setMonth:(NSInteger)month{
    if (month < 1) {
        return;
    }
    NSInteger maxMonth = _isLunar?[self.monthLunarStringArray count]:[self.dayNormalNumberArray count];
	
    if(_month != month && month <= maxMonth){
        _month = month;
        if (_innerPickView) {
            //刷新
            [self updateDayComponentAfterSelectedMonthChaged];
        }
    }

	currentMonth = _month;
}

-(void)setDay:(NSInteger)day{
    if (day < 1) {
        return;
    }
    NSInteger maxDay = _isLunar?[self.dayLunarStringArray count]:[self.dayNormalNumberArray[_month-1] integerValue];
    if (_day != day && day <= maxDay) {
        _day = day;
        if (_innerPickView) {
            //刷新
            [_innerPickView selectRow:_day-1 inComponent:_dayComponent animated:NO];
        }
    }
	
	if (self.completionBlock) {
		if (_month<=0) {
			return;
		}
		BOOL isLeap = NO;
		NSString *month = _monthLunarStringArray[_month-1];
		if ([month containsString:@"闰"]) {
			isLeap = YES;
		}
		self.completionBlock(isLeap);
	}
	currentDay = _day;
}

- (void)setDisplayOrder:(OYDatePickerDisplayOrder)displayOrder {
	_displayOrder = displayOrder;
	_yearComponent = [self yearComponentIndex];
	_monthComponent = [self monthComponentIndex];
	_dayComponent = [self dayComponentIndex];

	[self.innerPickView reloadAllComponents];
	[self sychronizePickerStatusWithAnimation:NO];
}




#pragma mark - dataSource update

/*
 * 更新picker数据源的正确顺序：
 * -> 1.更新picker数据源 (更新数组)
 * -> 2.更新选中状态相关变量 (更新_year,_month,_day)
 * -> 3.重新加载picker，使最新数据源生效 (reloadComponent:/reloadAllComponent)
 * -> 4.让picker同步选中状态 (selectRow:InComponent)
 */


-(void)updateComponentsAfterCalendarChanged{
    //1.更新picker数据源
    //2.更新选中状态相关变量
    switch (_calendarType) {
        case CalendarTypeLunarIgnoreYear:
            _monthLunarStringArray = [OYCalendarData standaloneMonthStringsOfLunarYear];
            _dayLunarStringArray = [OYCalendarData standaloneDayStringsOfLunarMonth];
            break;
        case CalendarTypeNormalIgnoreYear:
            _dayNormalNumberArray = [OYCalendarData standaloneNumberOfDaysInEveryMonth];
            break;
        case CalendarTypeLunar:
            //农历要刷新月数据和日数据
            [self updateLunarMonthData];
            [self updateLunarDayData];
            break;
        case CalendarTypeNormal:
            //公历要刷新日数据
            [self updateNormalDayData];
            break;
        default:
            break;
    }

    //3.重新加载picker，使最新数据源生效
    [_innerPickView reloadAllComponents];
    //4.让picker同步选中状态
    [self sychronizePickerStatusWithAnimation:YES];

}
//当年份改变的时候 刷新月、日组件
-(void)updateComponentsAfterSelectedYearChanged{

    //1.更新数据源
    //2.更新选择状态
    switch (_calendarType) {
        case CalendarTypeLunar:
            
            [self updateLunarMonthData];
            [self updateLunarDayData];
            break;
        
        case CalendarTypeNormal:
            [self updateNormalDayData];
            break;
        default:
            break;
    }
    //3.重新加载picker，使最新数据源生效
    [_innerPickView reloadComponent:_monthComponent];
    [_innerPickView reloadComponent:_dayComponent];
    //4.让picker同步选中状态
    [self sychronizePickerStatusWithAnimation:NO];
}



//当月份改变的时候 刷新日组件
-(void)updateDayComponentAfterSelectedMonthChaged{
    //1.更新数据源
    //2.更新选择状态
    if (_calendarType == CalendarTypeLunarIgnoreYear) {
        return;
    }
    
    if (_calendarType == CalendarTypeLunar) {
        [self updateLunarDayData];
       
    }
    if (!_isLunar) {
        if (self.day > [self.dayNormalNumberArray[_month-1] integerValue]) {
            _day = [self.dayNormalNumberArray[_month-1] integerValue];
        }
    }
    //3.重新加载picker，使最新数据源生效
    [_innerPickView reloadComponent:_dayComponent];
    //4.让picker同步选中状态
    [self sychronizePickerStatusWithAnimation:NO];

}

//年
//1.更新数据源
//2.更新选中状态
-(void)updateYearData{
    
    self.years = [[NSMutableArray alloc]init];
    NSMutableArray *years = [NSMutableArray new];
    NSInteger endDateYear = self.endDateYear;
    for (int i = 1901; i <= endDateYear; i++) {
        [years addObject:[NSString stringWithFormat:@"%d",i]];
    }
    self.years = years;
    if (_year - OYFirstYear > self.years.count -1) {
        _year = self.years.count+OYFirstYear - 1;
    }
}


-(void)updateNormalDayData{
    //1.更新数据源
    self.dayNormalNumberArray = [OYCalendarData numberOfDaysInEveryMonthOfYear:_year];
    if (_year == self.endDateYear && _endDateMode == OYEndDateModeToday && self.endDateDay < [self.dayNormalNumberArray[self.endDateMonth-1] integerValue]) {
        NSMutableArray * dayNormalNumberArray = [[self.dayNormalNumberArray subarrayWithRange:NSMakeRange(0, self.endDateMonth)] mutableCopy];
        dayNormalNumberArray[self.endDateMonth-1] = @(self.endDateDay);
        self.dayNormalNumberArray = [dayNormalNumberArray copy];
    }
    //2.更新选中状态
    if (self.month > self.dayNormalNumberArray.count) {
        _month = self.dayNormalNumberArray.count;
    }
	
    if (self.day > [self.dayNormalNumberArray[_month-1] integerValue]) {
        _day = [self.dayNormalNumberArray[_month-1] integerValue];
    }
    
}

-(void)updateLunarMonthData{
    //1.更新数据源
    self.monthLunarStringArray = [self.data lunarMonthStringsOfYear:_year];
    if (_year == self.endDateYear  && _endDateMode == OYEndDateModeToday && self.endDateMonth < _monthLunarStringArray.count) {
        _monthLunarStringArray = [_monthLunarStringArray subarrayWithRange:NSMakeRange(0, self.endDateMonth)];
    }
    
    //2.更新选中状态
    if (self.month > self.monthLunarStringArray.count) {
        _month = self.monthLunarStringArray.count;
    }
    
}
-(void)updateLunarDayData{
    //1.更新数据源
    self.dayLunarStringArray = [self.data lunarDayStringsOfMonth:self.month inYear:_year];
    if (_year == self.endDateYear  && self.month ==self.endDateMonth && _endDateMode == OYEndDateModeToday && self.endDateDay < self.dayLunarStringArray.count) {
        self.dayLunarStringArray = [self.dayLunarStringArray subarrayWithRange:NSMakeRange(0, self.endDateDay)];
    }
    //2.更新选中状态
    if (self.day > self.dayLunarStringArray.count) {
        _day = self.dayLunarStringArray.count;
    }
}

-(void)sychronizePickerStatusWithAnimation:(BOOL)animated{
	if (!_isYearIgnored) {
		[_innerPickView selectRow:self.year - OYFirstYear inComponent:[self yearComponentIndex] animated:animated];
	}
	[_innerPickView selectRow:self.month - 1 inComponent:[self monthComponentIndex] animated:animated];
	[_innerPickView selectRow:self.day - 1 inComponent:[self dayComponentIndex] animated:animated];
}
#pragma mark - pickerView delegate

-(NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView{
    return _isYearIgnored ? 2: 3;
}

- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
//	switch (component) {
//		case 0:
//			return pickerView.bounds.size.width / 3 + 20;
//			break;
//		case 1:
//			return pickerView.bounds.size.width / 3 - 10;
//			break;
//		case 2:
//			return pickerView.bounds.size.width / 3 - 10;
//			break;
//		default:
//			break;
//	}
	return pickerView.bounds.size.width/3.0;
}

-(NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component{
	if (component == [self monthComponentIndex]) {
		switch (_calendarType) {
			case CalendarTypeLunar:
			case CalendarTypeLunarIgnoreYear:
				return  _monthLunarStringArray.count;
			case CalendarTypeNormal:
				return _dayNormalNumberArray.count;
			case CalendarTypeNormalIgnoreYear:
				return 12;
			default:
				return 0;
		}
	} else if (component == [self dayComponentIndex]) {
		return _isLunar ? _dayLunarStringArray.count : [_dayNormalNumberArray[self.month-1] integerValue];
	} else if (component == [self yearComponentIndex]) {
		return _years.count;
	}
	return 0;
}

-(NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
	if (component == [self monthComponentIndex]) {
		NSString *keyString = [NSString stringWithFormat:@"month_array[%li]", (long)row];
		return _isLunar ? _monthLunarStringArray[row] : [[YLLocalizationManager shared] ylLocalizedStringC:keyString isSelf:YES];
	} else if (component == [self dayComponentIndex]) {
		return _isLunar ? _dayLunarStringArray[row] : [NSString stringWithFormat:@"%ld", row + 1];
	} else if (component == [self yearComponentIndex]) {
		return _isLunar ? [YLChineseCalendar getCZYear:_years[row]] : _years[row];
	}
	return @"";
}


-(void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
	if (component == [self monthComponentIndex]) {
		self.month = row + 1;
	} else if (component == [self dayComponentIndex]) {
		self.day = row + 1;
	} else if (component == [self yearComponentIndex]) {
		self.year = row + OYFirstYear;
	}
	
	if (self.completionBlock) {
		self.completionBlock([self isLeap]);
	}
}

- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
	return 60;
}

//重写方法
- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view{
    UILabel* pickerLabel = (UILabel*)view;
    if (!pickerLabel){
        pickerLabel = [[UILabel alloc] init];
        pickerLabel.adjustsFontSizeToFitWidth = YES;
        [pickerLabel setTextAlignment:NSTextAlignmentCenter];
        [pickerLabel setBackgroundColor:[UIColor clearColor]];
        [pickerLabel setFont:[UIFont systemFontOfSize:16]];
    }
    pickerLabel.text=[self pickerView:pickerView titleForRow:row forComponent:component];
    return pickerLabel;
}


- (BOOL)isLeap {
	if (self.isLunar) {
		if (self.month) {
			NSString *month = _monthLunarStringArray[_month-1];
			if ([month containsString:@"闰"]) {
				return YES;
			}
		}
	}
	
	return NO;
}

- (NSString *)yearString {
	return self.isLunar?[YLChineseCalendar getCZYear:_years[self.year-OYFirstYear]]:[NSString stringWithFormat:@"%ld",(long)_year];
}

-(NSString *)monthString{
	return self.isLunar? _monthLunarStringArray[self.month-1]:[NSString stringWithFormat:@"%ld",(long)_month];
}

-(NSString *)dayString{
	return self.isLunar? _dayLunarStringArray[self.day-1]:[NSString stringWithFormat:@"%ld",(long)_day];
}

- (void)clearSpearatorLine:(UIPickerView *)pickerView {
	if ([pickerView isKindOfClass:[UIPickerView class]])//取出UIPickerView
	{
		for(UIView *subView2 in pickerView.subviews)
		{
			if (subView2.frame.size.height < 1)//取出分割线view
			{
				subView2.hidden = YES;//隐藏分割线
			}
		}
	}
}

//MARK: 添加辅助方法：获取真实的年/月/日所在列
- (NSInteger)yearComponentIndex {
	if (_isYearIgnored) return NSNotFound;
	switch (self.displayOrder) {
		case OYDatePickerDisplayOrderYMD: return 0;
		case OYDatePickerDisplayOrderDMY: return 2;
		case OYDatePickerDisplayOrderMDY: return 2;
	}
	return 0;
}

- (NSInteger)monthComponentIndex {
	switch (self.displayOrder) {
		case OYDatePickerDisplayOrderYMD: return _isYearIgnored ? 0 : 1;
		case OYDatePickerDisplayOrderDMY: return 1;
		case OYDatePickerDisplayOrderMDY: return 0;
	}
	return 0;
}

- (NSInteger)dayComponentIndex {
	switch (self.displayOrder) {
		case OYDatePickerDisplayOrderYMD: return _isYearIgnored ? 1 : 2;
		case OYDatePickerDisplayOrderDMY: return 0;
		case OYDatePickerDisplayOrderMDY: return 1;
	}
	return 0;
}


@end
