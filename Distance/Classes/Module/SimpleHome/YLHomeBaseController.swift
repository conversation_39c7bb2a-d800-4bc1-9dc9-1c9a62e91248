//
//  YLHomeBaseController.swift
//  Distance
//
//  Created by chang<PERSON> on 2023/3/9.
//

import UIKit
import URLNavigator
import ThinkingSDK
import SwiftNotes

enum YLHomeType {
	case concise // 简洁版
	case single // 单人试玩
	case room // 从简洁版首页push过来
	case scene // 场景版首页
}

class YLHomeBaseController: DTBaseViewController, CustomNavigation {
	
	var customNavStyle: NavStyle = .clear
	
	override var isNavigationBarNeedHidden: Bool { true }
	
	open var currentType: YLHomeType {
		.concise
	}
	
	open var onLineView: UIImageView? {
		nil
	}
	
	var observer: NSKeyValueObservation?
	
	private var haveLoad = false
	
	///是否有未弹的（弹窗请求未完成点击了个人中心）
	var haveAlert = false
	
	@UserDefault("kHomeFirstShow", defaultValue: true)
	var firstLunch: Bool
	
	let group = DispatchGroup()
	
	override func viewWillAppear(_ animated: <PERSON>ol) {
		super.viewWillAppear(animated)
		YLChatAccessoryView.shared.setHidden(currentType != .room)
		YLAlerViewManager.shared.endTaskOperation()
		
	}
	
	override func viewDidAppear(_ animated: Bool) {
		super.viewDidAppear(animated)

	}

	override func viewWillDisappear(_ animated: Bool) {
		super.viewWillDisappear(animated)

	}
	
	override func viewDidLoad() {
		super.viewDidLoad()
		
		updatePairOnlineStatus()
		
		if currentType != .room {
			initialization()
			setupChatData()
			
			if let infoDictionary = Bundle.main.infoDictionary, let version = infoDictionary["CFBundleShortVersionString"] {
				DDLogInfo("进入首页-版本号:\(version), applicationState = \(UIApplication.shared.applicationState.rawValue)")
			} else {
				DDLogInfo("进入首页, applicationState = \(UIApplication.shared.applicationState.rawValue)")
			}
			
			YLStatisticsHelper.trackEventByShushu("ui_launch",dic: ["ui_path":"首页"])
			
			YLInterstitialAdManager.shared.loadInterstitialAd()
		
			when(UIApplication.didBecomeActiveNotification) { [weak self] _ in
				YLInterstitialAdManager.shared.loadInterstitialAd()
			}

			yl_onGlobal {
				UserData.shared.getUserNotifySetting()
				UserData.shared.getUserNotifySetting(queryOther: true)
			}
			
		} else {
			NotificationCenter.default.addObserver(self, selector: #selector(userDataRefresh(_:)), name: userDataRefreshNotification, object: nil)

		}
		
		Task {
			await reportUserSet()
		}
	}

	deinit {
		NotificationCenter.default.removeObserver(self)
	}
}

extension YLHomeBaseController {
	
	@objc open func initialization() {
		DDLogInfo("【手机是否设置密码】：\(UIDevice.isPasscodeEnabled())")
		
		YLChatAccessoryView.shared.addTo(topWindow)
		
		YLStatisticsHelper.trackEvent("Home.IM", dic: ["model": currentType == .concise ? "简洁" : "经典"])
		YLStatisticsHelper.trackEventByShushu("ui_launch",dic: ["ui_path":currentType == .concise ? "简洁版首页" : "场景版首页"],isOnce: true)
		
		//看电影
		observer = YLSharePlayManager.shared.observe(\YLSharePlayManager.otherInRoom, options: [.old, .new]) { [weak self] manager, change in
			guard let self = self else { return }
			if let new = change.newValue {
				if new {
					YLAlerViewManager.shared.addAlertQueueHandlers { taskOperation,isHome  in
						self.showMovieAlertViewIfNeed {
							taskOperation?.endOperation()
						}
					}
				}
				self.movieStateDidChanged(new)
			}
		}
		
		// 获取一下vip信息，给userda赋值
		UserData.shared.getVipInfo { [weak self](isVip, openType, error) in
			self?.vipStatusDidChange()
			UserData.shared.ifNeedOpenVipPage()
		}
		
		statisticAPNS()
		
		//推送
		if YLPushControl.isPush {
			YLPushControl.updatePushVC()
		}
		
	
		
		//注册通知
		regesterNotifications()
		
		//获取表情列表
		YLEmojiDataSource.shared.requestData()
		
		
		firstLunch = false
	}
	
	
	func setupChatData() {
		YLChatManager.shared.login() { [weak self]error in
			if error != nil {
				let num = YLChatManager.shared.unreadMessagesCount
				self?.homeBottomRedDotDidChange()
				YLChatAccessoryView.shared.badge = num
			}
		}
		YLChatManager.shared.unreadMessagesCountChangeHandler = { [weak self]num in
			self?.homeBottomRedDotDidChange()
			YLChatAccessoryView.shared.badge = num
		}
	}
	
}

//MARK: - 活动按钮
extension YLHomeBaseController {
	
	@objc public func getActivityModuleArray(completHandler:(()->Void)? = nil) {
		if currentType == .room {
			completHandler?()
			return
		}
		if let _ = completHandler {
			self.group.enter()
		}
		
		getSceneActivityList { [weak self]moduleArray in
			guard let self = self else { return }
			if let _ = completHandler {
				self.group.leave()
			}
			guard let moduleArray = moduleArray else { return }
			self.loadActivityButton(moduleArray)
		}
	}
	
	private func loadActivityButton(_ moduleArray:[YLHomeActivityModule]) {
		// 重置是否有拼图活动标记

		var redCacheArray = [YLRedHotCacheModel]()
		
		var buttons = [YLRedDotButton]()
		for model in moduleArray {
			
			//缓存点击时间 超过多少天不显示
			if model.cacheDay > 0 {
				let key = "\(currentType)\(String(describing: UserData.shared.pairId))\(String(describing: UserData.shared.userId))\(model.id)"
				let currentTimeStamp = Int(Date().timeIntervalSince1970)
				if let lastClcikTime = UserDefaults.standard.object(forKey: key) as? Int {
					let day = model.cacheDay * 24 * 60 * 60
					let differTime = currentTimeStamp - lastClcikTime
					if differTime > day {
						continue
					}
				}
				else
				{
					UserDefaults.standard.set(currentTimeStamp, forKey: key)
					UserDefaults.standard.synchronize()
				}
			}
			
			let type = ActivityType(rawValue: model.type)
			let button = YLRedDotButton(type: .custom)
			button.isScale = true
			button.linkURL = model.activityLinkUrl
			button.type = type
			button.model = model
			button.addTarget(self, action: #selector(self.activityButtonDidAction(_:)), for: .touchUpInside)



			
			//活动按钮红点处理
			var lastOpenDate : Int = 0
			if let findModel = YLHomeButtonManager.shared.findRedHot(saveKey: String(model.id),path: "ActivityButton"){
				lastOpenDate = findModel.lastOpenDate
			}
			
			if currentType == .concise {
				button.dotType = .redDotSimpleHome
			} else {
				button.dotType = .redDot(position: .top, topSpace: -46)
			}
			switch model.noticeType {
			case 1:
				let interval:TimeInterval = TimeInterval.init(lastOpenDate)
				let date = Date(timeIntervalSince1970: interval)
				if date.compare(toDate: Date(), granularity: .day) == .orderedSame {
					button.dotType = .none
					button.gifUrl = ""
				} else {
					if !model.noticeIcon.isEmpty {
						button.gifUrl = model.noticeIcon
					}
				}
			case 2:
				let btnStr = String(format: "ActivityButton_%d",model.id)
				if YLHomeButtonManager.shared.clickBtns.contains(btnStr) {
					button.dotType = .none
					button.gifUrl = ""
				} else {
					if !model.noticeIcon.isEmpty {
						button.gifUrl = model.noticeIcon
					}
				}
				break
			case 3:
				if lastOpenDate > 0 {
					button.dotType = .none
					button.gifUrl = ""
				} else {
					if !model.noticeIcon.isEmpty {
						button.gifUrl = model.noticeIcon
					}
				}
			default:
				button.dotType = .none
				button.gifUrl = ""
			}
			
			button.isHidden = true
			button.sd_setImage(with: URL(string: model.activityImgUrl), for: .normal) { [unowned button]_, _, _, _ in
				button.isHidden = false
			}
			
			let redHotCacheModel = YLRedHotCacheModel()
			redHotCacheModel.saveKey = String(model.id)
			redHotCacheModel.lastOpenDate = lastOpenDate
			redCacheArray.append(redHotCacheModel)
			YLStatisticsHelper.trackEvent("AD.XDRC.IM", dic: ["ad": "首页活动入口","name": ""])
			buttons.append(button)
		}
		addActivityButtons(activityBtns: buttons)
		YLHomeButtonManager.shared.saveCacheRedHot(datas: redCacheArray,path: "ActivityButton")
		
	}
	
	@objc open func activityButtonDidAction(_ sender: YLRedDotButton) {
		sender.isUserInteractionEnabled = false
		DispatchQueue.main.asyncAfter(deadline: .now() + .milliseconds(500), execute: {
			sender.isUserInteractionEnabled = true
		})
		
		guard let linkURL = sender.linkURL else {
			assertionFailure()
			return
		}
		
		var trackName = ""
		
		if let model = sender.model {
			trackName = model.name
		}
		
		if currentType == .concise {
			YLStatisticsHelper.trackEvent("SimpleHome.CK", dic: ["pt": trackName])
		} else {
			YLStatisticsHelper.trackEvent("Home.Activity.CK", dic: ["name": trackName])
		}
		
		YLStatisticsHelper.trackEventByShushu("ui_click",dic: ["element_path":"首页活动icon-\(trackName)"])
		
		if let model = sender.model,model.cacheDay > 0 {
			let key = "\(currentType)\(String(describing: UserData.shared.pairId))\(String(describing: UserData.shared.userId))\(model.id)"
			let currentTimeStamp = Int(Date().timeIntervalSince1970)
			UserDefaults.standard.set(currentTimeStamp, forKey: key)
			UserDefaults.standard.synchronize()
		}
		
		sender.dotType = .none
		sender.gifUrl = ""
		YLHomeButtonManager.shared.updateRedHot(saveKey: String(sender.model!.id), path: "ActivityButton")
		

		if let model = sender.model ,model.type == 3 {
			//首页探索任务
			let rect = sender.superview?.convert(sender.frame, to: self.view) ?? CGRect.zero
			Navigator.shared.open(linkURL, context: rect)
			YLStatisticsHelper.trackEvent("ExploreIcon.CK")
		}
		else
		{
			Navigator.shared.open(linkURL)
		}
	}
	
	
	// 获取经典版首页活动按钮
	private func getSceneActivityList(completHandler: (([YLHomeActivityModule]?)->Void)?) {

		DispatchQueue.global().async {
			userApi.request(.getActivityButton) { result in
				result.toJSONMapper { json in
					let data = json["data"]
					let moduleArray = YLHomeActivityModule.initArray(data)
					yl_onMain {
						completHandler?(moduleArray)
					}
				} failure: { error in
					yl_onMain {
						completHandler?(nil)
					}
				}
			}
		}
	}
}

//MARK: - UI
extension YLHomeBaseController {
	/* 更新头像 */
	@objc public func updateTopDoubleIcon(topDoubleIconView: UIView, vipFrameView: SDAnimatedImageView, otherVipFrameView: SDAnimatedImageView) {
		func dealIconStyle(view: UIView, conrnerRadus:CGFloat) {
			view.layer.cornerRadius = conrnerRadus
			view.layer.borderWidth = 0.5
			view.layer.borderColor =  #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
		}
		let leftIcon = topDoubleIconView.viewWithTag(TopIconTag.leftIcon.rawValue)! as! UIImageView
		let rightIcon = topDoubleIconView.viewWithTag(TopIconTag.rightIcon.rawValue)! as! UIImageView
		let leftBg = topDoubleIconView.viewWithTag(TopIconTag.leftBg.rawValue)!
		let rightBg = topDoubleIconView.viewWithTag(TopIconTag.rightBg.rawValue)!
		
		if currentType == .room {
			leftIcon.snp.updateConstraints { make in
				make.width.equalTo(38)
			}
			rightIcon.snp.updateConstraints { make in
				make.centerY.equalTo(leftIcon.snp.centerY)
			}
			
			leftBg.yl.layer(radius: leftBg.yl.width/2)
			rightBg.yl.layer(radius: rightBg.yl.width/2)
		}
		
		dealIconStyle(view: leftIcon, conrnerRadus: leftIcon.yl.width/2)
		dealIconStyle(view: rightIcon, conrnerRadus: rightIcon.yl.width/2)
		
		if YLDistanceAlertView.haveShowPrivicy {
			leftIcon.sd_setImage(with: URL(string: UserData.shared.avatar),
								 placeholderImage: UserData.shared.avatarPlaceholderImage,
								 options: [.retryFailed])
			rightIcon.sd_setImage(with: URL(string: UserData.shared.avatarOther),
								  placeholderImage: UserData.shared.avatarOtherPlaceholderImage,
								  options: [.retryFailed])
		}

		let userData = UserData.shared
		if let selfFrame = userData.avatarFrameSelf,!selfFrame.isEmpty {
			vipFrameView.isHidden = false
			vipFrameView.sd_setImage(with: URL(string: selfFrame), placeholderImage: nil, options: [.retryFailed])
			leftBg.isHidden = true
			
		} else {
			vipFrameView.isHidden = true
			leftBg.isHidden = false
		}
		
		if let otherrame = userData.avatarFrameOther,!otherrame.isEmpty {
			otherVipFrameView.isHidden = false
			otherVipFrameView.sd_setImage(with: URL(string: otherrame), placeholderImage: nil, options: [.retryFailed])
			rightBg.isHidden = true
		} else {
			otherVipFrameView.isHidden = true
			rightBg.isHidden = false
		}
		
	}
	
	
	
	/* 在线状态 */
	@objc public func updatePairOnlineStatus() {
		let isOnLine = UserData.shared.pairOnlineStatus
		let onLineImage = isOnLine ? UIImage(named: "home_hd_icon_online") : UIImage(named: "home_hd_icon_offline")
		DispatchQueue.main.async {
			if let onLineImageView = self.onLineView {
				onLineImageView.image = onLineImage
				onLineImageView.isHidden = UserData.shared.isSingle
			}
		}
		
		if UserData.shared.pairOnlineStatus {
			YLSharePlayManager.shared.queryInRoom()
		} else {
			YLSharePlayManager.shared.otherInRoom = false
		}
	}
}

extension YLHomeBaseController {
	// 活动弹框加载完毕
	@objc open func activityDataDidLoadEnd() {}
	
	// 看电影状态发生变化
	@objc open func movieStateDidChanged(_ inRoom: Bool) {}

	// 添加活动按钮UI
	@objc open func addActivityButtons(activityBtns: [YLRedDotButton]) {}
}

//MARK: - 通知回调
extension YLHomeBaseController {
	@objc open func userDataRefresh(_ notification: Notification? = nil) { }
	
	@objc open func updateHeatAndGoldDidChange(_ notification: Notification? = nil) { }
	
	@objc open func refresMatchHeart(_ notification: Notification? = nil) { }
	
	@objc open func diaryRedPointNameDidChange(_ notification: Notification? = nil) { }
	
	@objc open func privityChallengeDidChange(_ notification: Notification? = nil) { }
	
	@objc open func vipStatusDidChange(_ notification: Notification? = nil) { }
	
	@objc open func userGoldDidChange(_ notification: Notification? = nil) { }
	
	@objc open func homeBottomRedDotDidChange(_ notification: Notification? = nil) {}
}

// MARK: - 注册通知
extension YLHomeBaseController {
	
	@objc private func regesterNotifications() {
		NotificationCenter.default.addObserver(self, selector: #selector(userGoldDidChange(_:)), name: .userGoldChangeNotificationName, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(updateHeatAndGoldDidChange(_:)), name: updateHeatAndGoldNotification, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(privityChallengeDidChange(_:)), name: privityChallengeNotification, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(userDataRefresh(_:)), name: userDataRefreshNotification, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(updatePairOnlineStatus), name: .userPairOnlineStatusChangeNotificationName, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(refresMatchHeart(_:)), name: .refresMatchHeartNotification, object: nil)
		
		NotificationCenter.default.addObserver(self, selector: #selector(diaryRedPointNameDidChange(_:)), name: .diaryUpdateRedPointName, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(vipStatusDidChange(_:)), name: .vipStatusChangeNotification, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(onEnterHomePage(_:)), name: Notification.Name("kApplicationDidEnterHomePageNotification") , object: nil)

		NotificationCenter.default.addObserver(self, selector: #selector(homeBottomRedDotDidChange(_:)), name: .kHomeBottomRedDotNotificationName, object: nil)
		NotificationCenter.default.addObserver(self, selector: #selector(homeBottomRedDotDidChange(_:)), name: .kHomeBottomReOrderNotificationName, object: nil)
		
		NotificationCenter.default.addObserver(self, selector: #selector(self.jumpToMatchVC), name: breakMatchNotification, object: nil)
		
		if let appdelegate = AppDelegate.shared, appdelegate.adManager.enteredHomePage {
			onEnterHomePage()
			haveLoad = true
		}
	}
	
	
	@objc open func onEnterHomePage(_ notification: Notification? = nil) {
		
		if (currentType == .scene || currentType == .single), !UserData.shared.haveShowFirstMatchGuide || !YLHomeViewController.simpleGuideShowed {
			return
		}
		
		if haveLoad {
			haveLoad = false
			return
		}
		
		let state = showVIPAlertIfNeeded()
		if !state {
			startRequestActivityData()
		}
	}
	
	@objc public func jumpToMatchVC() {

		if let homeViewController = YLHomeViewController.shared {
			NSObject.cancelPreviousPerformRequests(withTarget: homeViewController)
		}
		
		if UserData.shared.matchStatus == .waitExport {
			return
		}
		YLPositiveReviewPopShowManager.shared.loadCSHData = false
		YLSharePlayManager.shared.leaveRoom()
		UserData.shared.deleteDataDiskWithVip()
		YLSharePlayManager.shared.otherInRoom = false
		UserData.shared.resetInfo()
		YLChatTalkInitManager.shared.clear()
		YLPageBannerTool.clearData()
		YLLocoKitManager.shared.clearData(isDelete: true)
		MNPlayer.shared.close()
		YLWidgetDeskPhotoData.shared.cleaInfo()

		UserData.shared.matchStatus = .waitExport
		UserData.shared.matchType = 4
		UserData.shared.getCurrentInfo { [weak self](result) in
			self?.navigationController?.popToRootViewController(animated: false)
			AppDelegate.shared?.chooseRootVCWith(userData: UserData.shared)
		}
	}
	
	@discardableResult
	func showVIPAlertIfNeeded() -> Bool {
		let user = UserData.shared
		guard let topViewController = topViewController else { return false }
		/// 展示VIP即将到期弹框
		func showVipAboutToExpireAlert() {
			YLVIPExpiredAlertViewController.show(from: topViewController, type: .aboutToExpire) {
				user.reportVIPAlertPoped(.aboutToExpire)
				YLStatisticsHelper.trackEvent("VIP.TemporaryPopup.IM")
			} confirmHandler: {
				Navigator.shared.open(Navigator.vipPurchase + "?lang=\(YLLocalizationManager.shared.ylLocalizedStringC("YL_webLanguage"))&source=临期弹窗", context: ["dismissHandler" : { [weak self] in
					self?.startRequestActivityData()
				}])
				YLStatisticsHelper.trackEvent("VIP.TemporaryPopup.Buy.CK")
			} dismissHandler: {
				self.startRequestActivityData()
			}
			
		}
		if user.vipExpirePop {//会员已过期
			YLVIPExpiredAlertViewController.show(from: topViewController, type: .expired) {
				user.reportVIPAlertPoped(.expired)
				YLStatisticsHelper.trackEvent("VIP.ExpirePopup.IM")
			} confirmHandler: {
				Navigator.shared.open(Navigator.vipPurchase + "?lang=\(YLLocalizationManager.shared.ylLocalizedStringC("YL_webLanguage"))&source=过期弹窗", context: ["dismissHandler" : { [weak self] in
					self?.startRequestActivityData()
				}])
				YLStatisticsHelper.trackEvent("VIP.ExpirePopup.Buy.CK")
			} dismissHandler: {
				self.startRequestActivityData()
			}
			
		} else if user.vipNearExpirePop && user.isVip {//会员即将过期
			showVipAboutToExpireAlert()
		}
		else if user.isVip && (user.vipLevel < 6 || user.vipInfo.vipType == .monthType || user.vipInfo.vipType == .payMonthType || user.vipInfo.vipType == .seasonType){
			self.startRequestActivityData()
		}else {
			return false
		}
		return true
	}
	
	
	func statisticAPNS() {
		let center = UNUserNotificationCenter.current()
		center.getNotificationSettings { settings in
			switch settings.authorizationStatus {
			case .authorized:
				YLStatisticsHelper.trackEventOnlyOnce("Home.Notice.WhetherToOpen", dic: ["state": "已开启"])
			case .denied:
				YLStatisticsHelper.trackEventOnlyOnce("Home.Notice.WhetherToOpen", dic: ["state": "已关闭"])
			case .notDetermined:
				YLStatisticsHelper.trackEventOnlyOnce("Home.Notice.WhetherToOpen", dic: ["state": "未选择"])
			default:
				break
			}
		}
		
	}
}


//MARK: - 弹框相关
extension YLHomeBaseController {
	
	static var lastShowMovieAlertVC = ""
	// 看电影弹框
	func showMovieAlertViewIfNeed(complet:(()->Void)?) {
		guard let topVC = UIApplication.ylTopMost() else {
			complet?()
			return
		}
		let vcStr = String(describing: type(of: topVC))
		if vcStr == Self.lastShowMovieAlertVC && vcStr != "YLMovieChatViewController" {
			complet?()
			return
		}
		Self.lastShowMovieAlertVC = vcStr
		
		let isMovieOpen = YLMovieChatViewController.shared?.isMovieMode
		let showMovieAlertViewIfNeed = UserDefaults.standard.string(forKey: "kShowMovieAlertViewIfNeed")
		
		if isMovieOpen != true && !YLHomeMovieAlertView.isShowing && showMovieAlertViewIfNeed == "1" {
			let isChatOpen = YLMovieChatViewController.shared != nil
			let alertView = YLHomeMovieAlertView()
			topWindow?.addSubview(alertView)
			alertView.snp.makeConstraints { make in
				make.edges.equalToSuperview()
			}
			
			alertView.okClosure = {
				Self.goMovie()
				complet?()
			}
			
			alertView.imClosure = {
				if isChatOpen {
					YLStatisticsHelper.trackEvent("Chat.MoviePopup.IM")
				}
			}
			
			alertView.cancelClosure = {
				complet?()
			}
			YLHomeMovieAlertView.isShowing = true
			alertView.showFunc()
		} else {
			complet?()
		}
	}
	
	static func goMovie() {
		
		func pushVC(vc:UIViewController)
		{
			if let topViewController = topViewController as? UINavigationController {
				topViewController.pushViewController(vc, animated: true)
			} else {
				topViewController?.navigationController?.pushViewController(vc, animated: true)
			}
		}
		
		let vc = YLMovieChatViewController()
		vc.shouldEnterMovieMode = true
		pushVC(vc: vc)
		
	}
	
	
	static let kHomeShowPraise = "kHomeShowPraise"
	/// 系统评分弹窗
	func showPraise() {
		let show = UserDefaults.standard.bool(forKey: Self.kHomeShowPraise)
		if show == false {
			YLAlerViewManager.shared.addAlertQueueHandlers { taskOperation,isHome  in
				//系统评分
				YLPraiseViewController.show(from: self) { isFeecback in
					taskOperation?.endOperation()
					if isFeecback {
						Navigator.shared.open(Navigator.feedback)
					}
				}
			}
			UserDefaults.standard.setValue(true, forKey: Self.kHomeShowPraise)
		}
	}
}
//MARK: 冷启动上报用户属性
extension YLHomeBaseController {
	
	func reportUserSet() async {
		let locaM = YLLocationManager.shared
		let locationStr: String
		if locaM.isAuthorizedAlways {
			locationStr = "始终共享"
		} else if locaM.isAuthorizedWhenInUse {
			locationStr = "打开APP更新"
		} else {
			locationStr = "关闭"
		}
		YLStatisticsHelper.trackUserSetByShushu(dic: ["permission_location": locationStr])
		
		
		let settings = await UNUserNotificationCenter.current().notificationSettings()
		let notificationStr: String
		switch settings.authorizationStatus {
		case .notDetermined, .denied:
			notificationStr = "关闭"
		case .authorized, .provisional, .ephemeral:
			notificationStr = "开启"
		}
		YLStatisticsHelper.trackUserSetByShushu(dic: ["permission_notification": notificationStr])
	}
}
