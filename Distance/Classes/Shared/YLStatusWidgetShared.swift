 import Foundation
import WidgetKit

public struct YLWidgetStatus: Codable {
    public let emoji: String
    public let text: String?
    public let timestamp: TimeInterval
}

public final class YLStatusWidgetShared {
    public static let shared = YLStatusWidgetShared()
    private let suite = UserDefaults(suiteName: "group.distance.widget")
    private let mineKey  = "kStatusWidgetMine"
    private let peerKey  = "kStatusWidgetPeer"

    private init() {}

    public func update(mine: YLWidgetStatus?, peer: YLWidgetStatus?) {
        let encoder = JSONEncoder()
        if let mine {
            suite?.set(try? encoder.encode(mine), forKey: mineKey)
        } else {
            suite?.removeObject(forKey: mineKey)
        }
        if let peer {
            suite?.set(try? encoder.encode(peer), forKey: peerKey)
        } else {
            suite?.removeObject(forKey: peerKey)
        }
        suite?.synchronize()
        if #available(iOS 14.0, *) {
            WidgetCenter.shared.reloadTimelines(ofKind: "YLStatusWidget")
        }
    }

    public func load() -> (mine: YLWidgetStatus?, peer: YLWidgetStatus?) {
        let decoder = JSONDecoder()
        var mineStatus: YLWidgetStatus? = nil
        var peerStatus: YLWidgetStatus? = nil
        if let data = suite?.data(forKey: mineKey) {
            mineStatus = try? decoder.decode(YLWidgetStatus.self, from: data)
        }
        if let data = suite?.data(forKey: peerKey) {
            peerStatus = try? decoder.decode(YLWidgetStatus.self, from: data)
        }
        return (mineStatus, peerStatus)
    }
}
