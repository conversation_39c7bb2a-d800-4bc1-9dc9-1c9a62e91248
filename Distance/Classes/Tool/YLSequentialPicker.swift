//
//  YLSequentialPicker.swift
//  Distance
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/5.
//

import Foundation

/// 按照顺序 从当前数组中取出对应的Item，
/// 使用示例：
/// let malepPicker = YLSequentialPicker(items: ["tx_boy", "tx_badge1", "tx_badge2"])
/// let item = malepPicker.next() ?? ""
class YLSequentialPicker<T> {
	private var items: [T]
	private var currentIndex = 0

	init(items: [T]) {
		self.items = items
	}

	func next() -> T? {
		guard !items.isEmpty else { return nil }
		let value = items[currentIndex]
		currentIndex = (currentIndex + 1) % items.count
		return value
	}

	func reset() {
		currentIndex = 0
	}
}
