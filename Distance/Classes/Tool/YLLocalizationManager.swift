//
//  YLLocalizationManager.swift
//  Distance
//
//  Created by Q on 2024/8/28.
//

import UIKit
import Foundation

extension Bundle {
	
	private static let languageMapping: [String: String] = [
		"zh": "zh-Hant",
		"pt": "pt-PT"
	]
	
	static func setLanguage(_ language: String) {

		let languageComponents = language.components(separatedBy: "-")
		let baseLanguage = languageComponents.first ?? language
		let simplifiedLanguage = languageComponents.prefix(2).joined(separator: "-")

		var candidates = [language] // 1. 完整语言代码

		if let mappedLanguage = languageMapping[baseLanguage] {
			candidates.append(mappedLanguage) // 2. 映射后的语言代码
		}
		candidates.append(simplifiedLanguage) // 3. 简化的语言代码 (例如 zh-Hans)
		candidates.append(baseLanguage) // 4. 基本语言代码 (例如 zh)

		for langCode in candidates {
			if let path = Bundle.main.path(forResource: langCode, ofType: "lproj"),
			   let bundle = Bundle(path: path) {
				customLanguageBundle = bundle
				return
			}
		}
		// 如果所有候选都失败，使用默认英语
		customLanguageBundle = Bundle.enMorLocalizedBundle()
	}
	
	static func enMorLocalizedBundle(_ key: String = "en") -> Bundle {
		if let bundleTuple = cusLanguageBundleTuple, bundleTuple.key == key {
			return bundleTuple.bundle
		}
		if let path = Bundle.main.path(forResource: key, ofType: "lproj"),
		   let bundle = Bundle(path: path) {
			cusLanguageBundleTuple = (bundle:bundle, key:key)
			return bundle
		}
		let languageComponents = key.components(separatedBy: "-")
		let baseLanguage = languageComponents.first ?? key
		if let mappedLanguage = languageMapping[baseLanguage] {
			if let path = Bundle.main.path(forResource: mappedLanguage, ofType: "lproj"),
			   let bundle = Bundle(path: path) {
				cusLanguageBundleTuple = (bundle: bundle, key:key)
				return Bundle.main
			}
		}
		cusLanguageBundleTuple = (bundle: Bundle.main, key:"")
		return Bundle.main
		
	}
	private static var cusLanguageBundleTuple: (bundle: Bundle,key: String)?
	
	private static var customLanguageBundle: Bundle?
	
	static func localizedBundle() -> Bundle {
		return customLanguageBundle ?? Bundle.main
	}
	
	func localizedStringYL(forKey key: String, value: String?, table tableName: String?, isSelf: Bool) -> String {
		var valueString = ""
		if isSelf {
			valueString = Bundle.localizedBundle().localizedString(forKey: key, value: value, table: tableName)
		}else {
			let otherKey = UserData.shared.langOther
			valueString = Bundle.enMorLocalizedBundle(otherKey).localizedString(forKey: key, value: value, table: tableName)
		}
		if valueString == key {
			return Bundle.enMorLocalizedBundle().localizedString(forKey: key, value: value, table: tableName)
		}
		return valueString
	}
}

@objc class YLLocalizationManager: NSObject {
	
	@objc static let shared = YLLocalizationManager()
	
	private let selectedLanguageKey = "YLSelectedLanguageKey"
	
	private var cachedLanguage: String?
	
	var currentLanguage: String {
		get {
			if let cached = cachedLanguage {
				return cached
			}
			let language = UserDefaults.standard.string(forKey: selectedLanguageKey) ?? Locale.preferredLanguages.first ?? "en"
			cachedLanguage = language
			return language
		}
		set {
			cachedLanguage = nil
			
			// 如果 newValue 为空或为 nil，删除保存的语言设置
			guard !newValue.isEmpty else {
				Bundle.setLanguage(Locale.preferredLanguages.first ?? "en")
				UserDefaults.standard.removeObject(forKey: selectedLanguageKey)
				return
			}
					
			// 保存新语言
			Bundle.setLanguage(newValue)
			UserDefaults.standard.set(newValue, forKey: selectedLanguageKey)
		}
	}
	
	@objc public override init() {
		super.init()
		// 初始化时设置当前语言
		let language = currentLanguage
		Bundle.setLanguage(language)
	}
	
	func defLanguage() -> Bool {
		return (UserDefaults.standard.string(forKey: selectedLanguageKey) ?? "").isEmpty
	}
	
	@objc func ylLocalizedStringC(_ stringKey: String,isSelf: Bool = true) -> String {
		let result = Bundle.main.localizedStringYL(forKey: stringKey, value: nil, table: nil,isSelf: isSelf)
		if result.isEmpty {
			return stringKey
		}
		return result
	}
}

extension String {
	var localized: String {
		return YLLocalizationManager.shared.ylLocalizedStringC(self)
	}
	var localizedWithOther: String {
		return YLLocalizationManager.shared.ylLocalizedStringC(self,isSelf: false)
	}
	//图片
	var ylLocalizedImageStr: String {
		return self + "_" + YLLocalizationManager.shared.ylLocalizedStringC("YL_allImageLanguage")
	}
}
public func localizedString(
	key: String,
	substitutions: [String: String] = [:],
	isSelf: Bool = true
) -> String {
	let localized = YLLocalizationManager.shared.ylLocalizedStringC(key,isSelf:isSelf)
	return substitute(localized, with: substitutions)
}

// Performs simple string interpolation on keys of the form `%{key}`.
private func substitute(_ string: String, with substitutions: [String: String]) -> String {
	return substitutions.reduce(string) { accum, sub in
		accum.replacingOccurrences(of: "%\(sub.0)s", with: sub.1)

	}
}
