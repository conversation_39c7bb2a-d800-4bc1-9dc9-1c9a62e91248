//
//  YLLogTool.swift
//  Distance
//
//  Created by yang li on 2021/5/25.
//

import Foundation
import CocoaLumberjack
import QCloudCOSXML

class YLLogTool {
	
	static func config() {
		DDLog.add(DDOSLogger.sharedInstance)
		if let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first {
			let fullPath = path.appending("/Logs")
			let fileManager = DDLogFileManagerDefault(logsDirectory: fullPath)
			let fileLogger = DDFileLogger(logFileManager: fileManager)
			fileLogger.maximumFileSize = kDDDefaultLogMaxFileSize * 10
			let dateFormatter = DateFormatter()
			dateFormatter.dateFormat = "yyyy/MM/dd HH:mm:ss:SSS"
			let logFormatter = DDLogFileFormatterDefault(dateFormatter: dateFormatter)
			fileLogger.logFormatter = logFormatter
			fileLogger.rollingFrequency = 60 * 60 * 24 * 3
			fileLogger.logFileManager.maximumNumberOfLogFiles = 7
			var logLevel: DDLogLevel
			#if DEBUG
			DDLog.add(DDOSLogger.sharedInstance)
			logLevel = .all
			#else
			logLevel = .info
			#endif
			
			DDLog.add(fileLogger, with: logLevel)
		} else {
			assertionFailure()
		}
	}
	
	static func logError(_ message: String) {
		let makeMessage = "\n" + message + Thread.callStackSymbols.joined(separator: "\n")
		DDLogError(makeMessage)
	}
	
	@discardableResult static func uploadErrorFile(completion: ((String?, Bool) -> Void)? = nil) -> Bool {
		let fileManager = FileManager()
		guard let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first else {
			assertionFailure()
			return false
		}
		let fullPath = path.appending("/Logs")
		if let fileArray = fileManager.subpaths(atPath: fullPath) {
			if fileArray.isEmpty {
				return false
			}
			var fileCount = 0
			var isCallBack: Bool = false
			for filePath in fileArray {
				uploadFile(fullPath.appending("/\(filePath)"), completionHandler: { path, value in
					if value == false {
						isCallBack = true
					}
					fileCount += 1
					if fileCount == fileArray.count {
						if isCallBack == false {
							completion?(path, true)
						} else {
							completion?(path, false)
						}
					}
				})
			}
		} else {
			return false
		}
		return true
	}
	
	static func uploadFile(_ path: String, completionHandler: @escaping (String?, Bool) -> Void) {
		qCloudApi.request(.getToken, completion: { result in
			result.toJSONMapper(success: { json in
				
				
				let data = json["data"]
				
				guard let token = data["token"].string,
					  let host = data["host"].string
				else {
					completionHandler(nil, false)
					return
				}
				
				
				let fileURL = URL(fileURLWithPath: path)
				let openID: String = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
				YLQCloudApi.uploadManger.putFile(path, key: "couple2/closer_feedback/\(openID)/\(fileURL.lastPathComponent)", token: token) { info, key, resp in
					guard let qnPath = resp?["key"] as? String else {
						if let key = key {
							completionHandler(host + "/" + key, false)
						} else {
							completionHandler(nil, false)
						}
						
						return
					}
					completionHandler(host + "/" + qnPath, true)
					try? FileManager.default.removeItem(at: fileURL)
				}
				
			}, failure: { error in
				completionHandler(nil, false)
			})
		})
	}
	
}
