//
//  YLImageUploadTool.swift
//  Distance
//
//  Created by ke wen on 2020/11/11.
//

import Foundation
import QCloudCOSXML
import AVFoundation
import Photos
import CocoaLumberjack

class YLImageUploadTool: NSObject {
    
    static let `default` = YLImageUploadTool()
    
	typealias GetImgUrlComplection = ((Result<(imgUrl: String, image: UIImage, data: Data), YLError>) -> Void)
    
    private var getComplectionBlock: GetImgUrlComplection?
	
	private var getCropImageBlock: ((UIImage?)->Void)?
	
	var token: String?
	
	var host: String?
    
    override init() {
        
    }
    
    func chooseImgAndGetImgUrl(completion: @escaping GetImgUrlComplection) {
        getComplectionBlock = completion
        let photoPickerViewController = UIImagePickerController()
        photoPickerViewController.sourceType = .photoLibrary
        photoPickerViewController.delegate = self
        photoPickerViewController.allowsEditing = true
        topViewController?.present(photoPickerViewController, animated: true, completion: nil)
    }
	
	func chooseCropImg(completion: @escaping ((UIImage?)->Void)) -> Void {
		getCropImageBlock = completion
		let photoPickerViewController = UIImagePickerController()
		photoPickerViewController.sourceType = .photoLibrary
		photoPickerViewController.delegate = self
		photoPickerViewController.allowsEditing = true
		topViewController?.present(photoPickerViewController, animated: true, completion: nil)
	}
	
	func checkImage(_ url: String, completion: ((Result<Bool, YLError>) -> Void)? = nil) {
		qCloudApi.request(.checkImage(url: url)) { result in
			result.toJSONMapper { json in
				let data = json["data"].boolValue
				completion?(.success(data))
			} failure: { error in
				completion?(.failure(error))
			}
		}
	}
		
	func uploadImage(image: UIImage, quality: CGFloat = 1.0, haveAlpha: Bool = false, completion: @escaping GetImgUrlComplection) {
		YLHUDView.showLoading(view: topViewController?.view)
		let openID: String = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
		let uuidString = UUID().uuidString.replacingOccurrences(of: "-", with: "").lowercased()
		let uniqueKey = "couple2/\(openID)/\(uuidString)"
		
		self.getToken { result in
			switch result {
			case let .success((token, host)):
				let imageData = haveAlpha ? image.pngData() ?? Data() : image.jpegData(compressionQuality: quality) ?? Data()
				YLQCloudApi.uploadManger.put(imageData, key: uniqueKey, token: token) { [weak self] info, key, resp in
					guard let key = resp?["key"] as? String else {
						DDLogInfo("【七牛上传image】key为空\(String(describing: resp))\ntoken:\(token)\nhost:\(host)")
						self?.getComplectionBlock?(.failure(.invalideStatusCode(statusCode: -1, message: "上传图片失败")))
						completion(.failure(.invalideStatusCode(statusCode: -1, message: "上传图片失败")))
						return
					}
					completion(.success((host + "/" + key, image, imageData)))
					YLHUDView.dismiss(view: topViewController?.view)
				}
			case let .failure(error):
				YLHUDView.dismiss(view: topViewController?.view)
				completion(.failure(error))
				DDLogInfo("【七牛上传image】错误:\(error.errorDescription)")
			}
		}
	}
	
	func uploadAsset(asset: PHAsset, key: String? = nil, progressHandler:((String?, Float)->Void)? = nil, completion: ((Result<String, YLError>) -> Void)? = nil) {
		let uniqueKey: String = key ?? {
			let openID = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
			let uuid = UUID().uuidString.replacingOccurrences(of: "-", with: "").lowercased()
			return "couple2/\(openID)/\(uuid)"
		}()

		self.getToken { result in
			switch result {
			case let .success((token, host)):
				YLQCloudApi.uploadManger.put(asset: asset, key: uniqueKey, token: token) { info, key, resp in
					guard let key = resp?["key"] as? String else {
						DDLogInfo("【七牛上传asset】key为空\(String(describing: resp))\ntoken:\(token)\nhost:\(host)")
						completion?(.failure(.invalideStatusCode(statusCode: -1, message: "上传图片失败")))
						return
					}
					completion?(.success(host + "/" + key))
				}
			case let .failure(error):
				completion?(.failure(error))
				DDLogInfo("【七牛上传asset】错误:\(error.errorDescription)")
			}
		}
	}
	
	func uploadFile(filePath: String, key: String? = nil, progressHandler:((String?, Float)->Void)? = nil, completion: ((Result<String, YLError>) -> Void)? = nil) {
		let uniqueKey: String = key ?? {
			let openID = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
			let uuid = UUID().uuidString.replacingOccurrences(of: "-", with: "").lowercased()
			return "couple2/\(openID)/\(uuid)"
		}()
		self.getToken { result in
			switch result {
			case let .success((token, host)):
				YLQCloudApi.uploadManger.putFile(filePath, key: uniqueKey, token: token) { info, key, resp in
					guard let key = resp?["key"] as? String else {
						DDLogInfo("【七牛上传filePath】key为空\(String(describing: resp))\ntoken:\(token)\nhost:\(host)")
						completion?(.failure(.invalideStatusCode(statusCode: -1, message: "上传图片失败")))
						return
					}
					completion?(.success(host + "/" + key))
				}
			case let .failure(error):
				completion?(.failure(error))
				DDLogInfo("【七牛上传filePath】错误:\(error.errorDescription)")
			}
		}
	}
	
	func uploadImage(imageData: Data, loadingView: UIView? = nil,progressHandler:((String?, Float)->Void)? = nil, completion: @escaping GetImgUrlComplection) {
		if let loadingView = loadingView {
			YLHUDView.showLoading(view: loadingView)
		}
		
		let openID: String = UserData.shared.userId.isEmpty ? WCRequestPublicParameters.openUDID : UserData.shared.userId
		let uuidString = UUID().uuidString.replacingOccurrences(of: "-", with: "").lowercased()
		let uniqueKey = "couple2/\(openID)/\(uuidString)"
		
		self.getToken { result in
			switch result {
			case let .success((token, host)):
				YLQCloudApi.uploadManger.put(imageData, key: uniqueKey, token: token) { [weak self] info, key, resp in
					
					if let loadingView = loadingView {
						YLHUDView.dismiss(view: loadingView)
					}
					
					guard let key = resp?["key"] as? String else {
						DDLogInfo("【七牛上传imageData】key为空\(String(describing: resp))\ntoken:\(token)\nhost:\(host)")
						self?.getComplectionBlock?(.failure(.invalideStatusCode(statusCode: -1, message: "上传图片失败")))
						return
					}
					completion(.success((host + "/" + key, UIImage(), imageData)))
					
				}
			case let .failure(error):
				YLHUDView.dismiss(view: loadingView)
				completion(.failure(error))	
				DDLogInfo("【七牛上传imageData】错误:\(error.errorDescription)")
			}
		}
	}
	
	func getToken(completion: ((Result<(String, String), YLError>) -> Void)? = nil) {
//		qCloudApi.request(.getToken, completion: { result in
		//改为低频
		qCloudApi.request(.getTokenLowFrequency, completion: { result in
			result.toJSONMapper(success: { json in
				let data = json["data"]
				guard let token = data["token"].string,
					  let host = data["host"].string
				else {
					completion?(.failure(.invalideStatusCode(statusCode: -1, message: "LoadError")))
					return
				}
				
				completion?(.success((token, host)))
			}, failure: { error in
				completion?(.failure(error))
			})
		})
	}
	
}
extension YLImageUploadTool: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        
        if let selectedImage = info[UIImagePickerController.InfoKey.editedImage] as? UIImage {
			if let _ = getCropImageBlock {
				picker.dismiss(animated: true) { [weak self] in
					self?.getCropImageBlock?(selectedImage)
					self?.getCropImageBlock = nil
				}
				return
			}
			uploadImage(image: selectedImage,  completion: { [weak self] result in
				picker.dismiss(animated: true, completion: nil)
				self?.getComplectionBlock?(result)
			})
		} else {
			if let _ = getCropImageBlock {
				picker.dismiss(animated: true) { [weak self] in
					self?.getCropImageBlock?(nil)
					self?.getCropImageBlock = nil
				}
				return
			}
		}
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
    }
}
