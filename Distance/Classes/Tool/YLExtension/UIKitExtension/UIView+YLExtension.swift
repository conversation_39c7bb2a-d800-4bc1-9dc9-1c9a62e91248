//
//  UIView+YLExtension.swift
//  Distance
//
//  Created by lxy on 2022/1/13.
//

import Foundation

// MARK: - UIView
extension UIView: YLNameSpaceCompatible { }
extension YLNameSpace where Base: UIView {
	
	var x:CGFloat {
		get {
			return self.base.center.x
		}
		set {
			var center = self.base.center
			center.x = newValue
			self.base.center = center
		}
	}
	
	var y:CGFloat {
		get {
			return self.base.center.y
		}
		set {
			var center = self.base.center
			center.y = newValue
			self.base.center = center
		}
	}
	
	var origin:CGPoint {
		get {
			return self.base.frame.origin
		}
		set {
			var rect = self.base.frame
			rect.origin = newValue
			self.base.frame = rect
		}
	}
	var size:CGSize {
		get {
			return self.base.frame.size
		}
		set {
			var rect = self.base.frame
			rect.size = newValue
			self.base.frame = rect
		}
	}
	
	var left:CGFloat {
		get {
			return self.base.frame.origin.x
		}
		set {
			var rect = self.base.frame
			rect.origin.x = newValue
			self.base.frame = rect
		}
	}
	
	var top:CGFloat {
		get {
			return self.base.frame.origin.y
		}
		set {
			var rect = self.base.frame
			rect.origin.y = newValue
			self.base.frame = rect
		}
	}
	
	var right:CGFloat {
		get {
			return (self.base.frame.origin.x + self.base.frame.size.width)
		}
		set {
			var rect = self.base.frame
			rect.origin.x = (newValue - self.base.frame.size.width)
			self.base.frame = rect
		}
	}
	
	var bottom:CGFloat {
		get {
			return (self.base.frame.origin.y + self.base.frame.size.height)
		}
		set {
			var rect = self.base.frame
			rect.origin.y = (newValue - self.base.frame.size.height)
			self.base.frame = rect
		}
	}
	
	var height: CGFloat {
		get {
			return base.frame.size.height
		}
		set(newValue) {
			var tempFrame: CGRect = base.frame
			tempFrame.size.height = newValue
			base.frame = tempFrame
		}
	}
	
	var width: CGFloat {
		get {
			return base.frame.size.width
		}
		set(newValue) {
			var tempFrame: CGRect = base.frame
			tempFrame.size.width = newValue
			base.frame = tempFrame
		}
	}
	
	var centerX: CGFloat {
	
		get {
			return base.center.x
		}
		
		set(newValue) {
			base.center = CGPoint(x: newValue, y: base.center.y)
		}
	}
	
	var centerY: CGFloat {
	
		get {
			return base.center.y
		}
		
		set(newValue) {
			base.center = CGPoint(x: base.center.x, y: newValue)
		}
	}
	
	// MARK: 毛玻璃效果
	
	// 毛玻璃
	func effectViewWithAlpha(alpha:CGFloat) -> Void
	{
		let effect = UIBlurEffect.init(style: UIBlurEffect.Style.light)
		let effectView = UIVisualEffectView.init(effect: effect)
		effectView.frame = self.base.bounds
		effectView.alpha = alpha
		
		self.base.addSubview(effectView)
	}
	
	// MARK: 边框属性
	
	/// 添加圆角
	/// - Parameters:
	///   - conrners: 具体哪个圆角
	///   - radius: 圆角的大小
	func addCorner(conrners: UIRectCorner = UIRectCorner.allCorners , radius: CGFloat) {
		let maskPath = UIBezierPath(roundedRect: base.bounds, byRoundingCorners: conrners, cornerRadii: CGSize(width: radius, height: radius))
		let maskLayer = CAShapeLayer()
		maskLayer.frame = base.bounds
		maskLayer.path = maskPath.cgPath
		base.layer.mask = maskLayer
	}
	
	/// 添加边框
	/// - Parameters:
	///   - width: 边框宽度
	///   - color: 边框颜色
	func addBorder(borderWidth: CGFloat, borderColor: UIColor) {
		if (0.0 < borderWidth)
		{
			base.layer.borderColor = borderColor.cgColor
			base.layer.borderWidth = borderWidth
			base.layer.masksToBounds = true
		}
	}
	
	/// 添加圆角边框
	/// - Parameters:
	///   - corners: 圆角位置
	///   - radius: 圆角大小
	///   - borderWidth: 边框宽度
	///   - borderColor: 边框颜色
	func layer(corners: UIRectCorner = .allCorners, radius: CGFloat = 0, borderWidth: CGFloat = 0, borderColor: UIColor = .clear) {
		// 清除旧的自定义边框和遮罩
		base.layer.sublayers?.removeAll(where: { $0.name == "CornerBorderLayer" })
		if let _ = base.layer.mask as? CAShapeLayer {
			base.layer.mask = nil
		}
		// 情况一：全圆角或边框
		if corners == .allCorners {
			if (radius >= 0.0) {
				self.base.layer.cornerRadius = radius
				self.base.layer.masksToBounds = true
				self.base.clipsToBounds = true
			}
			
			if (borderWidth >= 0.0) {
				self.base.layer.borderColor = borderColor.cgColor
				self.base.layer.borderWidth = borderWidth
				self.base.layer.masksToBounds = true
			}
			return
		}

		// 情况二：部分圆角，自定义 path 和遮罩
		let path = UIBezierPath(roundedRect: base.bounds, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))

		// 添加遮罩
		let maskLayer = CAShapeLayer()
		maskLayer.name = "CornerMaskLayer"
		maskLayer.frame = base.bounds
		maskLayer.path = path.cgPath
		base.layer.mask = maskLayer

		// 添加边框（可选）
		if borderWidth > 0 && borderColor != .clear {
			let borderLayer = CAShapeLayer()
			borderLayer.name = "CornerBorderLayer"
			borderLayer.frame = base.bounds
			borderLayer.path = path.cgPath
			borderLayer.strokeColor = borderColor.cgColor
			borderLayer.fillColor = UIColor.clear.cgColor
			borderLayer.lineWidth = borderWidth
			base.layer.addSublayer(borderLayer)
		}
	}
	
	func swiftDrawBoardDottedLine(width:CGFloat,lenth:CGFloat,space:CGFloat,cornerRadius:CGFloat,color:UIColor){
		base.layer.cornerRadius = cornerRadius
		let borderLayer =  CAShapeLayer()
		borderLayer.bounds = base.bounds
		
		borderLayer.position = CGPoint(x: base.bounds.midX, y: base.bounds.midY);
		borderLayer.path = UIBezierPath(roundedRect: borderLayer.bounds, cornerRadius: cornerRadius).cgPath
		borderLayer.lineWidth = width / UIScreen.main.scale
		
		//虚线边框---小边框的长度
		
		borderLayer.lineDashPattern = [lenth,space] as? [NSNumber] //前边是虚线的长度，后边是虚线之间空隙的长度
		borderLayer.lineDashPhase = 0.1;
		//实线边框
		
		borderLayer.fillColor = UIColor.clear.cgColor
		borderLayer.strokeColor = color.cgColor
		base.layer.addSublayer(borderLayer)
	}
	
}


// MARK: - 颜色渐变
public enum YLViewGradientDirection {
	/// 水平从左到右
	case horizontal
	///  垂直从上到下
	case vertical
	/// 左上到右下
	case leftOblique
	/// 右上到左下
	case rightOblique
	/// 其他情况.
	case other(CGPoint, CGPoint)
	
	public func point() -> (CGPoint, CGPoint) {
		switch self {
		case .horizontal:
			return (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0))
		case .vertical:
			return (CGPoint(x: 0, y: 0), CGPoint(x: 0, y: 1))
		case .leftOblique:
			return (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 1))
		case .rightOblique:
			return (CGPoint(x: 1, y: 0), CGPoint(x: 0, y: 1))
		case .other(let stat, let end):
			return (stat, end)
		}
	}
}

public extension YLNameSpace where Base: CAGradientLayer {
	// MARK: 设置渐变色图层
	/// 设置渐变色图层
	/// - Parameters:
	///   - direction: 渐变方向
	///   - gradientColors: 渐变的颜色数组（颜色的数组）
	///   - gradientLocations: 设置渐变颜色的终止位置，这些值必须是递增的，数组的长度和 colors 的长度最好一致
	func gradientLayer(_ direction: YLViewGradientDirection = .horizontal, _ gradientColors: [Any], _ gradientLocations: [NSNumber]? = nil) -> CAGradientLayer {
	   
		// 设置渐变的颜色数组
		self.base.colors = gradientColors
		// 设置渐变颜色的终止位置，这些值必须是递增的，数组的长度和 colors 的长度最好一致
		self.base.locations = gradientLocations
		// 设置渲染的起始结束位置（渐变方向设置）
		self.base.startPoint = direction.point().0
		self.base.endPoint = direction.point().1
		
		return self.base
	}
}

public extension YLNameSpace where Base : UIView {

	// MARK: 添加渐变色图层
	/// 添加渐变色图层
	/// - Parameters:
	///   - direction: 渐变方向
	///   - gradientColors: 渐变的颜色数组（颜色的数组是）
	///   - gradientLocations: 决定每个渐变颜色的终止位置，这些值必须是递增的，数组的长度和 colors 的长度最好一致
	func gradientColor(_ direction: YLViewGradientDirection = .horizontal, _ gradientColors: [Any], _ gradientLocations: [NSNumber]? = nil) {
		// 获取渐变对象
		let gradientLayer = CAGradientLayer().yl.gradientLayer(direction, gradientColors, gradientLocations)
		// 设置其CAGradientLayer对象的frame，并插入view的layer
		gradientLayer.frame = CGRect(x: 0, y: 0, width: self.base.yl.width, height: self.base.yl.height)
		self.base.layer.insertSublayer(gradientLayer, at: 0)
	}

	// MARK: 10.2、colors 变化渐变动画
	/// colors 变化渐变动画
	/// - Parameters:
	///   - direction: 渐变方向
	///   - startGradientColors: 开始渐变的颜色数组
	///   - endGradientColors: 结束渐变的颜色数组
	///   - gradientLocations: 决定每个渐变颜色的终止位置，这些值必须是递增的，数组的长度和 colors 的长度最好一致
	func gradientColorAnimation(direction: YLViewGradientDirection = .horizontal, startGradientColors: [Any], endGradientColors: [Any], duration: CFTimeInterval = 1.0, gradientLocations: [NSNumber]? = nil) {
		// 获取渐变对象
		let gradientLayer = CAGradientLayer().yl.gradientLayer(direction, startGradientColors, gradientLocations)
		// 设置其CAGradientLayer对象的frame，并插入view的layer
		gradientLayer.frame = CGRect(x: 0, y: 0, width: self.base.yl.width, height: self.base.yl.height)
		self.base.layer.insertSublayer(gradientLayer, at: 0)

		startgradientColorAnimation(gradientLayer, startGradientColors, endGradientColors, duration)
	}

	private func startgradientColorAnimation(_ gradientLayer: CAGradientLayer, _ startGradientColors: [Any], _ endGradientColors: [Any], _ duration: CFTimeInterval = 1.0) {
		// 添加渐变动画
		let colorChangeAnimation = CABasicAnimation(keyPath: "colors")
		// colorChangeAnimation.delegate = self
		colorChangeAnimation.duration = duration
		colorChangeAnimation.fromValue = startGradientColors
		colorChangeAnimation.toValue = endGradientColors
		colorChangeAnimation.fillMode = CAMediaTimingFillMode.forwards
		// 动画结束后保持最终的效果
		colorChangeAnimation.isRemovedOnCompletion = false
		gradientLayer.add(colorChangeAnimation, forKey: "colorChange")
	}
	
	/// 扩展UIView增加抖动方法
	///
	/// - Parameters:
	///   - isVertical: 抖动方向（默认是水平方向）
	///   - times: 抖动次数（默认5次）
	///   - interval: 每次抖动时间（默认0.1秒）
	///   - delta: 抖动偏移量（默认2）
	///   - completion: 抖动动画结束后的回调
	func shake(_ isVertical: Bool = false,
					  _ times: Int = 5,
					  _ interval: TimeInterval = 0.1,
					  _ delta: CGFloat = 2,
					  _ completion: (() -> ())? = nil) {
		// 播放动画
		UIView.animate(withDuration: interval) {
			var transform = CGAffineTransform(translationX: 0, y: delta)
			if isVertical == true {
				transform = CGAffineTransform(translationX: delta, y: 0)
			}
			self.base.layer.setAffineTransform(transform)
		} completion: {_ in
			// 如果当前是最后一次抖动，则将位置还原，并调用完成回调函数
			if (times == 0) {
				UIView.animate(withDuration: interval) {
					self.base.layer.setAffineTransform(CGAffineTransform.identity)
				} completion: { _ in
					completion?()
				}
			}else {
				// 如果当前不是最后一次抖动，则继续播放动画（总次数减1，偏移位置变成相反的）
				self.shake(isVertical, times - 1, interval, -delta, completion)
			}
		}
	}
	
	// MARK: 画圆环
	/// 画圆环
	/// - Parameters:
	///   - fillColor: 内环的颜色
	///   - strokeColor: 外环的颜色
	///   - strokeWidth: 外环的宽度
	func drawCircle(fillColor: UIColor, strokeColor: UIColor, strokeWidth: CGFloat) {
		let ciecleRadius = self.base.yl.width > self.base.yl.height ? self.base.yl.height : self.base.yl.width
		let path = UIBezierPath(roundedRect: CGRect(x: 0, y: 0, width: ciecleRadius, height: ciecleRadius), cornerRadius: ciecleRadius / 2)
		let shapeLayer = CAShapeLayer()
		shapeLayer.path = path.cgPath
		shapeLayer.fillColor = fillColor.cgColor
		shapeLayer.strokeColor = strokeColor.cgColor
		shapeLayer.lineWidth = strokeWidth
		self.base.layer.addSublayer(shapeLayer)
	}
	
}

// MARK: - Button扩大点击事件
private var YLUIButtonExpandSizeKey = "YLUIButtonExpandSizeKey"
public extension UIButton {
	override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
		let buttonRect = self.yl.expandRect()
		if (buttonRect.equalTo(bounds)) {
			return super.point(inside: point, with: event)
		}else{
			return buttonRect.contains(point)
		}
	}
}
public extension YLNameSpace where Base: UIButton {

	// MARK: 扩大UIButton的点击区域，向四周扩展10像素的点击范围
	/// 扩大UIButton的点击区域，向四周扩展10像素的点击范围
	/// - Parameter size: 向四周扩展像素的点击范围
	func expandSize(size: CGFloat) {
		objc_setAssociatedObject(self.base, &YLUIButtonExpandSizeKey, size, objc_AssociationPolicy.OBJC_ASSOCIATION_COPY)
	}

	fileprivate func expandRect() -> CGRect {
		let expandSize = objc_getAssociatedObject(self.base, &YLUIButtonExpandSizeKey)
		if (expandSize != nil) {
			return CGRect(x: self.base.bounds.origin.x - (expandSize as! CGFloat), y: self.base.bounds.origin.y - (expandSize as! CGFloat), width: self.base.bounds.size.width + 2 * (expandSize as! CGFloat), height: self.base.bounds.size.height + 2 * (expandSize as! CGFloat))
		} else {
			return self.base.bounds
		}
	}
	
	
	/*
	 枚举 设置 图片的位置
	 */
	enum ButtonImagePosition : Int  {
		case imageTop = 0
		case imageLeft
		case imageBottom
		case imageRight
	}
	
	/**
	type ：image 的位置
	Space ：图片文字之间的间距
	*/
	func setImagePosition(type:ButtonImagePosition,Space space:CGFloat)  {
		
		let imageWith :CGFloat = (self.base.imageView?.frame.size.width)!
		let imageHeight :CGFloat = (self.base.imageView?.frame.size.height)!
		
		var labelWidth :CGFloat = 0.0
		var labelHeight :CGFloat = 0.0
		
		labelWidth = CGFloat(self.base.titleLabel!.intrinsicContentSize.width)

		labelHeight = CGFloat(self.base.titleLabel!.intrinsicContentSize.height)
		
		var  imageEdgeInsets :UIEdgeInsets = UIEdgeInsets()
		var  labelEdgeInsets :UIEdgeInsets = UIEdgeInsets()
		
		switch type {
		case .imageTop:
			imageEdgeInsets = UIEdgeInsets.init(top: -labelHeight - space/2.0, left: 0, bottom: 0, right:  -labelWidth)
			labelEdgeInsets =  UIEdgeInsets.init(top:0, left: -imageWith, bottom: -imageHeight-space/2.0, right: 0)
			break;
		case .imageLeft:
			imageEdgeInsets = UIEdgeInsets.init(top:0, left:-space/2.0, bottom: 0, right:space/2.0)
			labelEdgeInsets =  UIEdgeInsets.init(top:0, left:space/2.0, bottom: 0, right: -space/2.0)
			break;
		case .imageBottom:
			imageEdgeInsets = UIEdgeInsets.init(top:0, left:0, bottom: -labelHeight-space/2.0, right: -labelWidth)
			labelEdgeInsets =  UIEdgeInsets.init(top:-imageHeight-space/2.0, left:-imageWith, bottom: 0, right: 0)
			break;
		case .imageRight:
			imageEdgeInsets = UIEdgeInsets.init(top:0, left:labelWidth+space/2.0, bottom: 0, right: -labelWidth-space/2.0)
			labelEdgeInsets =  UIEdgeInsets.init(top:0, left:-imageWith-space/2.0, bottom: 0, right:imageWith+space/2.0)
			break;
		}
		self.base.titleEdgeInsets = labelEdgeInsets
		self.base.imageEdgeInsets = imageEdgeInsets
	}
	
}
extension UIButton {
 
	func underline() {
 
		guard let text = self.titleLabel?.text else { return }
 
		let attributedString = NSMutableAttributedString(string: text)
 
		attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: text.count))
 
		self.setAttributedTitle(attributedString, for: .normal)
 
	}
 
}
