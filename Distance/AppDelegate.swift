//
//  AppDelegate.swift
//  Distance
//
//  Created by 严明俊 on 2020/9/29.
//

import UIKit
import IQKeyboardManagerSwift
import SwiftyJSON
import Alamofire
import Toast_Swift
import URLNavigator
import SwiftDate
import SwiftyStoreKit
import AppTrackingTransparency
import CocoaLumberjack
import HyphenateChat
import ZLPhotoBrowser
import Moya
import WidgetKit
import SDWebImage
import AdSupport
import StoreKit
import ThinkingSDK
import ActivityKit
import AVFoundation
import GoogleSignIn
import GoogleMobileAds
import FirebaseCore
import FirebaseAnalytics
import FBAEMKit
import FBSDKCoreKit
import AppsFlyerLib
import AnyThinkSDK
import QCloudCOSXML

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
	
    @objc static var shared: AppDelegate?
	
	@objc var showSplashAD: Bool {
		#if DEBUG
		return true
		#else
		//非会员或试用会员都要显示
		return !UserData.shared.isVip || UserData.shared.isTryVip
		
		#endif 
	}
	
	@objc var showRestarCoverView: Bool = false
	
	
	@objc var haveSplash: Bool {
		
		(UserData.shared.splashImage != nil || UserData.shared.splashShowCountDay || !UserData.shared.splashOtherData.isEmpty) && UserData.shared.isVipOther
	}
	    
	var coverView: UIView?
	
    var window: UIWindow?
	
	///启动时间戳
	var timeperiod :Int = 0
	
	///用于判断app生命周期
	var isFirstComeInApp: Bool = true
	
	// 是否是通过点击通知进入的
	var isTouchNotificationEnter: Bool = false
    
    var maskImageView: UIView?
        
    let reach: Reachability = Reachability.forInternetConnection()
		
	var isFirstLaunchOnToday = false
	
	static var restart: (()->Void) = {}
	
	static var checkVersionAfterLoginBlock: (()->Void)?
	
	var isStartApp: Bool = true
	var isLock: Bool = false {
		didSet {
			UserData.shared.add(model: YLDeviceStatusModel.getModel(reportType: .lock), isStartApp: isStartApp)
			isStartApp = false
		}
	}
	
	var backgroundEnterTime: Date?
		
	@UserDefault("lastOpenDateKey", defaultValue: Date(timeIntervalSince1970: 0))
	var lastOpenDate: Date
	///激活时间
	@UserDefault("firstInitDateKey", defaultValue: Date(timeIntervalSince1970: 0))
	var firstInitDate: Date
	
	@UserDefault("lastLoadLocationDate", defaultValue: 0)
	var lastLoadLocationDate: Int

	//是否上报过归因
	@UserDefault("couple2.isuploadAttribute", defaultValue: false)
	var isuploadAttribute: Bool
	
	var adManager = WCSplashAdManager()
	
	var topMostViewController: UIViewController? {
		var topController = self.window?.rootViewController
		while topController?.presentedViewController != nil {
			topController = topController?.presentedViewController
		}
		return topController
	}
	
	var credentialFenceQueue: QCloudCredentailFenceQueue?
	
	func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
		ApplicationDelegate.shared.application(
			application,
			didFinishLaunchingWithOptions: launchOptions
		)
		Self.shared = self
		// Override point for customization after application launch.
		UIApplication.swizzleOpenURL
		Self.restart = { [weak self] in
			self?.showRestarCoverView = true
			_ = self?.application(application, didFinishLaunchingWithOptions: launchOptions)
			self?.showRestarCoverView = false
		}
		
		YLProxyTool.shared.proxySetup(proxyType: YLOnlieParameterMap.default().proxyType)
		
		UIDevice.requestUA()
		
		YLLocationManager.shared.isStartApp = true
		
		UIApplication.shared.setMinimumBackgroundFetchInterval(UIApplication.backgroundFetchIntervalMinimum)

		UserDefaults.standard.setValue(WCRequestPublicParameters.appVersion, forKey: "CurrentAppVersion")
		
		YLChatManager.shared.start()
		
		if lastOpenDate.compare(toDate: Date(), granularity: .day) == .orderedSame {
			isFirstLaunchOnToday = false
		} else {
			isFirstLaunchOnToday = true
		}
		
		#if DEBUG
//		YLSceneItemView.needShowLongPressGuide = true
		isFirstLaunchOnToday = true
		#endif
		timeperiod = Int(Date().timeIntervalSince1970)
                
        Navigator.shared.registerActions()
		SwiftDate.defaultRegion = .current
		
        self.window?.backgroundColor = .white
		
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.shouldResignOnTouchOutside = true
        IQKeyboardManager.shared.enableAutoToolbar = false
        publicToastConfig()
        let webPCoder = SDImageWebPCoder.shared
        SDImageCodersManager.shared.addCoder(webPCoder)
        SDWebImageDownloader.shared.setValue("image/webp,image/*,*/*;q=0.8", forHTTPHeaderField:"Accept")
		
        UIButton.appearance().isExclusiveTouch = true
        UNUserNotificationCenter.current().delegate = self
		
		if let remoteNotification = launchOptions?[UIApplication.LaunchOptionsKey.remoteNotification] {
			let data = JSON(remoteNotification)
			
			let pushType: PushType?
			if let e = data["e"].string, e == "im" {
				pushType = .im
			} else {
				pushType = PushType(rawValue: data["type"].intValue)
			}
			
			if let _ = pushType {
				YLPushControl.remoteNotification = data
			}
		}
		
		
		if UserData.shared.isFullInformation == false {
            let controller = Storyboard.Login.instantiate(YLLoginViewController.self)
			let nv = YLNavigationViewController(navigationBarClass: YLNavigationBar.self, toolbarClass: nil)
			nv.setViewControllers([controller], animated: false)
            window?.rootViewController = nv
            serverReport()
			onAppDidLaunchOrEnterForeground(isLaunching: true)

			
        } else {
			UserData.shared.getCurrentInfo(timeout: 1.5) { result in
				let user = UserData.shared
				self.chooseRootVCWith(userData: user)
				self.launchGetNoticeInfo()
				self.onAppDidLaunchOrEnterForeground(isLaunching: true)
			}
        }
		
		
		
		self.adManager.showSplashScrrenIfNeeded(withIsFromlaunch: true)
		//注意谷歌sdk不能初始化两次 单次app生命周期
		if self.showRestarCoverView {
			return true
		}
		handleNetworkReachabilityChange()
		initRequestData()
		
		YLStatisticsHelper.trackEvent("Closer.Customize_Lanch", dic: [
			"userid": UserData.shared.userId,
			"deviceid": WCRequestPublicParameters.openUDID,
			"pt": "冷启动"
		])
		registerTokenInvalidNotification()
		if #available(iOS 14.0, *) {
			UITableViewCell.appearance().backgroundConfiguration = UIBackgroundConfiguration.clear()
		}
		
		YLLogTool.config()
		//原支付
//		registerInAppPurchase()
		//v2
		YLPaymentService.shared.setup()
		
		setupImagePreviewStyle()
		uploadOpenLog()
		isLock = false
		lastOpenDate = Date()

		do {
			try AVAudioSession.sharedInstance().setCategory(.playback)
			try AVAudioSession.sharedInstance().setActive(true)
		} catch {
			print("AVAudioSession:\(error)");
		}
		
		
        return true
    }
	
	func initRequestData() {
		DispatchQueue.global().async {
			self.launchGetNoticeInfo()
			WCOnlieParameter.default.update()
			YLChatTalkInitManager.shared.requestData(completion: nil)
			StickyManager.shared.getStickyModule()
			StickyManager.shared.getStickyModule(paper: 1)
		}
		YLPayManager.registerManager()
		MobileAds.shared.start(completionHandler: nil)
		regEventsStatistics()
	
	}
	
	/// APP统计
	func regEventsStatistics() {
		//shushu
		initShuShu()
		//AppsFlyer
		AppsFlyerLib.shared().appleAppID = "6673890505"
		AppsFlyerLib.shared().appsFlyerDevKey = "aVg6HRucDUQU9zqCQ48TTZ"
		AppsFlyerLib.shared().delegate = self
		//firebase
		FirebaseApp.configure()

		// idfa
		//AppsFlyerLib.shared().waitForATTUserAuthorization(timeoutInterval: 20)
		Settings.initialize()
		
		initTopOn()
		
	}

	func initShuShu() {
		// 1、初始化 iOS SDK

//		// 2、调用 enableThirdPartySharing 接口 设置 ta_distinct_id 到 appsflyer 事件中
//		// 3、强烈建议您使用 setCustomerUserId() 再设置一遍访客
//		NSString *distinctId = [TDAnalytics getDistinctId];
//		[[AppsFlyerLib shared] customerUserID:distinctId];
//		// 4、初始化 Appsflyer SDK
//		//  。。。
		// 5、注册或创角后，调用 login 设置账号 ID后，需要再次同步数据(可选)
		let config = TDConfig()
		if YLTestEnviromentManager.shared.isTestEnviroment {
			config.mode = .debug
		}
		config.appid = "8bd4d274eac34bae8c175ecce2b9b4fa";
		config.serverUrl = "https://ss.51wnl.com";
		TDAnalytics.start(with: config)
		var additionalData: [String: NSString] = [:]
		additionalData["device_id"] = TDAnalytics.getDeviceId() as NSString
		TDAnalytics.enableThirdPartySharing([.appsFlyer,.topOn],properties: additionalData)
		AppsFlyerLib.shared().customerUserID = TDAnalytics.getDistinctId()
		TDAnalytics.enableAutoTrack([.appStart,.appInstall,.appEnd,.appViewCrash],properties: YLStatisticsHelper.properties())

		if UserData.shared.isLogin {
			TDAnalytics.login(UserData.shared.userId)
		}

	}
	
	func initTopOn() {
		#if DEBUG
		ATAPI.setLogEnabled(true)
		ATAPI.integrationChecking()
		#endif
		let disID = TDAnalytics.getDistinctId()
		//此为和tapon 关联
		ATAPI.sharedInstance().customData = [kATCustomDataUserIDKey:disID]
		try? ATAPI.sharedInstance().start(withAppID: "h68356a88c2d5c", appKey: "acf38c9b7f7cece744d3ff8d11c611b86")
	}
	
	func setupImagePreviewStyle() {
		let config = ZLPhotoConfiguration.default()
		config.customImageNames = ["lt_yl_fx_icon", "lt_spyl_bf_icon", "lt_yl_gb_icon", "spbfyl_zt_icon", "spbfyl_bf_icon", "spyl_jdy_img"]
		ZLPreviewView.imageViewClass = SDAnimatedImageView.self
		config.statusBarStyle = .default
		config.showStatusBarInPreviewInterface = true
		config.themeColorDeploy.thumbnailBgColor = UIColor(R: 246, G: 244, B: 231)
	}
	
	func handleNetworkReachabilityChange() {
		reach.reachableBlock = { (reach) in
			DDLogInfo("网络已连接")
			DispatchQueue.main.async {
				if UserData.shared.isLogin {
					if UserData.shared.matchStatus == .success {
						self.refreshHomeData()
					}
				}
				
			}
		}
		
		reach.unreachableBlock = { (reach) in
//			print("网络连接断开")
			DDLogInfo("网络连接断开")
		}
		reach.startNotifier()
		
		// 网络充电发生变化
		YLNetworkChargingStatusManager.shared.startObserve()
	}
	
	
    func publicToastConfig() {
        ToastManager.shared.isTapToDismissEnabled = false
        ToastManager.shared.isQueueEnabled = true
        ToastManager.shared.duration = 2
        ToastManager.shared.style.backgroundColor = UIColor.white
        ToastManager.shared.style.messageFont = UIFont.systemFont(ofSize: 13)
        ToastManager.shared.style.messageColor = #colorLiteral(red: 0.4039215686, green: 0.1803921569, blue: 0.0431372549, alpha: 1)
        ToastManager.shared.style.cornerRadius = 18
        ToastManager.shared.style.horizontalPadding = 35
        ToastManager.shared.style.verticalPadding = 11
        ToastManager.shared.style.displayShadow = true
        ToastManager.shared.style.shadowColor = #colorLiteral(red: 0.6352941176, green: 0.6352941176, blue: 0.6352941176, alpha: 0.38)
        ToastManager.shared.style.shadowOpacity = 0.08
        ToastManager.shared.style.shadowOffset = CGSize(width: 0, height: 4)
        ToastManager.shared.style.shadowRadius = 4
    }
	
	/// 当用户登录成功的时候会被调用
	func onUserDidLogin() {
		Self.checkVersionAfterLoginBlock?()
		uploadLocation()
		adManager.enteredHomePage = true
	}
	
	/// 应用从后台到前台之间的间隔时间
	@objc func applicationDidEnterForeground(withInterval interval: Int) {
		DDLogInfo("应用从后台到前台的时间间隔为: \(interval) 秒")
		if interval > YLOnlieParameterMap.default().backgroundOpenAppTime {
			//超过10分钟上报打开app
			uploadOpenLog()
		}
		
	}
	
	/// 每次启动应用或者应用从后台到前台之间的时间超过1小时时会被调用
	@objc func onAppDidLaunchOrEnterForeground(isLaunching: Bool = false) {
		if YLDistanceAlertView.haveShowPrivicy {
			if UserData.shared.isLogin {
				YLDistanceAlertView.judgeAppVersion(needJudgeHide: true)
			}else {
				Self.checkVersionAfterLoginBlock = {
					YLDistanceAlertView.judgeAppVersion(needJudgeHide: true)
					Self.checkVersionAfterLoginBlock = nil
				}
			}
			
		}
		
		if isLaunching && UIApplication.shared.applicationState == .background {
		} else {
			if YLDistanceAlertView.haveShowPrivicy {
				YLMontioURLManager.shared.reportURL()
			}
		}
		uploadLocation()
	}
	
	func uploadLocation() {

		guard UserData.shared.isLogin else { return }
		
		
		#if DEBUG
//		YLFurnitureStore.shared.fetchAllGoods()
		#endif
		
		if YLLocationManager.shared.isAuthorized && YLLocationManager.shared.isUserPermited {//开启了定位服务，试用定位服务
			YLLocationManager.shared.startUpdatingLocation()
		}
		YLTaskManager.default.ActivityTimeEnd = true
		//超过1小时重新展示浮窗
		YLHomeViewController.shared?.showVipHomeFloatView(isShow: true,showHome: true, fromType: .vip)
		YLBehaviorManager.shared.refreshTask()
		
	}
	
	
	func uploadOpenLog() {
		YLLocationManager.shared.uploadDeviceOperate(eventType: .open)
	}
		
	@objc func notifyVIPExpired() {
		NotificationCenter.default.post(name: .vipStatusChangeNotification, object: UserData.shared)
	}
	
	func registerInAppPurchase() {
		
		SwiftyStoreKit.shouldAddStorePaymentHandler = { payment, product in
			YLPushControl.updateType(type: .vipPurchase, reportType: nil)
			YLPushControl.isPush = true
			YLPushControl.updatePushVC()
			return false
		}
		
		SwiftyStoreKit.completeTransactions(atomically: false, completion: { purchases in
			guard UserData.shared.isLogin else {
				return
			}
			NotificationCenter.default.post(name: .rechargeDismissRechargeLoading, object: nil)
			
			for purchase in purchases {
				#if DEBUG
//				SwiftyStoreKit.finishTransaction(purchase.transaction)
				#endif
				switch purchase.transaction.transactionState {
				case .purchased, .restored:

					let model = YLRechargeCacheModel.findModel(transaction: purchase.transaction)
					
					let transactionIdentifier = purchase.transaction.transactionIdentifier
					let originalTransactionIdentifier = purchase.originalTransaction?.transactionIdentifier
					if let transactionIdentifier = purchase.transaction.transactionIdentifier, var model = model {
						YLRechargeCacheModel.removeRecord(record: model)
						model.transactionIdentifier = transactionIdentifier
						model.originalTransactionId = originalTransactionIdentifier
						YLRechargeCacheModel.addRecord(record: model)
					}
					
					let receiptValidator = YLReceiptValidator(transactionIdentifier: transactionIdentifier, originalTransactionId: originalTransactionIdentifier, orderId: model?.orderId, WNLOrderId: model?.WNLOrderId, productId: purchase.productId, isCache: true)
				
					
					SwiftyStoreKit.verifyReceipt(using: receiptValidator, completion: { result in
						switch result {
						case .error:
							break
						case .success:
							if purchase.needsFinishTransaction {
								SwiftyStoreKit.finishTransaction(purchase.transaction)
							}
							if let model = model {
								YLRechargeCacheModel.removeRecord(record: model)
							}
						}
					})
					
				case .failed, .purchasing, .deferred:
					break
				@unknown default:
					fatalError()
				}
			}
		})
		
	}

    
    func serverReport() {
		if #available(iOS 14.3, *) {
			if !self.isuploadAttribute {
				YLASAManager.foundSource()
			}
		}
		
		AppsFlyerLib.shared().waitForATTUserAuthorization(timeoutInterval: 20)

		if #available(iOS 14, *) {
			ATTrackingManager.requestTrackingAuthorization { status in
				if status == .authorized {
					Settings.shared.isAdvertiserTrackingEnabled = true
					YLPublicParameter.IDFA = ASIdentifierManager.shared().advertisingIdentifier.uuidString
				}
				if self.firstInitDate.isYesterday {
					YLMontioURLManager.shared.reportURL(event_type: 7)
				}
			}
		} else {
			if ASIdentifierManager.shared().isAdvertisingTrackingEnabled {
				YLPublicParameter.IDFA = ASIdentifierManager.shared().advertisingIdentifier.uuidString
				if firstInitDate.isYesterday {
					YLMontioURLManager.shared.reportURL(event_type: 7)
				}
			}
		}
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [: ]) -> Bool {
		ApplicationDelegate.shared.application(
			 app,
			 open: url,
			 sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
			 annotation: options[UIApplication.OpenURLOptionsKey.annotation]
		 )
		if YLPayManager.payHandleOpenURL(openURL: url) {
			return true
		}
		
		let handled = GIDSignIn.sharedInstance.handle(url)
		if handled {
			return true
		}
		var abs = url.absoluteString
		let prefixStr = "ylcloser://match?code="
		if abs.hasPrefix(prefixStr) {
			abs.removeFirst(prefixStr.count)
			UserData.shared.pasteCode = abs.removingPercentEncoding!
			DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(2), execute: {
				NotificationCenter.default.post(name: updateMatchPasswordNotification, object: nil)
			})
			return true
		}
		
		if abs.hasPrefix("ylcloser://") {
			DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(2), execute: {
				if Navigator.shared.open(abs, context: nil) {
					
				}
			})
			return true
		}
		
//		let result = UMSocialManager.default()?.handleOpen(url)
		
		if abs.hasPrefix("ylcloser://ylweb?url=") {
			let url = (abs as NSString).substring(with: NSMakeRange(21, abs.count - 21)) as String
			DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(2), execute: {
				Navigator.shared.open(url.removingPercentEncoding!)
			})
		}
		return true
//		return result ?? true
    }

	
	
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
		if let url = userActivity.webpageURL, YLPayManager.payHandleOpenURL(openURL: url) {
			return true
		}
		let prefix = "https://www.51wnl-cq.com/closer/?path="
		if let string = userActivity.webpageURL?.absoluteString, string.hasPrefix(prefix) {
			var urlString = string.replacingOccurrences(of: prefix, with: "ylcloser://")
			if let range = urlString.range(of: "&") {
				urlString.replaceSubrange(range, with: "?")
			}
			DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(2), execute: {
				if Navigator.shared.open(urlString, context: nil) {
					
				}
			})
			return true
		}
		return true
    }
	
	func application(_ application: UIApplication, performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
		// 执行定时任务
		completionHandler(.newData)
	}

	func applicationDidEnterBackground(_ application: UIApplication) {
		// 记录进入后台的时间
		backgroundEnterTime = Date()
		
		showCoverView()
		isTouchNotificationEnter = false
		YLStatisticsHelper.trackEvent("XDRC.Reception.Report")
		stopTimer()
		YLLocationManager.shared.uploadDeviceOperate(eventType: .background)
		window?.endEditing(true)

//		UserData.shared.applicationDidEnterBackground()
		
		if !YLLocationManager.shared.isAuthorizedAlways {
			EMClient.shared().applicationDidEnterBackground(application)
		}
		DDLogInfo("【applicationDidEnterBackground】")
		
//		startTime()
		
    }
	private var timer: Timer? // 等待回复的时间
	
	private func startTime() {
		clearTime()
		timer = Timer.scheduledTimer(timeInterval: 25, target: self, selector: #selector(updateDeviceStateTime), userInfo: nil, repeats: true)
		RunLoop.main.add(timer!, forMode: .common)
	}
	
	private func clearTime() {
		self.timer?.invalidate()
		self.timer = nil
	}
	
	@objc func updateDeviceStateTime() {
		
		if UIApplication.shared.backgroundTimeRemaining < 60 {
			DDLogInfo("我还在isLoggedIn:\(YLChatManager.shared.isLoggedIn), isConnected:\(YLChatManager.shared.client.isConnected)")
		}
	}
	
	func applicationDidBecomeActive(_ application: UIApplication) {
		startTimer()
		AppsFlyerLib.shared().start()
//		UserData.shared.applicationDidBecomeActive()
	}
	
	func applicationWillTerminate(_ application: UIApplication) {
//		appTerminateLocalPush()
		DDLogInfo("【用户主动杀死了app】")
//		UserData.shared.applicationWillTerminate()
	}
	
	func applicationWillResignActive(_ application: UIApplication) {
		if #available(iOS 14.0, *) {

			YLWidgetDeskPhotoData.shared.updateWidget()
			
			DistanceWidgetHelper.reloadTimelinesWithPetWidget()
			DistanceWidgetHelper.reloadTimelinesWithStatusWidget()

		}
		
	}
    
	func applicationProtectedDataDidBecomeAvailable(_ application: UIApplication) {
		isLock = false
		UserData.shared.localPushDeviceState()
	}
	
	func applicationProtectedDataWillBecomeUnavailable(_ application: UIApplication) {
		isLock = true
	}
	
    func applicationWillEnterForeground(_ application: UIApplication) {
		
		if let backgroundTime = backgroundEnterTime {
			let interval = Date().timeIntervalSince(backgroundTime)
			applicationDidEnterForeground(withInterval: Int(interval))
		}
		
		hiddenCverView()
		if !YLLocationManager.shared.isAuthorizedAlways {
			EMClient.shared().applicationWillEnterForeground(application)
		}
		Self.shared = self
		if UserData.shared.matchStatus == .success {
			YLLocationManager.shared.uploadLocationSetting()
			func configBGMusic(viewController: UIViewController?) {
				if let vc = viewController as? EnableBgMusicProtocol {
					if vc.isEnableBgMusic {
						//MNPlayer.shared.open()
						//YLBgMusicPlayer.shared.play()
						YLPlayViewController.playBgmIfNeeded()
					} else {
						MNPlayer.shared.close()
						//YLBgMusicPlayer.shared.pause()
					}
				} else {
					//MNPlayer.shared.open()
					//YLBgMusicPlayer.shared.play()
					YLPlayViewController.playBgmIfNeeded()
					NotificationCenter.default.post(name: YLMovieChatViewController.PauseBGM, object: nil);
					
				}
			}
			
			configBGMusic(viewController: UIViewController.ylTopMost)
		}
		adManager.showBackgroundSplashIfNeeded()
	
//		YLLocationManager.shared.isBackground = false
        refreshHomeData()
		
//		let homeViewController = getVC(vc: YLHomeViewController.self)
//		homeViewController?.showMovieAlertViewIfNeed()
		
		
		// 延迟是为了等待确定是否需要处理通知业务
		DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 1.0) { [weak self] in
			guard let _ = self else {
				YLStatisticsHelper.trackEvent("XDRC.Backstage.Report", dic: ["tpye": "桌面图标"])
				return
			}
			YLStatisticsHelper.trackEvent("XDRC.Backstage.Report", dic: ["tpye": (self!.isTouchNotificationEnter ? "推送" : "桌面图标")])
		}
		
		YLStatisticsHelper.trackEvent("Closer.Customize_Lanch", dic: [
			"userid": UserData.shared.userId,
			"deviceid": WCRequestPublicParameters.openUDID,
			"pt": "热启动"
		])
		DDLogInfo("【applicationWillEnterForeground】")
//		_ = YLVpnErrorView.showVpnErrorIfNeeded()
    }
	
    func refreshHomeData() {
        let userData = UserData.shared
        let lastMatchAction = userData.matchStatus
        
        UserData.shared.getCurrentInfo { (result) in
            switch result {
            case let .success(userData):
                if lastMatchAction != userData.matchStatus {
                    if userData.matchStatus == .success {
                        NotificationCenter.default.post(name: successMatchNotification, object: nil)
						
                    } else {
                        NotificationCenter.default.post(name: breakMatchNotification, object: nil)
                    }
                } else {
                    if userData.matchStatus == .success {
                        NotificationCenter.default.post(name: userDataRefreshNotification, object: nil)
                        NotificationCenter.default.post(name: privityChallengeNotification, object: nil)
                        NotificationCenter.default.post(name: updateHeatAndGoldNotification, object: nil)
                    }
                }
				
				if userData.matchStatus == .success {
					if !userData.haveShowFirstMatchGuide, userData.homeType == .scene {
						YLRestoreManager.shared.matchSuccess()
					}
				}
				
            case .failure:
                break
            }
        }
        userData.handleSceneUpdateMessage()
    }
	
}

extension AppDelegate {
    func chooseRootVCWith(userData: UserData) {
        var vc: UIViewController!
		
		
		func getMatchVC() -> UIViewController {
			UserData.shared.remarkBySelf = ""
			UserData.shared.remarkByOther = ""
			UserData.shared.isOtherMsgDetail = true
			let vc = Storyboard.Main.instantiate(YLMatchPageViewController.self)
			return vc
		}
		
        if !userData.isLogin {
			UserData.shared.remarkBySelf = ""
			UserData.shared.remarkByOther = ""
			UserData.shared.isOtherMsgDetail = true
            vc = Storyboard.Login.instantiate(YLLoginViewController.self)
        } else {
//			MobClick.profileSignIn(withPUID: UserData.shared.userId)//统计应用自身的账号
			YLChatManager.shared.login()
            switch userData.matchStatus {
            case .none:
                vc = Storyboard.Login.instantiate(YLLoginUserInfoViewController.self)
				if UserData.shared.loginType == 3 {
					if !YLOnlieParameterMap.default().isPhoneLogin || UserData.shared.isFullInformation {
						vc = getMatchVC()
					}
				}
			case .watingMatch,.waitingInput:
				guard YLMatchPageViewController.matchPageVC == nil else {
					return
				}
				vc = getMatchVC()
				if UserData.shared.loginType == 3 {
					if YLOnlieParameterMap.default().isPhoneLogin && !UserData.shared.isFullInformation {
						vc = Storyboard.Login.instantiate(YLLoginUserInfoViewController.self)
					}
				}
				
            case .success:
				vc = Storyboard.Main.instantiate(YLHomeViewController.self)
			case .waitExport:
				vc = YLExportDiaryViewController()
				break
            }
        }
		
		let nv = YLNavigationViewController(navigationBarClass: YLNavigationBar.self, toolbarClass: nil)
		nv.setViewControllers([vc], animated: false)
        self.window?.rootViewController = nv
        serverReport()
    }
}

extension AppDelegate: UNUserNotificationCenterDelegate {
	func configPushNotification() {
		guard UserData.shared.isLogin else { return }
		YLAlerViewManager.shared.addAlertQueueHandlers(name: "通知弹框", queuePriority: .low) { taskOperation,_ in
			UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) {isAuthorization, error in
				taskOperation?.endOperation()
				guard isAuthorization else { return }
				self.registerForAPNSNotifications()
			}
		}
	}
	func registerForAPNSNotifications() {
		UNUserNotificationCenter.current().getNotificationSettings { (settings) in
			if settings.authorizationStatus == .authorized {
				DispatchQueue.main.async {
					UIApplication.shared.registerForRemoteNotifications()
				}
			}
		}
	}
	func applicationDidReceiveMemoryWarning(_ application: UIApplication) {
		let manager = SDWebImageManager.shared
		manager.cancelAll()
		manager.imageCache.clear?(with: .memory)
		DDLogInfo("【applicationDidReceiveMemoryWarning】")
	}

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
		var token = ""
		for byte in [UInt8](deviceToken) {
			token += String(format: "%02x", byte & 0x000000FF)
		}
		print(token+"------------")
		UserData.RegisterPushToken = token
		UserData.shared.uploadPushTokenIfNeed()
		DispatchQueue.global().async {
			EMClient.shared().bindDeviceToken(deviceToken)
		}
		
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
    
    }
	
	func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
		YLWidgetDeskPhotoData.shared.updateWidget()
//		if YLUmengConfig.isCompletHandler(userInfo) {
//			completionHandler(.newData);
//		}
	}

	func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any]) {
	}
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        
		let userInfo = notification.request.content.userInfo

		let data = JSON(userInfo)
		let pushType: PushType?
		if let e = data["e"].string, e == "im" {
			pushType = .im
		} else {
			pushType = PushType(rawValue: data["type"].intValue)
		}
		if let type = pushType, type == .newSticky {
			UserData.shared.notifyMemoAdd()
		}
        completionHandler([.alert, .sound, .badge])
		
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
		
		let userInfo = response.notification.request.content.userInfo
		AppDelegate.shared?.isTouchNotificationEnter = true
		
		let data = JSON(userInfo)
		var useData = data
		let payload = useData["payload"].stringValue
		if !payload.isEmpty {
			//个推离线回调数据
			useData = JSON(payload.yl.jsonStringToDictionary() ?? ["":""])
		}
		
		let pushType: PushType?
		if let e = useData["e"].string, e == "im" {
			pushType = .im
		} else {
			pushType = PushType(rawValue: useData["type"].intValue)
		}

		if let type = pushType {
			YLPushControl.updateType(type: type,reportType:useData["pushType"].stringValue)
			YLPushControl.isPush = true
			YLPushControl.updatePushVC(userInfo: useData)
		}
        completionHandler()
		
    }
	
	func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {

		if let topVc = topViewController.self, String(describing: topVc.self).contains(find: "BUWebView") {
			return .all
		}
		
		return .portrait
	}

	
	func stopTimer() {
		YLAblumPreviewController.shared?.pause()

	}
	
	func startTimer() {
		let userData = UserData.shared
		YLAblumPreviewController.shared?.resume()
		
	}
}



extension UIAlertController {
	open override var shouldAutorotate: Bool {
		return false
	}
}

@available(iOS, introduced: 15.7, obsoleted: 16.0)
@objc extension SKStoreProductViewController {
	func sceneDisconnected(_ arg: AnyObject) {
		
	}
	func appWillTerminate() {
		
	}
}

extension AppDelegate: AppsFlyerLibDelegate {
	func onConversionDataSuccess(_ conversionInfo: [AnyHashable : Any]) {
		if isuploadAttribute {
			return
		}
		YLASAManager.AFData = conversionInfo
		YLASAManager.uploadASAData()
		
	}
	
	func onConversionDataFail(_ error: any Error) {
		print("-----------error\(error)")
	}
	 
}

//MARK: QCloudSignatureProvider, QCloudCredentailFenceQueueDelegate
extension AppDelegate: QCloudSignatureProvider, QCloudCredentailFenceQueueDelegate {
	
	func initCdnSDK() {
		credentialFenceQueue = QCloudCredentailFenceQueue()
		credentialFenceQueue?.delegate = self
		
		let config = QCloudServiceConfiguration()
		config.appID = YLQCloudUploadManager.appID
		
		let endpoint = QCloudCOSXMLEndPoint()
		// 替换为用户的 region，已创建桶归属的 region 可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
		// COS 支持的所有 region 列表参见 https://www.qcloud.com/document/product/436/6224
		endpoint.regionName = YLQCloudUploadManager.regionName
		// 使用 HTTPS
		endpoint.useHTTPS = true
		config.endpoint = endpoint
		config.signatureProvider = self
		
		// 初始化 COS 服务示例
		QCloudCOSXMLService.registerDefaultCOSXML(with: config)
		QCloudCOSTransferMangerService.registerDefaultCOSTransferManger(with: config)
	}
	
	
	// 获取签名的回调方法入口
	func signature(with fileds: QCloudSignatureFields!,
				   request: QCloudBizHTTPRequest!,
				   urlRequest urlRequst: NSMutableURLRequest!,
				   compelete continueBlock: QCloudHTTPAuthentationContinueBlock!) {
		credentialFenceQueue?.performAction { creator, error in
			if let creator = creator {
				let signature = creator.signature(forData: urlRequst)
				continueBlock(signature, nil)
			} else {
				continueBlock(nil, error)
			}
		}
	}
	
	func fenceQueue(_ queue: QCloudCredentailFenceQueue!, requestCreatorWithContinue continueBlock: QCloudCredentailFenceQueueContinue!) {
		
	}
	
	
}
