#!/bin/bash

# PNG图片压缩脚本
# 使用pngquant以90-100的质量范围压缩PNG图片，保留透明度

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}PNG图片压缩工具 - 智能透明度检测版本${NC}"
    echo "智能检测PNG透明度，自动选择最佳质量设置"
    echo ""
    echo "用法:"
    echo "  $0 [选项] [目录或文件]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -r, --recursive 递归处理子目录"
    echo "  -o, --output   指定输出目录（默认覆盖原文件）"
    echo "  -q, --quality  指定质量范围（格式: 最小-最大）"
    echo "                 默认: 自动检测透明度并选择质量"
    echo "                 - 有透明度: 90-100 (高质量保留透明度)"
    echo "                 - 无透明度: 75-85 (标准压缩)"
    echo "  --dry-run      预览模式，不实际压缩文件"
    echo ""
    echo "示例:"
    echo "  $0 image.png                    # 压缩单个文件，自动检测透明度"
    echo "  $0 -r ./images                  # 递归压缩images目录下所有PNG，智能质量选择"
    echo "  $0 -o ./compressed ./images     # 压缩到指定目录"
    echo "  $0 -q 80-90 image.png          # 使用自定义质量范围"
    echo ""
    echo "功能特性:"
    echo "  • 智能检测PNG透明度，自动选择最佳质量设置"
    echo "  • 有透明度的图片使用90-100高质量，保留透明效果"
    echo "  • 无透明度的图片使用75-85标准质量，获得更好压缩率"
    echo "  • 自动跳过压缩后会增大的文件"
    echo "  • 支持递归处理和自定义输出目录"
    echo "  • 详细的压缩统计和进度显示"
}

# 默认参数
RECURSIVE=false
OUTPUT_DIR=""
QUALITY="75-85"  # 默认质量范围，脚本会自动检测透明度并调整
DRY_RUN=false
TARGET=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--recursive)
            RECURSIVE=true
            shift
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -q|--quality)
            QUALITY="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -*)
            echo -e "${RED}错误: 未知选项 $1${NC}"
            show_help
            exit 1
            ;;
        *)
            TARGET="$1"
            shift
            ;;
    esac
done

# 检查是否提供了目标
if [[ -z "$TARGET" ]]; then
    echo -e "${RED}错误: 请指定要压缩的文件或目录${NC}"
    show_help
    exit 1
fi

# 检查目标是否存在
if [[ ! -e "$TARGET" ]]; then
    echo -e "${RED}错误: 文件或目录不存在: $TARGET${NC}"
    exit 1
fi

# 创建输出目录（如果指定了）
if [[ -n "$OUTPUT_DIR" ]]; then
    mkdir -p "$OUTPUT_DIR"
    if [[ ! -d "$OUTPUT_DIR" ]]; then
        echo -e "${RED}错误: 无法创建输出目录: $OUTPUT_DIR${NC}"
        exit 1
    fi
fi

# 压缩单个PNG文件的函数
compress_png() {
    local input_file="$1"
    local output_file="$2"
    
    if [[ "$DRY_RUN" == true ]]; then
        echo -e "${YELLOW}[预览] 将压缩: $input_file -> $output_file${NC}"
        return 0
    fi
    
    echo -e "${BLUE}压缩中: $input_file${NC}"
    
    # 检测PNG文件是否有透明度
    local has_alpha=false
    if command -v sips >/dev/null 2>&1; then
        # 使用macOS的sips命令检测Alpha通道
        if sips -g hasAlpha "$input_file" 2>/dev/null | grep -q "hasAlpha: yes"; then
            has_alpha=true
        fi
    else
        # 如果没有sips，尝试使用file命令检测RGBA格式
        if file "$input_file" 2>/dev/null | grep -q "RGBA"; then
            has_alpha=true
        fi
    fi
    
    # 根据是否有透明度选择质量设置
    local quality_setting
    local user_specified_quality=false
    
    # 检查用户是否指定了自定义质量（通过命令行参数）
    if [[ "$QUALITY" != "75-85" ]]; then
        user_specified_quality=true
    fi
    
    if [[ "$user_specified_quality" == true ]]; then
        # 用户指定了自定义质量，使用用户设置
        quality_setting="$QUALITY"
        echo -e "  使用用户指定质量: $quality_setting"
    elif [[ "$has_alpha" == true ]]; then
        # 检测到透明度，使用高质量设置
        quality_setting="90-100"
        echo -e "  检测到透明度，使用高质量设置: $quality_setting"
    else
        # 无透明度，使用标准质量设置
        quality_setting="75-85"
        echo -e "  无透明度，使用标准质量设置: $quality_setting"
    fi
    
    # 获取原文件大小
    local original_size=$(stat -f%z "$input_file" 2>/dev/null || stat -c%s "$input_file" 2>/dev/null)
    
    # 创建临时文件用于测试压缩
    local temp_file="${output_file}.tmp"
    
    # 根据是否有透明度选择压缩参数
    local pngquant_args="--quality=$quality_setting --force --skip-if-larger"
    if [[ "$has_alpha" == true ]]; then
        # 有透明度时使用保留透明度的参数
        pngquant_args="$pngquant_args --nofs --speed=1"
    fi
    
    # 使用pngquant压缩到临时文件
    if pngquant $pngquant_args --output "$temp_file" "$input_file" 2>/dev/null; then
        # 获取压缩后文件大小
        local compressed_size=$(stat -f%z "$temp_file" 2>/dev/null || stat -c%s "$temp_file" 2>/dev/null)
        
        # 计算压缩比例
        local reduction=$((100 - (compressed_size * 100 / original_size)))
        
        # 检查是否压缩有效（文件变小）
        if [[ $compressed_size -lt $original_size ]]; then
            # 压缩有效，移动临时文件到目标位置
            mv "$temp_file" "$output_file"
            
            echo -e "${GREEN}✓ 压缩完成: $input_file${NC}"
            echo -e "  原大小: $(numfmt --to=iec-i --suffix=B $original_size)"
            echo -e "  新大小: $(numfmt --to=iec-i --suffix=B $compressed_size)"
            echo -e "  减少: ${reduction}%"
            echo ""
            
            # 更新全局统计
            total_original_size=$((total_original_size + original_size))
            total_compressed_size=$((total_compressed_size + compressed_size))
            
            return 0
        else
            # 压缩后反而变大，删除临时文件，保持原文件不变
            rm -f "$temp_file"
            
            echo -e "${YELLOW}⚠ 跳过压缩: $input_file (压缩后会增大 ${reduction#-}%)${NC}"
            echo -e "  原大小: $(numfmt --to=iec-i --suffix=B $original_size)"
            echo -e "  压缩后: $(numfmt --to=iec-i --suffix=B $compressed_size)"
            echo ""
            
            # 如果不是覆盖模式，需要复制原文件到输出位置
            if [[ "$input_file" != "$output_file" ]]; then
                cp "$input_file" "$output_file"
            fi
            
            # 更新全局统计（保持原大小）
            total_original_size=$((total_original_size + original_size))
            total_compressed_size=$((total_compressed_size + original_size))
            skipped_files=$((skipped_files + 1))
            
            return 0
        fi
    else
        echo -e "${RED}✗ 压缩失败: $input_file${NC}"
        rm -f "$temp_file"
        return 1
    fi
}

# 处理文件的函数
process_file() {
    local file="$1"
    local base_dir="$2"
    
    # 检查是否为PNG文件
    local filename=$(basename "$file")
    local extension="${filename##*.}"
    extension=$(echo "$extension" | tr '[:upper:]' '[:lower:]')
    if [[ "$extension" != "png" ]]; then
        return 0
    fi
    
    # 确定输出文件路径
    local output_file
    if [[ -n "$OUTPUT_DIR" ]]; then
        # 保持相对路径结构
        local relative_path="${file#$base_dir/}"
        output_file="$OUTPUT_DIR/$relative_path"
        
        # 创建输出目录结构
        local output_dir=$(dirname "$output_file")
        mkdir -p "$output_dir"
    else
        # 覆盖原文件
        output_file="$file"
    fi
    
    compress_png "$file" "$output_file"
}

# 统计变量
total_files=0
processed_files=0
failed_files=0
skipped_files=0
total_original_size=0
total_compressed_size=0

echo -e "${BLUE}开始PNG压缩任务${NC}"
echo -e "质量范围: $QUALITY"
echo -e "目标: $TARGET"
if [[ -n "$OUTPUT_DIR" ]]; then
    echo -e "输出目录: $OUTPUT_DIR"
else
    echo -e "模式: 覆盖原文件"
fi
if [[ "$RECURSIVE" == true ]]; then
    echo -e "递归模式: 启用"
fi
if [[ "$DRY_RUN" == true ]]; then
    echo -e "${YELLOW}预览模式: 启用（不会实际修改文件）${NC}"
fi
echo ""

# 处理目标
if [[ -f "$TARGET" ]]; then
    # 单个文件
    filename=$(basename "$TARGET")
    extension="${filename##*.}"
    extension=$(echo "$extension" | tr '[:upper:]' '[:lower:]')
    if [[ "$extension" == "png" ]]; then
        total_files=1
        if process_file "$TARGET" "$(dirname "$TARGET")"; then
            ((processed_files++))
        else
            ((failed_files++))
        fi
    else
        echo -e "${YELLOW}警告: $TARGET 不是PNG文件${NC}"
    fi
elif [[ -d "$TARGET" ]]; then
    # 目录
    if [[ "$RECURSIVE" == true ]]; then
        # 递归查找PNG文件
        while IFS= read -r -d '' file; do
            ((total_files++))
            if process_file "$file" "$TARGET"; then
                ((processed_files++))
            else
                ((failed_files++))
            fi
        done < <(find "$TARGET" -type f -iname "*.png" -print0)
    else
        # 只处理当前目录的PNG文件
        for file in "$TARGET"/*.png "$TARGET"/*.PNG; do
            if [[ -f "$file" ]]; then
                ((total_files++))
                if process_file "$file" "$TARGET"; then
                    ((processed_files++))
                else
                    ((failed_files++))
                fi
            fi
        done
    fi
fi

# 显示总结
echo -e "${BLUE}压缩任务完成${NC}"
echo -e "总文件数: $total_files"
echo -e "成功压缩: ${GREEN}$processed_files${NC}"
if [[ $skipped_files -gt 0 ]]; then
    echo -e "跳过文件: ${YELLOW}$skipped_files${NC} (压缩后会增大)"
fi
if [[ $failed_files -gt 0 ]]; then
    echo -e "失败文件: ${RED}$failed_files${NC}"
fi

# 显示大小统计
if [[ $total_original_size -gt 0 ]]; then
    echo ""
    echo -e "${BLUE}大小统计:${NC}"
    echo -e "压缩前总大小: $(numfmt --to=iec-i --suffix=B $total_original_size)"
    echo -e "压缩后总大小: $(numfmt --to=iec-i --suffix=B $total_compressed_size)"
    
    saved_size=$((total_original_size - total_compressed_size))
    saved_percentage=0
    if [[ $total_original_size -gt 0 ]]; then
        saved_percentage=$((saved_size * 100 / total_original_size))
    fi
    
    if [[ $saved_size -gt 0 ]]; then
        echo -e "节省空间: ${GREEN}$(numfmt --to=iec-i --suffix=B $saved_size) (${saved_percentage}%)${NC}"
    else
        echo -e "节省空间: ${YELLOW}0B (0%)${NC}"
    fi
fi

if [[ $total_files -eq 0 ]]; then
    echo -e "${YELLOW}未找到PNG文件${NC}"
    exit 1
elif [[ $failed_files -gt 0 ]]; then
    exit 1
else
    exit 0
fi