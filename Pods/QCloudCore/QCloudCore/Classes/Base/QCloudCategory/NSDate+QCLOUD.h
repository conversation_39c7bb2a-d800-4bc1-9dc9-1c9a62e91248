//
//  NSDate+QCLOUD.h
//  QCloudCore
//
//  Created by <PERSON><PERSON><PERSON>(李雪) on 2018/12/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSDate (QCLOUD)
+ (NSDate *)qcloud_calibrateTime;
+ (void)qcloud_setTimeDeviation:(NSTimeInterval)timeDeviation;
+ (NSTimeInterval)qcloud_getTimeDeviation;
+ (NSString *)qcloud_stringFromDate:(NSDate *)date;
+ (NSString *)qcloud_stringFromDate_24:(NSDate *)date;
+ (NSString *)qcloud_stringFromDate_24SSS:(NSDate *)date;
+ (NSDate *)localDate;
@end

NS_ASSUME_NONNULL_END
