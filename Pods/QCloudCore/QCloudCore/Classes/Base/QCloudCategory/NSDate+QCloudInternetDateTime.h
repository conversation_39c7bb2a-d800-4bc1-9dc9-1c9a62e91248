//
//  NSDate+QCloudInternetDateTime.h
//  MWFeedParser
//
//  Created by <PERSON> on 07/10/2010.
//  Copyright 2010 <PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>

// Formatting hints
typedef enum { QCloudDateFormatHintNone, QCloudDateFormatHintRFC822, QCloudDateFormatHintRFC3339 } DateFormatHint;

// A category to parse internet date & time strings
@interface NSDate (QCloudInternetDateTime)

// Get date from RFC3339 or RFC822 string
// - A format/specification hint can be used to speed up,
//   otherwise both will be attempted in order to get a date
+ (NSDate *)qcloud_dateFromInternetDateTimeString:(NSString *)dateString formatHint:(DateFormatHint)hint;

// Get date from a string using a specific date specification
+ (NSDate *)qcloud_dateFromRFC3339String:(NSString *)dateString;
+ (NSDate *)qcloud_dateFromRFC822String:(NSString *)dateString;

@end
