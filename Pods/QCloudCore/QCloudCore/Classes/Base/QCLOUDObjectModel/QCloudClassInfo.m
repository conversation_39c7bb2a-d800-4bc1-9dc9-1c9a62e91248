//
//  QCloudClassInfo.m
//  QCloudModel <https://github.com/ibireme/QCloudModel>
//
//  Created by ibireme on 15/5/9.
//  Copyright (c) 2015 ibireme.
//
//  This source code is licensed under the MIT-style license found in the
//  LICENSE file in the root directory of this source tree.
//

#import "QCloudClassInfo.h"
#import <objc/runtime.h>

QCloudEncodingType QCloudEncodingGetType(const char *typeEncoding) {
    char *type = (char *)typeEncoding;
    if (!type)
        return QCloudEncodingTypeUnknown;
    size_t len = strlen(type);
    if (len == 0)
        return QCloudEncodingTypeUnknown;

    QCloudEncodingType qualifier = 0;
    bool prefix = true;
    while (prefix) {
        switch (*type) {
            case 'r': {
                qualifier |= QCloudEncodingTypeQualifierConst;
                type++;
            } break;
            case 'n': {
                qualifier |= QCloudEncodingTypeQualifierIn;
                type++;
            } break;
            case 'N': {
                qualifier |= QCloudEncodingTypeQualifierInout;
                type++;
            } break;
            case 'o': {
                qualifier |= QCloudEncodingTypeQualifierOut;
                type++;
            } break;
            case 'O': {
                qualifier |= QCloudEncodingTypeQualifierBycopy;
                type++;
            } break;
            case 'R': {
                qualifier |= QCloudEncodingTypeQualifierByref;
                type++;
            } break;
            case 'V': {
                qualifier |= QCloudEncodingTypeQualifierOneway;
                type++;
            } break;
            default: {
                prefix = false;
            } break;
        }
    }

    len = strlen(type);
    if (len == 0)
        return QCloudEncodingTypeUnknown | qualifier;

    switch (*type) {
        case 'v':
            return QCloudEncodingTypeVoid | qualifier;
        case 'B':
            return QCloudEncodingTypeBool | qualifier;
        case 'c':
            return QCloudEncodingTypeInt8 | qualifier;
        case 'C':
            return QCloudEncodingTypeUInt8 | qualifier;
        case 's':
            return QCloudEncodingTypeInt16 | qualifier;
        case 'S':
            return QCloudEncodingTypeUInt16 | qualifier;
        case 'i':
            return QCloudEncodingTypeInt32 | qualifier;
        case 'I':
            return QCloudEncodingTypeUInt32 | qualifier;
        case 'l':
            return QCloudEncodingTypeInt32 | qualifier;
        case 'L':
            return QCloudEncodingTypeUInt32 | qualifier;
        case 'q':
            return QCloudEncodingTypeInt64 | qualifier;
        case 'Q':
            return QCloudEncodingTypeUInt64 | qualifier;
        case 'f':
            return QCloudEncodingTypeFloat | qualifier;
        case 'd':
            return QCloudEncodingTypeDouble | qualifier;
        case 'D':
            return QCloudEncodingTypeLongDouble | qualifier;
        case '#':
            return QCloudEncodingTypeClass | qualifier;
        case ':':
            return QCloudEncodingTypeSEL | qualifier;
        case '*':
            return QCloudEncodingTypeCString | qualifier;
        case '^':
            return QCloudEncodingTypePointer | qualifier;
        case '[':
            return QCloudEncodingTypeCArray | qualifier;
        case '(':
            return QCloudEncodingTypeUnion | qualifier;
        case '{':
            return QCloudEncodingTypeStruct | qualifier;
        case '@': {
            if (len == 2 && *(type + 1) == '?')
                return QCloudEncodingTypeBlock | qualifier;
            else
                return QCloudEncodingTypeObject | qualifier;
        }
        default:
            return QCloudEncodingTypeUnknown | qualifier;
    }
}

@implementation QCloudClassIvarInfo

- (instancetype)initWithIvar:(Ivar)ivar {
    if (!ivar)
        return nil;
    self = [super init];
    _ivar = ivar;
    const char *name = ivar_getName(ivar);
    if (name) {
        _name = [NSString stringWithUTF8String:name];
    }
    _offset = ivar_getOffset(ivar);
    const char *typeEncoding = ivar_getTypeEncoding(ivar);
    if (typeEncoding) {
        _typeEncoding = [NSString stringWithUTF8String:typeEncoding];
        _type = QCloudEncodingGetType(typeEncoding);
    }
    return self;
}

@end

@implementation QCloudClassMethodInfo

- (instancetype)initWithMethod:(Method)method {
    if (!method)
        return nil;
    self = [super init];
    _method = method;
    _sel = method_getName(method);
    _imp = method_getImplementation(method);
    const char *name = sel_getName(_sel);
    if (name) {
        _name = [NSString stringWithUTF8String:name];
    }
    const char *typeEncoding = method_getTypeEncoding(method);
    if (typeEncoding) {
        _typeEncoding = [NSString stringWithUTF8String:typeEncoding];
    }
    char *returnType = method_copyReturnType(method);
    if (returnType) {
        _returnTypeEncoding = [NSString stringWithUTF8String:returnType];
        free(returnType);
    }
    unsigned int argumentCount = method_getNumberOfArguments(method);
    if (argumentCount > 0) {
        NSMutableArray *argumentTypes = [NSMutableArray new];
        for (unsigned int i = 0; i < argumentCount; i++) {
            char *argumentType = method_copyArgumentType(method, i);
            NSString *type = argumentType ? [NSString stringWithUTF8String:argumentType] : nil;
            [argumentTypes addObject:type ? type : @""];
            if (argumentType)
                free(argumentType);
        }
        _argumentTypeEncodings = argumentTypes;
    }
    return self;
}

@end

@implementation QCloudClassPropertyInfo

- (instancetype)initWithProperty:(objc_property_t)property {
    if (!property)
        return nil;
    self = [super init];
    _property = property;
    const char *name = property_getName(property);
    if (name) {
        _name = [NSString stringWithUTF8String:name];
    }

    QCloudEncodingType type = 0;
    unsigned int attrCount;
    objc_property_attribute_t *attrs = property_copyAttributeList(property, &attrCount);
    for (unsigned int i = 0; i < attrCount; i++) {
        switch (attrs[i].name[0]) {
            case 'T': { // Type encoding
                if (attrs[i].value) {
                    _typeEncoding = [NSString stringWithUTF8String:attrs[i].value];
                    type = QCloudEncodingGetType(attrs[i].value);

                    if ((type & QCloudEncodingTypeMask) == QCloudEncodingTypeObject && _typeEncoding.length) {
                        NSScanner *scanner = [NSScanner scannerWithString:_typeEncoding];
                        if (![scanner scanString:@"@\"" intoString:NULL])
                            continue;

                        NSString *clsName = nil;
                        if ([scanner scanUpToCharactersFromSet:[NSCharacterSet characterSetWithCharactersInString:@"\"<"] intoString:&clsName]) {
                            if (clsName.length)
                                _cls = objc_getClass(clsName.UTF8String);
                        }

                        NSMutableArray *protocols = nil;
                        while ([scanner scanString:@"<" intoString:NULL]) {
                            NSString *protocol = nil;
                            if ([scanner scanUpToString:@">" intoString:&protocol]) {
                                if (protocol.length) {
                                    if (!protocols)
                                        protocols = [NSMutableArray new];
                                    [protocols addObject:protocol];
                                }
                            }
                            [scanner scanString:@">" intoString:NULL];
                        }
                        _protocols = protocols;
                    }
                }
            } break;
            case 'V': { // Instance variable
                if (attrs[i].value) {
                    _ivarName = [NSString stringWithUTF8String:attrs[i].value];
                }
            } break;
            case 'R': {
                type |= QCloudEncodingTypePropertyReadonly;
            } break;
            case 'C': {
                type |= QCloudEncodingTypePropertyCopy;
            } break;
            case '&': {
                type |= QCloudEncodingTypePropertyRetain;
            } break;
            case 'N': {
                type |= QCloudEncodingTypePropertyNonatomic;
            } break;
            case 'D': {
                type |= QCloudEncodingTypePropertyDynamic;
            } break;
            case 'W': {
                type |= QCloudEncodingTypePropertyWeak;
            } break;
            case 'G': {
                type |= QCloudEncodingTypePropertyCustomGetter;
                if (attrs[i].value) {
                    _getter = NSSelectorFromString([NSString stringWithUTF8String:attrs[i].value]);
                }
            } break;
            case 'S': {
                type |= QCloudEncodingTypePropertyCustomSetter;
                if (attrs[i].value) {
                    _setter = NSSelectorFromString([NSString stringWithUTF8String:attrs[i].value]);
                }
            } // break; commented for code coverage in next line
            default:
                break;
        }
    }
    if (attrs) {
        free(attrs);
        attrs = NULL;
    }

    _type = type;
    if (_name.length) {
        if (!_getter) {
            _getter = NSSelectorFromString(_name);
        }
        if (!_setter && _name.length) {
            _setter = NSSelectorFromString(
                [NSString stringWithFormat:@"set%@%@:", [_name substringToIndex:1].uppercaseString, [_name substringFromIndex:1]]);
        }
    }
    return self;
}

@end

@implementation QCloudClassInfo {
    BOOL _needUpdate;
}

- (instancetype)initWithClass:(Class)cls {
    if (!cls)
        return nil;
    self = [super init];
    _cls = cls;
    _superCls = class_getSuperclass(cls);
    _isMeta = class_isMetaClass(cls);
    if (!_isMeta) {
        _metaCls = objc_getMetaClass(class_getName(cls));
    }
    _name = NSStringFromClass(cls);
    [self _update];

    _superClassInfo = [self.class classInfoWithClass:_superCls];
    return self;
}

- (void)_update {
    _ivarInfos = nil;
    _methodInfos = nil;
    _propertyInfos = nil;

    Class cls = self.cls;
    unsigned int methodCount = 0;
    Method *methods = class_copyMethodList(cls, &methodCount);
    if (methods) {
        NSMutableDictionary *methodInfos = [NSMutableDictionary new];
        _methodInfos = methodInfos;
        for (unsigned int i = 0; i < methodCount; i++) {
            QCloudClassMethodInfo *info = [[QCloudClassMethodInfo alloc] initWithMethod:methods[i]];
            if (info.name)
                methodInfos[info.name] = info;
        }
        free(methods);
    }
    unsigned int propertyCount = 0;
    objc_property_t *properties = class_copyPropertyList(cls, &propertyCount);
    if (properties) {
        NSMutableDictionary *propertyInfos = [NSMutableDictionary new];
        _propertyInfos = propertyInfos;
        for (unsigned int i = 0; i < propertyCount; i++) {
            QCloudClassPropertyInfo *info = [[QCloudClassPropertyInfo alloc] initWithProperty:properties[i]];
            if (info.name)
                propertyInfos[info.name] = info;
        }
        free(properties);
    }

    unsigned int ivarCount = 0;
    Ivar *ivars = class_copyIvarList(cls, &ivarCount);
    if (ivars) {
        NSMutableDictionary *ivarInfos = [NSMutableDictionary new];
        _ivarInfos = ivarInfos;
        for (unsigned int i = 0; i < ivarCount; i++) {
            QCloudClassIvarInfo *info = [[QCloudClassIvarInfo alloc] initWithIvar:ivars[i]];
            if (info.name)
                ivarInfos[info.name] = info;
        }
        free(ivars);
    }

    if (!_ivarInfos)
        _ivarInfos = @{};
    if (!_methodInfos)
        _methodInfos = @{};
    if (!_propertyInfos)
        _propertyInfos = @{};

    _needUpdate = NO;
}

- (void)setNeedUpdate {
    _needUpdate = YES;
}

- (BOOL)needUpdate {
    return _needUpdate;
}

+ (instancetype)classInfoWithClass:(Class)cls {
    if (!cls)
        return nil;
    static CFMutableDictionaryRef classCache;
    static CFMutableDictionaryRef metaCache;
    static dispatch_once_t onceToken;
    static dispatch_semaphore_t lock;
    dispatch_once(&onceToken, ^{
        classCache = CFDictionaryCreateMutable(CFAllocatorGetDefault(), 0, &kCFTypeDictionaryKeyCallBacks, &kCFTypeDictionaryValueCallBacks);
        metaCache = CFDictionaryCreateMutable(CFAllocatorGetDefault(), 0, &kCFTypeDictionaryKeyCallBacks, &kCFTypeDictionaryValueCallBacks);
        lock = dispatch_semaphore_create(1);
    });
    dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
    QCloudClassInfo *info = CFDictionaryGetValue(class_isMetaClass(cls) ? metaCache : classCache, (__bridge const void *)(cls));
    if (info && info->_needUpdate) {
        [info _update];
    }
    dispatch_semaphore_signal(lock);
    if (!info) {
        info = [[QCloudClassInfo alloc] initWithClass:cls];
        if (info) {
            dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
            CFDictionarySetValue(info.isMeta ? metaCache : classCache, (__bridge const void *)(cls), (__bridge const void *)(info));
            dispatch_semaphore_signal(lock);
        }
    }
    return info;
}

+ (instancetype)classInfoWithClassName:(NSString *)className {
    Class cls = NSClassFromString(className);
    return [self classInfoWithClass:cls];
}

@end
