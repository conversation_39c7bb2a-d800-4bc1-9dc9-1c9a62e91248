//
//  NSObject+QCloudModel.m
//  QCloudModel <https://github.com/ibireme/QCloudModel>
//
//  Created by ibireme on 15/5/10.
//  Copyright (c) 2015 ibireme.
//
//  This source code is licensed under the MIT-style license found in the
//  LICENSE file in the root directory of this source tree.
//

#import "NSObject+QCloudModel.h"
#import "QCloudClassInfo.h"
#import <objc/message.h>

#define force_inline __inline__ __attribute__((always_inline))

/// Foundation Class Type
typedef NS_ENUM(NSUInteger, QCloudEncodingNSType) {
    QCloudEncodingTypeNSUnknown = 0,
    QCloudEncodingTypeNSString,
    QCloudEncodingTypeNSMutableString,
    QCloudEncodingTypeNSValue,
    QCloudEncodingTypeNSNumber,
    QCloudEncodingTypeNSDecimalNumber,
    QCloudEncodingTypeNSData,
    QCloudEncodingTypeNSMutableData,
    QCloudEncodingTypeNSDate,
    QCloudEncodingTypeNSURL,
    QCloudEncodingTypeNSArray,
    QCloudEncodingTypeNSMutableArray,
    QCloudEncodingTypeNSDictionary,
    QCloudEncodingTypeNSMutableDictionary,
    QCloudEncodingTypeNSSet,
    QCloudEncodingTypeNSMutableSet,
};

/// Get the Foundation class type from property info.
static force_inline QCloudEncodingNSType QCloudClassGetNSType(Class cls) {
    if (!cls)
        return QCloudEncodingTypeNSUnknown;
    if ([cls isSubclassOfClass:[NSMutableString class]])
        return QCloudEncodingTypeNSMutableString;
    if ([cls isSubclassOfClass:[NSString class]])
        return QCloudEncodingTypeNSString;
    if ([cls isSubclassOfClass:[NSDecimalNumber class]])
        return QCloudEncodingTypeNSDecimalNumber;
    if ([cls isSubclassOfClass:[NSNumber class]])
        return QCloudEncodingTypeNSNumber;
    if ([cls isSubclassOfClass:[NSValue class]])
        return QCloudEncodingTypeNSValue;
    if ([cls isSubclassOfClass:[NSMutableData class]])
        return QCloudEncodingTypeNSMutableData;
    if ([cls isSubclassOfClass:[NSData class]])
        return QCloudEncodingTypeNSData;
    if ([cls isSubclassOfClass:[NSDate class]])
        return QCloudEncodingTypeNSDate;
    if ([cls isSubclassOfClass:[NSURL class]])
        return QCloudEncodingTypeNSURL;
    if ([cls isSubclassOfClass:[NSMutableArray class]])
        return QCloudEncodingTypeNSMutableArray;
    if ([cls isSubclassOfClass:[NSArray class]])
        return QCloudEncodingTypeNSArray;
    if ([cls isSubclassOfClass:[NSMutableDictionary class]])
        return QCloudEncodingTypeNSMutableDictionary;
    if ([cls isSubclassOfClass:[NSDictionary class]])
        return QCloudEncodingTypeNSDictionary;
    if ([cls isSubclassOfClass:[NSMutableSet class]])
        return QCloudEncodingTypeNSMutableSet;
    if ([cls isSubclassOfClass:[NSSet class]])
        return QCloudEncodingTypeNSSet;
    return QCloudEncodingTypeNSUnknown;
}

/// Whether the type is c number.
static force_inline BOOL QCloudEncodingTypeIsCNumber(QCloudEncodingType type) {
    switch (type & QCloudEncodingTypeMask) {
        case QCloudEncodingTypeBool:
        case QCloudEncodingTypeInt8:
        case QCloudEncodingTypeUInt8:
        case QCloudEncodingTypeInt16:
        case QCloudEncodingTypeUInt16:
        case QCloudEncodingTypeInt32:
        case QCloudEncodingTypeUInt32:
        case QCloudEncodingTypeInt64:
        case QCloudEncodingTypeUInt64:
        case QCloudEncodingTypeFloat:
        case QCloudEncodingTypeDouble:
        case QCloudEncodingTypeLongDouble:
            return YES;
        default:
            return NO;
    }
}

/// Parse a number value from 'id'.
static force_inline NSNumber *QCloudNSNumberCreateFromID(__unsafe_unretained id value) {
    static NSCharacterSet *dot;
    static NSDictionary *dic;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        dot = [NSCharacterSet characterSetWithRange:NSMakeRange('.', 1)];
        dic = @{
            @"TRUE" : @(YES),
            @"True" : @(YES),
            @"true" : @(YES),
            @"FALSE" : @(NO),
            @"False" : @(NO),
            @"false" : @(NO),
            @"YES" : @(YES),
            @"Yes" : @(YES),
            @"yes" : @(YES),
            @"NO" : @(NO),
            @"No" : @(NO),
            @"no" : @(NO),
            @"NIL" : (id)kCFNull,
            @"Nil" : (id)kCFNull,
            @"nil" : (id)kCFNull,
            @"NULL" : (id)kCFNull,
            @"Null" : (id)kCFNull,
            @"null" : (id)kCFNull,
            @"(NULL)" : (id)kCFNull,
            @"(Null)" : (id)kCFNull,
            @"(null)" : (id)kCFNull,
            @"<NULL>" : (id)kCFNull,
            @"<Null>" : (id)kCFNull,
            @"<null>" : (id)kCFNull
        };
    });

    if (!value || value == (id)kCFNull)
        return nil;
    if ([value isKindOfClass:[NSNumber class]])
        return value;
    if ([value isKindOfClass:[NSString class]]) {
        NSNumber *num = dic[value];
        if (num != nil) {
            if (num == (id)kCFNull)
                return nil;
            return num;
        }
        if ([(NSString *)value rangeOfCharacterFromSet:dot].location != NSNotFound) {
            const char *cstring = ((NSString *)value).UTF8String;
            if (!cstring)
                return nil;
            double num = atof(cstring);
            if (isnan(num) || isinf(num))
                return nil;
            return @(num);
        } else {
            const char *cstring = ((NSString *)value).UTF8String;
            if (!cstring)
                return nil;
            return @(atoll(cstring));
        }
    }
    return nil;
}

/// Parse string to date.
static force_inline NSDate *QCloudNSDateFromString(__unsafe_unretained NSString *string) {
    typedef NSDate * (^QCloudNSDateParseBlock)(NSString *string);
#define kParserNum 34
    static QCloudNSDateParseBlock blocks[kParserNum + 1] = { 0 };
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        {
            /*
             2014-01-20  // Google
             */
            NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
            formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
            formatter.timeZone = [NSTimeZone timeZoneForSecondsFromGMT:0];
            formatter.dateFormat = @"yyyy-MM-dd";
            blocks[10] = ^(NSString *string) {
        return [formatter dateFromString:string]; };
}

{
    /*
     2014-01-20 12:24:48
     2014-01-20T12:24:48   // Google
     2014-01-20 12:24:48.000
     2014-01-20T12:24:48.000
     */
    NSDateFormatter *formatter1 = [[NSDateFormatter alloc] init];
    formatter1.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter1.timeZone = [NSTimeZone timeZoneForSecondsFromGMT:0];
    formatter1.dateFormat = @"yyyy-MM-dd'T'HH:mm:ss";

    NSDateFormatter *formatter2 = [[NSDateFormatter alloc] init];
    formatter2.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter2.timeZone = [NSTimeZone timeZoneForSecondsFromGMT:0];
    formatter2.dateFormat = @"yyyy-MM-dd HH:mm:ss";

    NSDateFormatter *formatter3 = [[NSDateFormatter alloc] init];
    formatter3.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter3.timeZone = [NSTimeZone timeZoneForSecondsFromGMT:0];
    formatter3.dateFormat = @"yyyy-MM-dd'T'HH:mm:ss.SSS";

    NSDateFormatter *formatter4 = [[NSDateFormatter alloc] init];
    formatter4.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter4.timeZone = [NSTimeZone timeZoneForSecondsFromGMT:0];
    formatter4.dateFormat = @"yyyy-MM-dd HH:mm:ss.SSS";

    blocks[19] = ^(NSString *string) {
        if ([string characterAtIndex:10] == 'T') {
            return [formatter1 dateFromString:string];
        } else {
            return [formatter2 dateFromString:string];
        }
    };

    blocks[23] = ^(NSString *string) {
        if ([string characterAtIndex:10] == 'T') {
            return [formatter3 dateFromString:string];
        } else {
            return [formatter4 dateFromString:string];
        }
    };
}

{
    /*
     2014-01-20T12:24:48Z        // Github, Apple
     2014-01-20T12:24:48+0800    // Facebook
     2014-01-20T12:24:48+12:00   // Google
     2014-01-20T12:24:48.000Z
     2014-01-20T12:24:48.000+0800
     2014-01-20T12:24:48.000+12:00
     */
    NSDateFormatter *formatter = [NSDateFormatter new];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter.dateFormat = @"yyyy-MM-dd'T'HH:mm:ssZ";

    NSDateFormatter *formatter2 = [NSDateFormatter new];
    formatter2.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter2.dateFormat = @"yyyy-MM-dd'T'HH:mm:ss.SSSZ";

    blocks[20] = ^(NSString *string) {
        return [formatter dateFromString:string];
    };
    blocks[24] = ^(NSString *string) {
        return [formatter dateFromString:string] ?: [formatter2 dateFromString:string];
    };
    blocks[25] = ^(NSString *string) {
        return [formatter dateFromString:string];
    };
    blocks[28] = ^(NSString *string) {
        return [formatter2 dateFromString:string];
    };
    blocks[29] = ^(NSString *string) {
        return [formatter2 dateFromString:string];
    };
}

{
    /*
     Fri Sep 04 00:12:21 +0800 2015 // Weibo, Twitter
     Fri Sep 04 00:12:21.000 +0800 2015
     */
    NSDateFormatter *formatter = [NSDateFormatter new];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter.dateFormat = @"EEE MMM dd HH:mm:ss Z yyyy";

    NSDateFormatter *formatter2 = [NSDateFormatter new];
    formatter2.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
    formatter2.dateFormat = @"EEE MMM dd HH:mm:ss.SSS Z yyyy";

    blocks[30] = ^(NSString *string) {
        return [formatter dateFromString:string];
    };
    blocks[34] = ^(NSString *string) {
        return [formatter2 dateFromString:string];
    };
}
});
if (!string)
    return nil;
if (string.length > kParserNum)
    return nil;
QCloudNSDateParseBlock parser = blocks[string.length];
if (!parser)
    return nil;
return parser(string);
#undef kParserNum
}

/// Get the 'NSBlock' class.
static force_inline Class QCloudNSBlockClass() {
    static Class cls;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        void (^block)(void) = ^{
        };
        cls = ((NSObject *)block).class;
        while (class_getSuperclass(cls) != [NSObject class]) {
            cls = class_getSuperclass(cls);
        }
    });
    return cls; // current is "NSBlock"
}

/**
 Get the ISO date formatter.

 ISO8601 format example:
 2010-07-09T16:13:30+12:00
 2011-01-11T11:11:11+0000
 2011-01-26T19:06:43Z

 length: 20/24/25
 */
static force_inline NSDateFormatter *QCloudISODateFormatter() {
    static NSDateFormatter *formatter = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        formatter = [[NSDateFormatter alloc] init];
        formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"];
        formatter.dateFormat = @"yyyy-MM-dd'T'HH:mm:ssZ";
    });
    return formatter;
}

/// Get the value with key paths from dictionary
/// The dic should be NSDictionary, and the keyPath should not be nil.
static force_inline id QCloudValueForKeyPath(__unsafe_unretained NSDictionary *dic, __unsafe_unretained NSArray *keyPaths) {
    id value = nil;
    for (NSUInteger i = 0, max = keyPaths.count; i < max; i++) {
        value = dic[keyPaths[i]];
        if (i + 1 < max) {
            if ([value isKindOfClass:[NSDictionary class]]) {
                dic = value;
            } else {
                return nil;
            }
        }
    }
    return value;
}

/// Get the value with multi key (or key path) from dictionary
/// The dic should be NSDictionary
static force_inline id QCloudValueForMultiKeys(__unsafe_unretained NSDictionary *dic, __unsafe_unretained NSArray *multiKeys) {
    id value = nil;
    for (NSString *key in multiKeys) {
        if ([key isKindOfClass:[NSString class]]) {
            value = dic[key];
            if (value)
                break;
        } else {
            value = QCloudValueForKeyPath(dic, (NSArray *)key);
            if (value)
                break;
        }
    }
    return value;
}

/// A property info in object model.
@interface _QCloudModelPropertyMeta : NSObject {
@package
    NSString *_name;                         ///< property's name
    QCloudEncodingType _type;                ///< property's type
    QCloudEncodingNSType _nsType;            ///< property's Foundation type
    BOOL _isCNumber;                         ///< is c number type
    Class _cls;                              ///< property's class, or nil
    Class _genericCls;                       ///< container's generic class, or nil if threr's no generic class
    SEL _getter;                             ///< getter, or nil if the instances cannot respond
    SEL _setter;                             ///< setter, or nil if the instances cannot respond
    BOOL _isKVCCompatible;                   ///< YES if it can access with key-value coding
    BOOL _isStructAvailableForKeyedArchiver; ///< YES if the struct can encoded with keyed archiver/unarchiver
    BOOL _hasCustomClassFromDictionary;      ///< class/generic class implements +modelCustomClassForDictionary:

    /*
     property->key:       _mappedToKey:key     _mappedToKeyPath:nil            _mappedToKeyArray:nil
     property->keyPath:   _mappedToKey:keyPath _mappedToKeyPath:keyPath(array) _mappedToKeyArray:nil
     property->keys:      _mappedToKey:keys[0] _mappedToKeyPath:nil/keyPath    _mappedToKeyArray:keys(array)
     */
    NSString *_mappedToKey;          ///< the key mapped to
    NSArray *_mappedToKeyPath;       ///< the key path mapped to (nil if the name is not key path)
    NSArray *_mappedToKeyArray;      ///< the key(NSString) or keyPath(NSArray) array (nil if not mapped to multiple keys)
    QCloudClassPropertyInfo *_info;  ///< property's info
    _QCloudModelPropertyMeta *_next; ///< next meta if there are multiple properties mapped to the same key.
}
@end

@implementation _QCloudModelPropertyMeta
+ (instancetype)metaWithClassInfo:(QCloudClassInfo *)classInfo propertyInfo:(QCloudClassPropertyInfo *)propertyInfo generic:(Class)generic {
    // support pseudo generic class with protocol name
    if (!generic && propertyInfo.protocols) {
        for (NSString *protocol in propertyInfo.protocols) {
            Class cls = objc_getClass(protocol.UTF8String);
            if (cls) {
                generic = cls;
                break;
            }
        }
    }

    _QCloudModelPropertyMeta *meta = [self new];
    meta->_name = propertyInfo.name;
    meta->_type = propertyInfo.type;
    meta->_info = propertyInfo;
    meta->_genericCls = generic;

    if ((meta->_type & QCloudEncodingTypeMask) == QCloudEncodingTypeObject) {
        meta->_nsType = QCloudClassGetNSType(propertyInfo.cls);
    } else {
        meta->_isCNumber = QCloudEncodingTypeIsCNumber(meta->_type);
    }
    if ((meta->_type & QCloudEncodingTypeMask) == QCloudEncodingTypeStruct) {
        /*
         It seems that NSKeyedUnarchiver cannot decode NSValue except these structs:
         */
        static NSSet *types = nil;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            NSMutableSet *set = [NSMutableSet new];
            // 32 bit
            [set addObject:@"{CGSize=ff}"];
            [set addObject:@"{CGPoint=ff}"];
            [set addObject:@"{CGRect={CGPoint=ff}{CGSize=ff}}"];
            [set addObject:@"{CGAffineTransform=ffffff}"];
            [set addObject:@"{UIEdgeInsets=ffff}"];
            [set addObject:@"{UIOffset=ff}"];
            // 64 bit
            [set addObject:@"{CGSize=dd}"];
            [set addObject:@"{CGPoint=dd}"];
            [set addObject:@"{CGRect={CGPoint=dd}{CGSize=dd}}"];
            [set addObject:@"{CGAffineTransform=dddddd}"];
            [set addObject:@"{UIEdgeInsets=dddd}"];
            [set addObject:@"{UIOffset=dd}"];
            types = set;
        });
        if ([types containsObject:propertyInfo.typeEncoding]) {
            meta->_isStructAvailableForKeyedArchiver = YES;
        }
    }
    meta->_cls = propertyInfo.cls;

    if (generic) {
        meta->_hasCustomClassFromDictionary = [generic respondsToSelector:@selector(modelCustomClassForDictionary:)];
    } else if (meta->_cls && meta->_nsType == QCloudEncodingTypeNSUnknown) {
        meta->_hasCustomClassFromDictionary = [meta->_cls respondsToSelector:@selector(modelCustomClassForDictionary:)];
    }

    if (propertyInfo.getter) {
        if ([classInfo.cls instancesRespondToSelector:propertyInfo.getter]) {
            meta->_getter = propertyInfo.getter;
        }
    }
    if (propertyInfo.setter) {
        if ([classInfo.cls instancesRespondToSelector:propertyInfo.setter]) {
            meta->_setter = propertyInfo.setter;
        }
    }

    if (meta->_getter && meta->_setter) {
        /*
         KVC invalid type:
         long double
         pointer (such as SEL/CoreFoundation object)
         */
        switch (meta->_type & QCloudEncodingTypeMask) {
            case QCloudEncodingTypeBool:
            case QCloudEncodingTypeInt8:
            case QCloudEncodingTypeUInt8:
            case QCloudEncodingTypeInt16:
            case QCloudEncodingTypeUInt16:
            case QCloudEncodingTypeInt32:
            case QCloudEncodingTypeUInt32:
            case QCloudEncodingTypeInt64:
            case QCloudEncodingTypeUInt64:
            case QCloudEncodingTypeFloat:
            case QCloudEncodingTypeDouble:
            case QCloudEncodingTypeObject:
            case QCloudEncodingTypeClass:
            case QCloudEncodingTypeBlock:
            case QCloudEncodingTypeStruct:
            case QCloudEncodingTypeUnion: {
                meta->_isKVCCompatible = YES;
            } break;
            default:
                break;
        }
    }

    return meta;
}
@end

/// A class info in object model.
@interface _QCloudModelMeta : NSObject {
@package
    QCloudClassInfo *_classInfo;
    /// Key:mapped key and key path, Value:_QCloudModelPropertyMeta.
    NSDictionary *_mapper;
    /// Array<_QCloudModelPropertyMeta>, all property meta of this model.
    NSArray *_allPropertyMetas;
    /// Array<_QCloudModelPropertyMeta>, property meta which is mapped to a key path.
    NSArray *_keyPathPropertyMetas;
    /// Array<_QCloudModelPropertyMeta>, property meta which is mapped to multi keys.
    NSArray *_multiKeysPropertyMetas;
    /// The number of mapped key (and key path), same to _mapper.count.
    NSUInteger _keyMappedCount;
    /// Model class type.
    QCloudEncodingNSType _nsType;

    BOOL _hasCustomWillTransformFromDictionary;
    BOOL _hasCustomTransformFromDictionary;
    BOOL _hasCustomTransformToDictionary;
    BOOL _hasCustomClassFromDictionary;
}
@end

@implementation _QCloudModelMeta
- (instancetype)initWithClass:(Class)cls {
    QCloudClassInfo *classInfo = [QCloudClassInfo classInfoWithClass:cls];
    if (!classInfo)
        return nil;
    self = [super init];

    // Get black list
    NSSet *blacklist = nil;
    if ([cls respondsToSelector:@selector(modelPropertyBlacklist)]) {
        NSArray *properties = [(id<QCloudModel>)cls modelPropertyBlacklist];
        if (properties) {
            blacklist = [NSSet setWithArray:properties];
        }
    }

    // Get white list
    NSSet *whitelist = nil;
    if ([cls respondsToSelector:@selector(modelPropertyWhitelist)]) {
        NSArray *properties = [(id<QCloudModel>)cls modelPropertyWhitelist];
        if (properties) {
            whitelist = [NSSet setWithArray:properties];
        }
    }

    // Get container property's generic class
    NSDictionary *genericMapper = nil;
    if ([cls respondsToSelector:@selector(modelContainerPropertyGenericClass)]) {
        genericMapper = [(id<QCloudModel>)cls modelContainerPropertyGenericClass];
        if (genericMapper) {
            NSMutableDictionary *tmp = [NSMutableDictionary new];
            [genericMapper enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
                if (![key isKindOfClass:[NSString class]])
                    return;
                Class meta = object_getClass(obj);
                if (!meta)
                    return;
                if (class_isMetaClass(meta)) {
                    tmp[key] = obj;
                } else if ([obj isKindOfClass:[NSString class]]) {
                    Class cls = NSClassFromString(obj);
                    if (cls) {
                        tmp[key] = cls;
                    }
                }
            }];
            genericMapper = tmp;
        }
    }

    // Create all property metas.
    NSMutableDictionary *allPropertyMetas = [NSMutableDictionary new];
    QCloudClassInfo *curClassInfo = classInfo;
    while (curClassInfo && curClassInfo.superCls != nil) { // recursive parse super class, but ignore root class (NSObject/NSProxy)
        for (QCloudClassPropertyInfo *propertyInfo in curClassInfo.propertyInfos.allValues) {
            if (!propertyInfo.name)
                continue;
            if (blacklist && [blacklist containsObject:propertyInfo.name])
                continue;
            if (whitelist && ![whitelist containsObject:propertyInfo.name])
                continue;
            _QCloudModelPropertyMeta *meta = [_QCloudModelPropertyMeta metaWithClassInfo:classInfo
                                                                            propertyInfo:propertyInfo
                                                                                 generic:genericMapper[propertyInfo.name]];
            if (!meta || !meta->_name)
                continue;
            if (!meta->_getter || !meta->_setter)
                continue;
            if (allPropertyMetas[meta->_name])
                continue;
            allPropertyMetas[meta->_name] = meta;
        }
        curClassInfo = curClassInfo.superClassInfo;
    }
    if (allPropertyMetas.count)
        _allPropertyMetas = allPropertyMetas.allValues.copy;

    // create mapper
    NSMutableDictionary *mapper = [NSMutableDictionary new];
    NSMutableArray *keyPathPropertyMetas = [NSMutableArray new];
    NSMutableArray *multiKeysPropertyMetas = [NSMutableArray new];

    if ([cls respondsToSelector:@selector(modelCustomPropertyMapper)]) {
        NSDictionary *customMapper = [(id<QCloudModel>)cls modelCustomPropertyMapper];
        [customMapper enumerateKeysAndObjectsUsingBlock:^(NSString *propertyName, NSString *mappedToKey, BOOL *stop) {
            _QCloudModelPropertyMeta *propertyMeta = allPropertyMetas[propertyName];
            if (!propertyMeta)
                return;
            [allPropertyMetas removeObjectForKey:propertyName];

            if ([mappedToKey isKindOfClass:[NSString class]]) {
                if (mappedToKey.length == 0)
                    return;

                propertyMeta->_mappedToKey = mappedToKey;
                NSArray *keyPath = [mappedToKey componentsSeparatedByString:@"."];
                for (NSString *onePath in keyPath) {
                    if (onePath.length == 0) {
                        NSMutableArray *tmp = keyPath.mutableCopy;
                        [tmp removeObject:@""];
                        keyPath = tmp;
                        break;
                    }
                }
                if (keyPath.count > 1) {
                    propertyMeta->_mappedToKeyPath = keyPath;
                    [keyPathPropertyMetas addObject:propertyMeta];
                }
                propertyMeta->_next = mapper[mappedToKey] ?: nil;
                mapper[mappedToKey] = propertyMeta;

            } else if ([mappedToKey isKindOfClass:[NSArray class]]) {
                NSMutableArray *mappedToKeyArray = [NSMutableArray new];
                for (NSString *oneKey in ((NSArray *)mappedToKey)) {
                    if (![oneKey isKindOfClass:[NSString class]])
                        continue;
                    if (oneKey.length == 0)
                        continue;

                    NSArray *keyPath = [oneKey componentsSeparatedByString:@"."];
                    if (keyPath.count > 1) {
                        [mappedToKeyArray addObject:keyPath];
                    } else {
                        [mappedToKeyArray addObject:oneKey];
                    }

                    if (!propertyMeta->_mappedToKey) {
                        propertyMeta->_mappedToKey = oneKey;
                        propertyMeta->_mappedToKeyPath = keyPath.count > 1 ? keyPath : nil;
                    }
                }
                if (!propertyMeta->_mappedToKey)
                    return;

                propertyMeta->_mappedToKeyArray = mappedToKeyArray;
                [multiKeysPropertyMetas addObject:propertyMeta];

                propertyMeta->_next = mapper[mappedToKey] ?: nil;
                mapper[mappedToKey] = propertyMeta;
            }
        }];
    }

    [allPropertyMetas enumerateKeysAndObjectsUsingBlock:^(NSString *name, _QCloudModelPropertyMeta *propertyMeta, BOOL *stop) {
        propertyMeta->_mappedToKey = name;
        propertyMeta->_next = mapper[name] ?: nil;
        mapper[name] = propertyMeta;
    }];

    if (mapper.count)
        _mapper = mapper;
    if (keyPathPropertyMetas)
        _keyPathPropertyMetas = keyPathPropertyMetas;
    if (multiKeysPropertyMetas)
        _multiKeysPropertyMetas = multiKeysPropertyMetas;

    _classInfo = classInfo;
    _keyMappedCount = _allPropertyMetas.count;
    _nsType = QCloudClassGetNSType(cls);
    _hasCustomWillTransformFromDictionary = ([cls instancesRespondToSelector:@selector(modelCustomWillTransformFromDictionary:)]);
    _hasCustomTransformFromDictionary = ([cls instancesRespondToSelector:@selector(modelCustomTransformFromDictionary:)]);
    _hasCustomTransformToDictionary = ([cls instancesRespondToSelector:@selector(modelCustomTransformToDictionary:)]);
    _hasCustomClassFromDictionary = ([cls respondsToSelector:@selector(modelCustomClassForDictionary:)]);

    return self;
}

/// Returns the cached model class meta
+ (instancetype)metaWithClass:(Class)cls {
    if (!cls)
        return nil;
    static CFMutableDictionaryRef cache;
    static dispatch_once_t onceToken;
    static dispatch_semaphore_t lock;
    dispatch_once(&onceToken, ^{
        cache = CFDictionaryCreateMutable(CFAllocatorGetDefault(), 0, &kCFTypeDictionaryKeyCallBacks, &kCFTypeDictionaryValueCallBacks);
        lock = dispatch_semaphore_create(1);
    });
    dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
    _QCloudModelMeta *meta = CFDictionaryGetValue(cache, (__bridge const void *)(cls));
    dispatch_semaphore_signal(lock);
    if (!meta || meta->_classInfo.needUpdate) {
        meta = [[_QCloudModelMeta alloc] initWithClass:cls];
        if (meta) {
            dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
            CFDictionarySetValue(cache, (__bridge const void *)(cls), (__bridge const void *)(meta));
            dispatch_semaphore_signal(lock);
        }
    }
    return meta;
}

@end

/**
 Get number from property.
 @discussion Caller should hold strong reference to the parameters before this function returns.
 @param model Should not be nil.
 @param meta  Should not be nil, meta.isCNumber should be YES, meta.getter should not be nil.
 @return A number object, or nil if failed.
 */
static force_inline NSNumber *qcloudModelCreateNumberFromProperty(__unsafe_unretained id model, __unsafe_unretained _QCloudModelPropertyMeta *meta) {
    switch (meta->_type & QCloudEncodingTypeMask) {
        case QCloudEncodingTypeBool: {
            return @(((bool (*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeInt8: {
            return @(((int8_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeUInt8: {
            return @(((uint8_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeInt16: {
            return @(((int16_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeUInt16: {
            return @(((uint16_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeInt32: {
            return @(((int32_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeUInt32: {
            return @(((uint32_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeInt64: {
            return @(((int64_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeUInt64: {
            return @(((uint64_t(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter));
        }
        case QCloudEncodingTypeFloat: {
            float num = ((float (*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter);
            if (isnan(num) || isinf(num))
                return nil;
            return @(num);
        }
        case QCloudEncodingTypeDouble: {
            double num = ((double (*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter);
            if (isnan(num) || isinf(num))
                return nil;
            return @(num);
        }
        case QCloudEncodingTypeLongDouble: {
            double num = ((long double (*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter);
            if (isnan(num) || isinf(num))
                return nil;
            return @(num);
        }
        default:
            return nil;
    }
}

/**
 Set number to property.
 @discussion Caller should hold strong reference to the parameters before this function returns.
 @param model Should not be nil.
 @param num   Can be nil.
 @param meta  Should not be nil, meta.isCNumber should be YES, meta.setter should not be nil.
 */
static force_inline void qcloudModelSetNumberToProperty(__unsafe_unretained id model, __unsafe_unretained NSNumber *num,
                                                  __unsafe_unretained _QCloudModelPropertyMeta *meta) {
    switch (meta->_type & QCloudEncodingTypeMask) {
        case QCloudEncodingTypeBool: {
            ((void (*)(id, SEL, bool))(void *)objc_msgSend)((id)model, meta->_setter, num.boolValue);
        } break;
        case QCloudEncodingTypeInt8: {
            ((void (*)(id, SEL, int8_t))(void *)objc_msgSend)((id)model, meta->_setter, (int8_t)num.charValue);
        } break;
        case QCloudEncodingTypeUInt8: {
            ((void (*)(id, SEL, uint8_t))(void *)objc_msgSend)((id)model, meta->_setter, (uint8_t)num.unsignedCharValue);
        } break;
        case QCloudEncodingTypeInt16: {
            ((void (*)(id, SEL, int16_t))(void *)objc_msgSend)((id)model, meta->_setter, (int16_t)num.shortValue);
        } break;
        case QCloudEncodingTypeUInt16: {
            ((void (*)(id, SEL, uint16_t))(void *)objc_msgSend)((id)model, meta->_setter, (uint16_t)num.unsignedShortValue);
        } break;
        case QCloudEncodingTypeInt32: {
            ((void (*)(id, SEL, int32_t))(void *)objc_msgSend)((id)model, meta->_setter, (int32_t)num.intValue);
        } break;
        case QCloudEncodingTypeUInt32: {
            ((void (*)(id, SEL, uint32_t))(void *)objc_msgSend)((id)model, meta->_setter, (uint32_t)num.unsignedIntValue);
        } break;
        case QCloudEncodingTypeInt64: {
            if ([num isKindOfClass:[NSDecimalNumber class]]) {
                ((void (*)(id, SEL, int64_t))(void *)objc_msgSend)((id)model, meta->_setter, (int64_t)num.stringValue.longLongValue);
            } else {
                ((void (*)(id, SEL, uint64_t))(void *)objc_msgSend)((id)model, meta->_setter, (uint64_t)num.longLongValue);
            }
        } break;
        case QCloudEncodingTypeUInt64: {
            if ([num isKindOfClass:[NSDecimalNumber class]]) {
                ((void (*)(id, SEL, int64_t))(void *)objc_msgSend)((id)model, meta->_setter, (int64_t)num.stringValue.longLongValue);
            } else {
                ((void (*)(id, SEL, uint64_t))(void *)objc_msgSend)((id)model, meta->_setter, (uint64_t)num.unsignedLongLongValue);
            }
        } break;
        case QCloudEncodingTypeFloat: {
            float f = num.floatValue;
            if (isnan(f) || isinf(f))
                f = 0;
            ((void (*)(id, SEL, float))(void *)objc_msgSend)((id)model, meta->_setter, f);
        } break;
        case QCloudEncodingTypeDouble: {
            double d = num.doubleValue;
            if (isnan(d) || isinf(d))
                d = 0;
            ((void (*)(id, SEL, double))(void *)objc_msgSend)((id)model, meta->_setter, d);
        } break;
        case QCloudEncodingTypeLongDouble: {
            long double d = num.doubleValue;
            if (isnan(d) || isinf(d))
                d = 0;
            ((void (*)(id, SEL, long double))(void *)objc_msgSend)((id)model, meta->_setter, (long double)d);
        } // break; commented for code coverage in next line
        default:
            break;
    }
}

/**
 Set value to model with a property meta.

 @discussion Caller should hold strong reference to the parameters before this function returns.

 @param model Should not be nil.
 @param value Should not be nil, but can be NSNull.
 @param meta  Should not be nil, and meta->_setter should not be nil.
 */
static void qcloudModelSetValueForProperty(__unsafe_unretained id model, __unsafe_unretained id value, __unsafe_unretained _QCloudModelPropertyMeta *meta) {
    if (meta->_isCNumber) {
        NSNumber *num = QCloudNSNumberCreateFromID(value);
        qcloudModelSetNumberToProperty(model, num, meta);
        if (num != nil)
            [num class]; // hold the number
    } else if (meta->_nsType) {
        if (value == (id)kCFNull) {
            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, (id)nil);
        } else {
            switch (meta->_nsType) {
                case QCloudEncodingTypeNSString:
                case QCloudEncodingTypeNSMutableString: {
                    if ([value isKindOfClass:[NSString class]]) {
                        if (meta->_nsType == QCloudEncodingTypeNSString) {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                        } else {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, ((NSString *)value).mutableCopy);
                        }
                    } else if ([value isKindOfClass:[NSNumber class]]) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter,
                                                                      (meta->_nsType == QCloudEncodingTypeNSString)
                                                                          ? ((NSNumber *)value).stringValue
                                                                          : ((NSNumber *)value).stringValue.mutableCopy);
                    } else if ([value isKindOfClass:[NSData class]]) {
                        NSMutableString *string = [[NSMutableString alloc] initWithData:value encoding:NSUTF8StringEncoding];
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, string);
                    } else if ([value isKindOfClass:[NSURL class]]) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter,
                                                                      (meta->_nsType == QCloudEncodingTypeNSString)
                                                                          ? ((NSURL *)value).absoluteString
                                                                          : ((NSURL *)value).absoluteString.mutableCopy);
                    } else if ([value isKindOfClass:[NSAttributedString class]]) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter,
                                                                      (meta->_nsType == QCloudEncodingTypeNSString)
                                                                          ? ((NSAttributedString *)value).string
                                                                          : ((NSAttributedString *)value).string.mutableCopy);
                    }
                } break;

                case QCloudEncodingTypeNSValue:
                case QCloudEncodingTypeNSNumber:
                case QCloudEncodingTypeNSDecimalNumber: {
                    if (meta->_nsType == QCloudEncodingTypeNSNumber) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, QCloudNSNumberCreateFromID(value));
                    } else if (meta->_nsType == QCloudEncodingTypeNSDecimalNumber) {
                        if ([value isKindOfClass:[NSDecimalNumber class]]) {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                        } else if ([value isKindOfClass:[NSNumber class]]) {
                            NSDecimalNumber *decNum = [NSDecimalNumber decimalNumberWithDecimal:[((NSNumber *)value) decimalValue]];
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, decNum);
                        } else if ([value isKindOfClass:[NSString class]]) {
                            NSDecimalNumber *decNum = [NSDecimalNumber decimalNumberWithString:value];
                            NSDecimal dec = decNum.decimalValue;
                            if (dec._length == 0 && dec._isNegative) {
                                decNum = nil; // NaN
                            }
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, decNum);
                        }
                    } else { // QCloudEncodingTypeNSValue
                        if ([value isKindOfClass:[NSValue class]]) {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                        }
                    }
                } break;

                case QCloudEncodingTypeNSData:
                case QCloudEncodingTypeNSMutableData: {
                    if ([value isKindOfClass:[NSData class]]) {
                        if (meta->_nsType == QCloudEncodingTypeNSData) {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                        } else {
                            NSMutableData *data = ((NSData *)value).mutableCopy;
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, data);
                        }
                    } else if ([value isKindOfClass:[NSString class]]) {
                        NSData *data = [(NSString *)value dataUsingEncoding:NSUTF8StringEncoding];
                        if (meta->_nsType == QCloudEncodingTypeNSMutableData) {
                            data = ((NSData *)data).mutableCopy;
                        }
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, data);
                    }
                } break;

                case QCloudEncodingTypeNSDate: {
                    if ([value isKindOfClass:[NSDate class]]) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                    } else if ([value isKindOfClass:[NSString class]]) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, QCloudNSDateFromString(value));
                    }
                } break;

                case QCloudEncodingTypeNSURL: {
                    if ([value isKindOfClass:[NSURL class]]) {
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                    } else if ([value isKindOfClass:[NSString class]]) {
                        NSCharacterSet *set = [NSCharacterSet whitespaceAndNewlineCharacterSet];
                        NSString *str = [value stringByTrimmingCharactersInSet:set];
                        if (str.length == 0) {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, nil);
                        } else {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, [[NSURL alloc] initWithString:str]);
                        }
                    }
                } break;

                case QCloudEncodingTypeNSArray:
                case QCloudEncodingTypeNSMutableArray: {
                    if (meta->_genericCls) {
                        NSArray *valueArr = nil;
                        if ([value isKindOfClass:[NSArray class]])
                            valueArr = value;
                        else if ([value isKindOfClass:[NSSet class]])
                            valueArr = ((NSSet *)value).allObjects;
                        if (valueArr) {
                            NSMutableArray *objectArr = [NSMutableArray new];
                            for (id one in valueArr) {
                                if ([one isKindOfClass:meta->_genericCls]) {
                                    [objectArr addObject:one];
                                } else if ([one isKindOfClass:[NSDictionary class]]) {
                                    Class cls = meta->_genericCls;
                                    if (meta->_hasCustomClassFromDictionary) {
                                        cls = [cls modelCustomClassForDictionary:one];
                                        if (!cls)
                                            cls = meta->_genericCls; // for xcode code coverage
                                    }
                                    NSObject *newOne = [cls new];
                                    [newOne qcloud_modelSetWithDictionary:one];
                                    if (newOne)
                                        [objectArr addObject:newOne];
                                }
                            }
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, objectArr);
                        }
                    } else {
                        if ([value isKindOfClass:[NSArray class]]) {
                            if (meta->_nsType == QCloudEncodingTypeNSArray) {
                                ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                            } else {
                                ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, ((NSArray *)value).mutableCopy);
                            }
                        } else if ([value isKindOfClass:[NSSet class]]) {
                            if (meta->_nsType == QCloudEncodingTypeNSArray) {
                                ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, ((NSSet *)value).allObjects);
                            } else {
                                ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, ((NSSet *)value).allObjects.mutableCopy);
                            }
                        }
                    }
                } break;

                case QCloudEncodingTypeNSDictionary:
                case QCloudEncodingTypeNSMutableDictionary: {
                    if ([value isKindOfClass:[NSDictionary class]]) {
                        if (meta->_genericCls) {
                            NSMutableDictionary *dic = [NSMutableDictionary new];
                            [((NSDictionary *)value) enumerateKeysAndObjectsUsingBlock:^(NSString *oneKey, id oneValue, BOOL *stop) {
                                if ([oneValue isKindOfClass:[NSDictionary class]]) {
                                    Class cls = meta->_genericCls;
                                    if (meta->_hasCustomClassFromDictionary) {
                                        cls = [cls modelCustomClassForDictionary:oneValue];
                                        if (!cls)
                                            cls = meta->_genericCls; // for xcode code coverage
                                    }
                                    NSObject *newOne = [cls new];
                                    [newOne qcloud_modelSetWithDictionary:(id)oneValue];
                                    if (newOne)
                                        dic[oneKey] = newOne;
                                }
                            }];
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, dic);
                        } else {
                            if (meta->_nsType == QCloudEncodingTypeNSDictionary) {
                                ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, value);
                            } else {
                                ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, ((NSDictionary *)value).mutableCopy);
                            }
                        }
                    }
                } break;

                case QCloudEncodingTypeNSSet:
                case QCloudEncodingTypeNSMutableSet: {
                    NSSet *valueSet = nil;
                    if ([value isKindOfClass:[NSArray class]])
                        valueSet = [NSMutableSet setWithArray:value];
                    else if ([value isKindOfClass:[NSSet class]])
                        valueSet = ((NSSet *)value);

                    if (meta->_genericCls) {
                        NSMutableSet *set = [NSMutableSet new];
                        for (id one in valueSet) {
                            if ([one isKindOfClass:meta->_genericCls]) {
                                [set addObject:one];
                            } else if ([one isKindOfClass:[NSDictionary class]]) {
                                Class cls = meta->_genericCls;
                                if (meta->_hasCustomClassFromDictionary) {
                                    cls = [cls modelCustomClassForDictionary:one];
                                    if (!cls)
                                        cls = meta->_genericCls; // for xcode code coverage
                                }
                                NSObject *newOne = [cls new];
                                [newOne qcloud_modelSetWithDictionary:one];
                                if (newOne)
                                    [set addObject:newOne];
                            }
                        }
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, set);
                    } else {
                        if (meta->_nsType == QCloudEncodingTypeNSSet) {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, valueSet);
                        } else {
                            ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, ((NSSet *)valueSet).mutableCopy);
                        }
                    }
                } // break; commented for code coverage in next line

                default:
                    break;
            }
        }
    } else {
        BOOL isNull = (value == (id)kCFNull);
        switch (meta->_type & QCloudEncodingTypeMask) {
            case QCloudEncodingTypeObject: {
                if (isNull) {
                    ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, (id)nil);
                } else if ([value isKindOfClass:meta->_cls] || !meta->_cls) {
                    ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, (id)value);
                } else if ([value isKindOfClass:[NSDictionary class]]) {
                    NSObject *one = nil;
                    if (meta->_getter) {
                        one = ((id(*)(id, SEL))(void *)objc_msgSend)((id)model, meta->_getter);
                    }
                    if (one) {
                        [one qcloud_modelSetWithDictionary:value];
                    } else {
                        Class cls = meta->_cls;
                        if (meta->_hasCustomClassFromDictionary) {
                            cls = [cls modelCustomClassForDictionary:value];
                            if (!cls)
                                cls = meta->_genericCls; // for xcode code coverage
                        }
                        one = [cls new];
                        [one qcloud_modelSetWithDictionary:value];
                        ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)model, meta->_setter, (id)one);
                    }
                }
            } break;

            case QCloudEncodingTypeClass: {
                if (isNull) {
                    ((void (*)(id, SEL, Class))(void *)objc_msgSend)((id)model, meta->_setter, (Class)NULL);
                } else {
                    Class cls = nil;
                    if ([value isKindOfClass:[NSString class]]) {
                        cls = NSClassFromString(value);
                        if (cls) {
                            ((void (*)(id, SEL, Class))(void *)objc_msgSend)((id)model, meta->_setter, (Class)cls);
                        }
                    } else {
                        cls = object_getClass(value);
                        if (cls) {
                            if (class_isMetaClass(cls)) {
                                ((void (*)(id, SEL, Class))(void *)objc_msgSend)((id)model, meta->_setter, (Class)value);
                            }
                        }
                    }
                }
            } break;

            case QCloudEncodingTypeSEL: {
                if (isNull) {
                    ((void (*)(id, SEL, SEL))(void *)objc_msgSend)((id)model, meta->_setter, (SEL)NULL);
                } else if ([value isKindOfClass:[NSString class]]) {
                    SEL sel = NSSelectorFromString(value);
                    if (sel)
                        ((void (*)(id, SEL, SEL))(void *)objc_msgSend)((id)model, meta->_setter, (SEL)sel);
                }
            } break;

            case QCloudEncodingTypeBlock: {
                if (isNull) {
                    ((void (*)(id, SEL, void (^)(void)))(void *)objc_msgSend)((id)model, meta->_setter, (void (^)(void))NULL);
                } else if ([value isKindOfClass:QCloudNSBlockClass()]) {
                    ((void (*)(id, SEL, void (^)(void)))(void *)objc_msgSend)((id)model, meta->_setter, (void (^)(void))value);
                }
            } break;

            case QCloudEncodingTypeStruct:
            case QCloudEncodingTypeUnion:
            case QCloudEncodingTypeCArray: {
                if ([value isKindOfClass:[NSValue class]]) {
                    const char *valueType = ((NSValue *)value).objCType;
                    const char *metaType = meta->_info.typeEncoding.UTF8String;
                    if (valueType && metaType && strcmp(valueType, metaType) == 0) {
                        [model setValue:value forKey:meta->_name];
                    }
                }
            } break;

            case QCloudEncodingTypePointer:
            case QCloudEncodingTypeCString: {
                if (isNull) {
                    ((void (*)(id, SEL, void *))(void *)objc_msgSend)((id)model, meta->_setter, (void *)NULL);
                } else if ([value isKindOfClass:[NSValue class]]) {
                    NSValue *nsValue = value;
                    if (nsValue.objCType && strcmp(nsValue.objCType, "^v") == 0) {
                        ((void (*)(id, SEL, void *))(void *)objc_msgSend)((id)model, meta->_setter, nsValue.pointerValue);
                    }
                }
            } // break; commented for code coverage in next line

            default:
                break;
        }
    }
}

typedef struct {
    void *modelMeta;  ///< _QCloudModelMeta
    void *model;      ///< id (self)
    void *dictionary; ///< NSDictionary (json)
} ModelSetContext;

/**
 Apply function for dictionary, to set the key-value pair to model.

 @param _key     should not be nil, NSString.
 @param _value   should not be nil.
 @param _context _context.modelMeta and _context.model should not be nil.
 */
static void qcloudModelSetWithDictionaryFunction(const void *_key, const void *_value, void *_context) {
    ModelSetContext *context = _context;
    __unsafe_unretained _QCloudModelMeta *meta = (__bridge _QCloudModelMeta *)(context->modelMeta);
    __unsafe_unretained _QCloudModelPropertyMeta *propertyMeta = [meta->_mapper objectForKey:(__bridge id)(_key)];
    __unsafe_unretained id model = (__bridge id)(context->model);
    while (propertyMeta) {
        if (propertyMeta->_setter) {
            qcloudModelSetValueForProperty(model, (__bridge __unsafe_unretained id)_value, propertyMeta);
        }
        propertyMeta = propertyMeta->_next;
    };
}

/**
 Apply function for model property meta, to set dictionary to model.

 @param _propertyMeta should not be nil, _QCloudModelPropertyMeta.
 @param _context      _context.model and _context.dictionary should not be nil.
 */
static void qcloudModelSetWithPropertyMetaArrayFunction(const void *_propertyMeta, void *_context) {
    ModelSetContext *context = _context;
    __unsafe_unretained NSDictionary *dictionary = (__bridge NSDictionary *)(context->dictionary);
    __unsafe_unretained _QCloudModelPropertyMeta *propertyMeta = (__bridge _QCloudModelPropertyMeta *)(_propertyMeta);
    if (!propertyMeta->_setter)
        return;
    id value = nil;

    if (propertyMeta->_mappedToKeyArray) {
        value = QCloudValueForMultiKeys(dictionary, propertyMeta->_mappedToKeyArray);
    } else if (propertyMeta->_mappedToKeyPath) {
        value = QCloudValueForKeyPath(dictionary, propertyMeta->_mappedToKeyPath);
    } else {
        value = [dictionary objectForKey:propertyMeta->_mappedToKey];
    }

    if (value) {
        __unsafe_unretained id model = (__bridge id)(context->model);
        qcloudModelSetValueForProperty(model, value, propertyMeta);
    }
}

/**
 Returns a valid JSON object (NSArray/NSDictionary/NSString/NSNumber/NSNull),
 or nil if an error occurs.

 @param model Model, can be nil.
 @return JSON object, nil if an error occurs.
 */
static id qcloud_ModelToJSONObjectRecursive(NSObject *model) {
    if (!model || model == (id)kCFNull)
        return model;
    if ([model isKindOfClass:[NSString class]])
        return model;
    if ([model isKindOfClass:[NSNumber class]])
        return model;
    if ([model isKindOfClass:[NSDictionary class]]) {
        if ([NSJSONSerialization isValidJSONObject:model])
            return model;
        NSMutableDictionary *newDic = [NSMutableDictionary new];
        [((NSDictionary *)model) enumerateKeysAndObjectsUsingBlock:^(NSString *key, id obj, BOOL *stop) {
            NSString *stringKey = [key isKindOfClass:[NSString class]] ? key : key.description;
            if (!stringKey)
                return;
            id jsonObj = qcloud_ModelToJSONObjectRecursive(obj);
            if (!jsonObj)
                jsonObj = (id)kCFNull;
            newDic[stringKey] = jsonObj;
        }];
        return newDic;
    }
    if ([model isKindOfClass:[NSSet class]]) {
        NSArray *array = ((NSSet *)model).allObjects;
        if ([NSJSONSerialization isValidJSONObject:array])
            return array;
        NSMutableArray *newArray = [NSMutableArray new];
        for (id obj in array) {
            if ([obj isKindOfClass:[NSString class]] || [obj isKindOfClass:[NSNumber class]]) {
                [newArray addObject:obj];
            } else {
                id jsonObj = qcloud_ModelToJSONObjectRecursive(obj);
                if (jsonObj && jsonObj != (id)kCFNull)
                    [newArray addObject:jsonObj];
            }
        }
        return newArray;
    }
    if ([model isKindOfClass:[NSArray class]]) {
        if ([NSJSONSerialization isValidJSONObject:model])
            return model;
        NSMutableArray *newArray = [NSMutableArray new];
//        for (id obj in (NSArray *)model) {
//            if ([obj isKindOfClass:[NSString class]] || [obj isKindOfClass:[NSNumber class]]) {
//                [newArray addObject:obj];
//            } else {
//                id jsonObj = qcloud_ModelToJSONObjectRecursive(obj);
//                if (jsonObj && jsonObj != (id)kCFNull)
//                    [newArray addObject:jsonObj];
//            }
//        }
        NSArray * array = (NSArray *)model;
        for (int i = 0;  i < array.count; i ++ ) {
            id obj = [array objectAtIndex:i];
            if ([obj isKindOfClass:[NSString class]] || [obj isKindOfClass:[NSNumber class]]) {
                [newArray addObject:obj];
            } else {
                id jsonObj = qcloud_ModelToJSONObjectRecursive(obj);
                if (jsonObj && jsonObj != (id)kCFNull)
                    [newArray addObject:jsonObj];
            }
        }
        return newArray;
    }
    if ([model isKindOfClass:[NSURL class]])
        return ((NSURL *)model).absoluteString;
    if ([model isKindOfClass:[NSAttributedString class]])
        return ((NSAttributedString *)model).string;
    if ([model isKindOfClass:[NSDate class]])
        return [QCloudISODateFormatter() stringFromDate:(id)model];
    if ([model isKindOfClass:[NSData class]])
        return nil;

    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:[model class]];
    if (!modelMeta || modelMeta->_keyMappedCount == 0)
        return nil;
    NSMutableDictionary *result = [[NSMutableDictionary alloc] initWithCapacity:64];
    __unsafe_unretained NSMutableDictionary *dic = result; // avoid retain and release in block
    [modelMeta->_mapper enumerateKeysAndObjectsUsingBlock:^(NSString *propertyMappedKey, _QCloudModelPropertyMeta *propertyMeta, BOOL *stop) {
        if ([propertyMappedKey isEqualToString:@"retryHandler"]) {
            return;
        }
        if (!propertyMeta->_getter)
            return;

        id value = nil;
        if (propertyMeta->_isCNumber) {
            value = qcloudModelCreateNumberFromProperty(model, propertyMeta);
        } else if (propertyMeta->_nsType) {
            id v = ((id(*)(id, SEL))(void *)objc_msgSend)((id)model, propertyMeta->_getter);
            value = qcloud_ModelToJSONObjectRecursive(v);
        } else {
            switch (propertyMeta->_type & QCloudEncodingTypeMask) {
                case QCloudEncodingTypeObject: {
                    id v = ((id(*)(id, SEL))(void *)objc_msgSend)((id)model, propertyMeta->_getter);
                    value = qcloud_ModelToJSONObjectRecursive(v);
                    if (value == (id)kCFNull)
                        value = nil;
                } break;
                case QCloudEncodingTypeClass: {
                    Class v = ((Class(*)(id, SEL))(void *)objc_msgSend)((id)model, propertyMeta->_getter);
                    value = v ? NSStringFromClass(v) : nil;
                } break;
                case QCloudEncodingTypeSEL: {
                    SEL v = ((SEL(*)(id, SEL))(void *)objc_msgSend)((id)model, propertyMeta->_getter);
                    value = v ? NSStringFromSelector(v) : nil;
                } break;
                default:
                    break;
            }
        }
        if (!value)
            return;

        if (propertyMeta->_mappedToKeyPath) {
            NSMutableDictionary *superDic = dic;
            NSMutableDictionary *subDic = nil;
            for (NSUInteger i = 0, max = propertyMeta->_mappedToKeyPath.count; i < max; i++) {
                NSString *key = propertyMeta->_mappedToKeyPath[i];
                if (i + 1 == max) { // end
                    if (!superDic[key])
                        superDic[key] = value;
                    break;
                }

                subDic = superDic[key];
                if (subDic) {
                    if ([subDic isKindOfClass:[NSDictionary class]]) {
                        subDic = subDic.mutableCopy;
                        superDic[key] = subDic;
                    } else {
                        break;
                    }
                } else {
                    subDic = [NSMutableDictionary new];
                    superDic[key] = subDic;
                }
                superDic = subDic;
                subDic = nil;
            }
        } else {
            if (!dic[propertyMeta->_mappedToKey]) {
                dic[propertyMeta->_mappedToKey] = value;
            }
        }
    }];

    if (modelMeta->_hasCustomTransformToDictionary) {
        BOOL suc = [((id<QCloudModel>)model) modelCustomTransformToDictionary:dic];
        if (!suc)
            return nil;
    }
    return result;
}

/// Add indent to string (exclude first line)
static NSMutableString *qcloudModelDescriptionAddIndent(NSMutableString *desc, NSUInteger indent) {
    for (NSUInteger i = 0, max = desc.length; i < max; i++) {
        unichar c = [desc characterAtIndex:i];
        if (c == '\n') {
            for (NSUInteger j = 0; j < indent; j++) {
                [desc insertString:@"    " atIndex:i + 1];
            }
            i += indent * 4;
            max += indent * 4;
        }
    }
    return desc;
}

/// Generaate a description string
static NSString *qcloudModelDescription(NSObject *model) {
    static const int kDescMaxLength = 100;
    if (!model)
        return @"<nil>";
    if (model == (id)kCFNull)
        return @"<null>";
    if (![model isKindOfClass:[NSObject class]])
        return [NSString stringWithFormat:@"%@", model];

    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:model.class];
    switch (modelMeta->_nsType) {
        case QCloudEncodingTypeNSString:
        case QCloudEncodingTypeNSMutableString: {
            return [NSString stringWithFormat:@"\"%@\"", model];
        }

        case QCloudEncodingTypeNSValue:
        case QCloudEncodingTypeNSData:
        case QCloudEncodingTypeNSMutableData: {
            NSString *tmp = model.description;
            if (tmp.length > kDescMaxLength) {
                tmp = [tmp substringToIndex:kDescMaxLength];
                tmp = [tmp stringByAppendingString:@"..."];
            }
            return tmp;
        }

        case QCloudEncodingTypeNSNumber:
        case QCloudEncodingTypeNSDecimalNumber:
        case QCloudEncodingTypeNSDate:
        case QCloudEncodingTypeNSURL: {
            return [NSString stringWithFormat:@"%@", model];
        }

        case QCloudEncodingTypeNSSet:
        case QCloudEncodingTypeNSMutableSet: {
            model = ((NSSet *)model).allObjects;
        } // no break

        case QCloudEncodingTypeNSArray:
        case QCloudEncodingTypeNSMutableArray: {
            NSArray *array = (id)model;
            NSMutableString *desc = [NSMutableString new];
            if (array.count == 0) {
                return [desc stringByAppendingString:@"[]"];
            } else {
                [desc appendFormat:@"[\n"];
                for (NSUInteger i = 0, max = array.count; i < max; i++) {
                    NSObject *obj = array[i];
                    [desc appendString:@"    "];
                    [desc appendString:qcloudModelDescriptionAddIndent(qcloudModelDescription(obj).mutableCopy, 1)];
                    [desc appendString:(i + 1 == max) ? @"\n" : @";\n"];
                }
                [desc appendString:@"]"];
                return desc;
            }
        }
        case QCloudEncodingTypeNSDictionary:
        case QCloudEncodingTypeNSMutableDictionary: {
            NSDictionary *dic = (id)model;
            NSMutableString *desc = [NSMutableString new];
            if (dic.count == 0) {
                return [desc stringByAppendingString:@"{}"];
            } else {
                NSArray *keys = dic.allKeys;

                [desc appendFormat:@"{\n"];
                for (NSUInteger i = 0, max = keys.count; i < max; i++) {
                    NSString *key = keys[i];
                    NSObject *value = dic[key];
                    [desc appendString:@"    "];
                    [desc appendFormat:@"%@ = %@", key, qcloudModelDescriptionAddIndent(qcloudModelDescription(value).mutableCopy, 1)];
                    [desc appendString:(i + 1 == max) ? @"\n" : @";\n"];
                }
                [desc appendString:@"}"];
            }
            return desc;
        }

        default: {
            NSMutableString *desc = [NSMutableString new];
            [desc appendFormat:@"<%@: %p>", model.class, model];
            if (modelMeta->_allPropertyMetas.count == 0)
                return desc;

            // sort property names
            NSArray *properties = [modelMeta->_allPropertyMetas
                sortedArrayUsingComparator:^NSComparisonResult(_QCloudModelPropertyMeta *p1, _QCloudModelPropertyMeta *p2) {
                    return [p1->_name compare:p2->_name];
                }];

            [desc appendFormat:@" {\n"];
            for (NSUInteger i = 0, max = properties.count; i < max; i++) {
                _QCloudModelPropertyMeta *property = properties[i];
                NSString *propertyDesc;
                if (property->_isCNumber) {
                    NSNumber *num = qcloudModelCreateNumberFromProperty(model, property);
                    propertyDesc = num.stringValue;
                } else {
                    switch (property->_type & QCloudEncodingTypeMask) {
                        case QCloudEncodingTypeObject: {
                            id v = ((id(*)(id, SEL))(void *)objc_msgSend)((id)model, property->_getter);
                            propertyDesc = qcloudModelDescription(v);
                            if (!propertyDesc)
                                propertyDesc = @"<nil>";
                        } break;
                        case QCloudEncodingTypeClass: {
                            id v = ((id(*)(id, SEL))(void *)objc_msgSend)((id)model, property->_getter);
                            propertyDesc = ((NSObject *)v).description;
                            if (!propertyDesc)
                                propertyDesc = @"<nil>";
                        } break;
                        case QCloudEncodingTypeSEL: {
                            SEL sel = ((SEL(*)(id, SEL))(void *)objc_msgSend)((id)model, property->_getter);
                            if (sel)
                                propertyDesc = NSStringFromSelector(sel);
                            else
                                propertyDesc = @"<NULL>";
                        } break;
                        case QCloudEncodingTypeBlock: {
                            id block = ((id(*)(id, SEL))(void *)objc_msgSend)((id)model, property->_getter);
                            propertyDesc = block ? ((NSObject *)block).description : @"<nil>";
                        } break;
                        case QCloudEncodingTypeCArray:
                        case QCloudEncodingTypeCString:
                        case QCloudEncodingTypePointer: {
                            void *pointer = ((void *(*)(id, SEL))(void *)objc_msgSend)((id)model, property->_getter);
                            propertyDesc = [NSString stringWithFormat:@"%p", pointer];
                        } break;
                        case QCloudEncodingTypeStruct:
                        case QCloudEncodingTypeUnion: {
                            NSValue *value = [model valueForKey:property->_name];
                            propertyDesc = value ? value.description : @"{unknown}";
                        } break;
                        default:
                            propertyDesc = @"<unknown>";
                    }
                }

                propertyDesc = qcloudModelDescriptionAddIndent(propertyDesc.mutableCopy, 1);
                [desc appendFormat:@"    %@ = %@", property->_name, propertyDesc];
                [desc appendString:(i + 1 == max) ? @"\n" : @";\n"];
            }
            [desc appendFormat:@"}"];
            return desc;
        }
    }
}

@implementation NSObject (QCloudModel)

+ (NSDictionary *)_qcloud_dictionaryWithJSON:(id)json {
    if (!json || json == (id)kCFNull)
        return nil;
    NSDictionary *dic = nil;
    NSData *jsonData = nil;
    if ([json isKindOfClass:[NSDictionary class]]) {
        dic = json;
    } else if ([json isKindOfClass:[NSString class]]) {
        jsonData = [(NSString *)json dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([json isKindOfClass:[NSData class]]) {
        jsonData = json;
    }
    if (jsonData) {
        dic = [NSJSONSerialization JSONObjectWithData:jsonData options:kNilOptions error:NULL];
        if (![dic isKindOfClass:[NSDictionary class]])
            dic = nil;
    }
    return dic;
}

+ (instancetype)qcloud_modelWithJSON:(id)json {
    NSDictionary *dic = [self _qcloud_dictionaryWithJSON:json];
    return [self qcloud_modelWithDictionary:dic];
}

+ (instancetype)qcloud_modelWithDictionary:(NSDictionary *)dictionary {
    if (!dictionary || dictionary == (id)kCFNull)
        return nil;
    if (![dictionary isKindOfClass:[NSDictionary class]])
        return nil;

    Class cls = [self class];
    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:cls];
    if (modelMeta->_hasCustomClassFromDictionary) {
        cls = [cls modelCustomClassForDictionary:dictionary] ?: cls;
    }

    NSObject *one = [cls new];
    if ([one qcloud_modelSetWithDictionary:dictionary])
        return one;
    return nil;
}

- (BOOL)qcloud_modelSetWithJSON:(id)json {
    NSDictionary *dic = [NSObject _qcloud_dictionaryWithJSON:json];
    return [self qcloud_modelSetWithDictionary:dic];
}

- (BOOL)qcloud_modelSetWithDictionary:(NSDictionary *)dic {
    if (!dic || dic == (id)kCFNull)
        return NO;
    if (![dic isKindOfClass:[NSDictionary class]])
        return NO;

    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:object_getClass(self)];
    if (modelMeta->_keyMappedCount == 0)
        return NO;

    if (modelMeta->_hasCustomWillTransformFromDictionary) {
        dic = [((id<QCloudModel>)self) modelCustomWillTransformFromDictionary:dic];
        if (![dic isKindOfClass:[NSDictionary class]])
            return NO;
    }

    ModelSetContext context = { 0 };
    context.modelMeta = (__bridge void *)(modelMeta);
    context.model = (__bridge void *)(self);
    context.dictionary = (__bridge void *)(dic);

    if (modelMeta->_keyMappedCount >= CFDictionaryGetCount((CFDictionaryRef)dic)) {
        CFDictionaryApplyFunction((CFDictionaryRef)dic, qcloudModelSetWithDictionaryFunction, &context);
        if (modelMeta->_keyPathPropertyMetas) {
            CFArrayApplyFunction((CFArrayRef)modelMeta->_keyPathPropertyMetas,
                                 CFRangeMake(0, CFArrayGetCount((CFArrayRef)modelMeta->_keyPathPropertyMetas)), qcloudModelSetWithPropertyMetaArrayFunction,
                                 &context);
        }
        if (modelMeta->_multiKeysPropertyMetas) {
            CFArrayApplyFunction((CFArrayRef)modelMeta->_multiKeysPropertyMetas,
                                 CFRangeMake(0, CFArrayGetCount((CFArrayRef)modelMeta->_multiKeysPropertyMetas)),
                                 qcloudModelSetWithPropertyMetaArrayFunction, &context);
        }
    } else {
        CFArrayApplyFunction((CFArrayRef)modelMeta->_allPropertyMetas, CFRangeMake(0, modelMeta->_keyMappedCount),
                             qcloudModelSetWithPropertyMetaArrayFunction, &context);
    }

    if (modelMeta->_hasCustomTransformFromDictionary) {
        return [((id<QCloudModel>)self) modelCustomTransformFromDictionary:dic];
    }
    return YES;
}

- (id)qcloud_modelToJSONObject {
    /*
     Apple said:
     The top level object is an NSArray or NSDictionary.
     All objects are instances of NSString, NSNumber, NSArray, NSDictionary, or NSNull.
     All dictionary keys are instances of NSString.
     Numbers are not NaN or infinity.
     */
    id jsonObject = qcloud_ModelToJSONObjectRecursive(self);
    if ([jsonObject isKindOfClass:[NSArray class]])
        return jsonObject;
    if ([jsonObject isKindOfClass:[NSDictionary class]])
        return jsonObject;
    return nil;
}

- (NSData *)qcloud_modelToJSONData {
    id jsonObject = [self qcloud_modelToJSONObject];
    if (!jsonObject)
        return nil;
    
    @try {
        return [NSJSONSerialization dataWithJSONObject:jsonObject options:0 error:NULL];
    } @catch (NSException *exception) {
        return nil;
    }
    
}

- (NSString *)qcloud_modelToJSONString {
    NSData *jsonData = [self qcloud_modelToJSONData];
    if (jsonData.length == 0)
        return nil;
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

- (id)qcloud_modelCopy {
    if (self == (id)kCFNull)
        return self;
    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:self.class];
    if (modelMeta->_nsType)
        return [self copy];

    NSObject *one = [self.class new];
    for (_QCloudModelPropertyMeta *propertyMeta in modelMeta->_allPropertyMetas) {
        if (!propertyMeta->_getter || !propertyMeta->_setter)
            continue;

        if (propertyMeta->_isCNumber) {
            switch (propertyMeta->_type & QCloudEncodingTypeMask) {
                case QCloudEncodingTypeBool: {
                    bool num = ((bool (*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, bool))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeInt8:
                case QCloudEncodingTypeUInt8: {
                    uint8_t num = ((bool (*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, uint8_t))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeInt16:
                case QCloudEncodingTypeUInt16: {
                    uint16_t num = ((uint16_t(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, uint16_t))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeInt32:
                case QCloudEncodingTypeUInt32: {
                    uint32_t num = ((uint32_t(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, uint32_t))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeInt64:
                case QCloudEncodingTypeUInt64: {
                    uint64_t num = ((uint64_t(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, uint64_t))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeFloat: {
                    float num = ((float (*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, float))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeDouble: {
                    double num = ((double (*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, double))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } break;
                case QCloudEncodingTypeLongDouble: {
                    long double num = ((long double (*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, long double))(void *)objc_msgSend)((id)one, propertyMeta->_setter, num);
                } // break; commented for code coverage in next line
                default:
                    break;
            }
        } else {
            switch (propertyMeta->_type & QCloudEncodingTypeMask) {
                case QCloudEncodingTypeObject:
                case QCloudEncodingTypeClass:
                case QCloudEncodingTypeBlock: {
                    id value = ((id(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)one, propertyMeta->_setter, value);
                } break;
                case QCloudEncodingTypeSEL:
                case QCloudEncodingTypePointer:
                case QCloudEncodingTypeCString: {
                    size_t value = ((size_t(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    ((void (*)(id, SEL, size_t))(void *)objc_msgSend)((id)one, propertyMeta->_setter, value);
                } break;
                case QCloudEncodingTypeStruct:
                case QCloudEncodingTypeUnion: {
                    @try {
                        NSValue *value = [self valueForKey:NSStringFromSelector(propertyMeta->_getter)];
                        if (value) {
                            [one setValue:value forKey:propertyMeta->_name];
                        }
                    } @catch (NSException *exception) {
                    }
                } // break; commented for code coverage in next line
                default:
                    break;
            }
        }
    }
    return one;
}

- (void)qcloud_modelEncodeWithCoder:(NSCoder *)aCoder {
    if (!aCoder)
        return;
    if (self == (id)kCFNull) {
        [((id<NSCoding>)self) encodeWithCoder:aCoder];
        return;
    }

    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:self.class];
    if (modelMeta->_nsType) {
        [((id<NSCoding>)self) encodeWithCoder:aCoder];
        return;
    }

    for (_QCloudModelPropertyMeta *propertyMeta in modelMeta->_allPropertyMetas) {
        if (!propertyMeta->_getter)
            return;

        if (propertyMeta->_isCNumber) {
            NSNumber *value = qcloudModelCreateNumberFromProperty(self, propertyMeta);
            if (value != nil)
                [aCoder encodeObject:value forKey:propertyMeta->_name];
        } else {
            switch (propertyMeta->_type & QCloudEncodingTypeMask) {
                case QCloudEncodingTypeObject: {
                    id value = ((id(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    if (value && (propertyMeta->_nsType || [value respondsToSelector:@selector(encodeWithCoder:)])) {
                        if ([value isKindOfClass:[NSValue class]]) {
                            if ([value isKindOfClass:[NSNumber class]]) {
                                [aCoder encodeObject:value forKey:propertyMeta->_name];
                            }
                        } else {
                            [aCoder encodeObject:value forKey:propertyMeta->_name];
                        }
                    }
                } break;
                case QCloudEncodingTypeSEL: {
                    SEL value = ((SEL(*)(id, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_getter);
                    if (value) {
                        NSString *str = NSStringFromSelector(value);
                        [aCoder encodeObject:str forKey:propertyMeta->_name];
                    }
                } break;
                case QCloudEncodingTypeStruct:
                case QCloudEncodingTypeUnion: {
                    if (propertyMeta->_isKVCCompatible && propertyMeta->_isStructAvailableForKeyedArchiver) {
                        @try {
                            NSValue *value = [self valueForKey:NSStringFromSelector(propertyMeta->_getter)];
                            [aCoder encodeObject:value forKey:propertyMeta->_name];
                        } @catch (NSException *exception) {
                        }
                    }
                } break;

                default:
                    break;
            }
        }
    }
}

- (id)qcloud_modelInitWithCoder:(NSCoder *)aDecoder {
    if (!aDecoder)
        return self;
    if (self == (id)kCFNull)
        return self;
    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:self.class];
    if (modelMeta->_nsType)
        return self;

    for (_QCloudModelPropertyMeta *propertyMeta in modelMeta->_allPropertyMetas) {
        if (!propertyMeta->_setter)
            continue;

        if (propertyMeta->_isCNumber) {
            NSNumber *value = [aDecoder decodeObjectForKey:propertyMeta->_name];
            if ([value isKindOfClass:[NSNumber class]]) {
                qcloudModelSetNumberToProperty(self, value, propertyMeta);
                [value class];
            }
        } else {
            QCloudEncodingType type = propertyMeta->_type & QCloudEncodingTypeMask;
            switch (type) {
                case QCloudEncodingTypeObject: {
                    id value = [aDecoder decodeObjectForKey:propertyMeta->_name];
                    ((void (*)(id, SEL, id))(void *)objc_msgSend)((id)self, propertyMeta->_setter, value);
                } break;
                case QCloudEncodingTypeSEL: {
                    NSString *str = [aDecoder decodeObjectForKey:propertyMeta->_name];
                    if ([str isKindOfClass:[NSString class]]) {
                        SEL sel = NSSelectorFromString(str);
                        ((void (*)(id, SEL, SEL))(void *)objc_msgSend)((id)self, propertyMeta->_setter, sel);
                    }
                } break;
                case QCloudEncodingTypeStruct:
                case QCloudEncodingTypeUnion: {
                    if (propertyMeta->_isKVCCompatible) {
                        @try {
                            NSValue *value = [aDecoder decodeObjectForKey:propertyMeta->_name];
                            if (value)
                                [self setValue:value forKey:propertyMeta->_name];
                        } @catch (NSException *exception) {
                        }
                    }
                } break;

                default:
                    break;
            }
        }
    }
    return self;
}

- (NSUInteger)qcloud_modelHash {
    if (self == (id)kCFNull)
        return [self hash];
    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:self.class];
    if (modelMeta->_nsType)
        return [self hash];

    NSUInteger value = 0;
    NSUInteger count = 0;
    for (_QCloudModelPropertyMeta *propertyMeta in modelMeta->_allPropertyMetas) {
        if (!propertyMeta->_isKVCCompatible)
            continue;
        value ^= [[self valueForKey:NSStringFromSelector(propertyMeta->_getter)] hash];
        count++;
    }
    if (count == 0)
        value = (long)((__bridge void *)self);
    return value;
}

- (BOOL)qcloud_modelIsEqual:(id)model {
    if (self == model)
        return YES;
    if (![model isMemberOfClass:self.class])
        return NO;
    _QCloudModelMeta *modelMeta = [_QCloudModelMeta metaWithClass:self.class];
    if (modelMeta->_nsType)
        return [self isEqual:model];
    if ([self hash] != [model hash])
        return NO;

    for (_QCloudModelPropertyMeta *propertyMeta in modelMeta->_allPropertyMetas) {
        if (!propertyMeta->_isKVCCompatible)
            continue;
        id this = [self valueForKey:NSStringFromSelector(propertyMeta->_getter)];
        id that = [model valueForKey:NSStringFromSelector(propertyMeta->_getter)];
        if (this == that)
            continue;
        if (this == nil || that == nil)
            return NO;
        if (![this isEqual:that])
            return NO;
    }
    return YES;
}

- (NSString *)qcloud_modelDescription {
    return qcloudModelDescription(self);
}

@end

@implementation NSArray (QCloudModel)

+ (NSArray *)qcloud_modelArrayWithClass:(Class)cls json:(id)json {
    if (!json)
        return nil;
    NSArray *arr = nil;
    NSData *jsonData = nil;
    if ([json isKindOfClass:[NSArray class]]) {
        arr = json;
    } else if ([json isKindOfClass:[NSString class]]) {
        jsonData = [(NSString *)json dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([json isKindOfClass:[NSData class]]) {
        jsonData = json;
    }
    if (jsonData) {
        arr = [NSJSONSerialization JSONObjectWithData:jsonData options:kNilOptions error:NULL];
        if (![arr isKindOfClass:[NSArray class]])
            arr = nil;
    }
    return [self qcloud_modelArrayWithClass:cls array:arr];
}

+ (NSArray *)qcloud_modelArrayWithClass:(Class)cls array:(NSArray *)arr {
    if (!cls || !arr)
        return nil;
    NSMutableArray *result = [NSMutableArray new];
    for (NSDictionary *dic in arr) {
        if (![dic isKindOfClass:[NSDictionary class]])
            continue;
        NSObject *obj = [cls qcloud_modelWithDictionary:dic];
        if (obj)
            [result addObject:obj];
    }
    return result;
}

@end

@implementation NSDictionary (QCloudModel)

+ (NSDictionary *)qcloud_modelDictionaryWithClass:(Class)cls json:(id)json {
    if (!json)
        return nil;
    NSDictionary *dic = nil;
    NSData *jsonData = nil;
    if ([json isKindOfClass:[NSDictionary class]]) {
        dic = json;
    } else if ([json isKindOfClass:[NSString class]]) {
        jsonData = [(NSString *)json dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([json isKindOfClass:[NSData class]]) {
        jsonData = json;
    }
    if (jsonData) {
        dic = [NSJSONSerialization JSONObjectWithData:jsonData options:kNilOptions error:NULL];
        if (![dic isKindOfClass:[NSDictionary class]])
            dic = nil;
    }
    return [self qcloud_modelDictionaryWithClass:cls dictionary:dic];
}

+ (NSDictionary *)qcloud_modelDictionaryWithClass:(Class)cls dictionary:(NSDictionary *)dic {
    if (!cls || !dic)
        return nil;
    NSMutableDictionary *result = [NSMutableDictionary new];
    for (NSString *key in dic.allKeys) {
        if (![key isKindOfClass:[NSString class]])
            continue;
        NSObject *obj = [cls qcloud_modelWithDictionary:dic[key]];
        if (obj)
            result[key] = obj;
    }
    return result;
}

@end
