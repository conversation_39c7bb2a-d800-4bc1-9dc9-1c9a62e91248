//
//  NSDate+QCloudComapre.m
//  Pods
//
//  Created by <PERSON> on 2017/4/5.
//
//

#import "NSDate+QCloudComapre.h"

@implementation NSDate (QCloudComapre)
#pragma mark Comparators
/**
 *  Returns a YES if receiver is earlier than provided comparison date, otherwise returns NO
 *
 *  @param date NSDate - Provided date for comparison
 *
 *  @return BOOL representing comparison result
 */
- (BOOL)qcloud_isEarlierThan:(NSDate *)date {
    if (self.timeIntervalSince1970 < date.timeIntervalSince1970) {
        return YES;
    }
    return NO;
}

/**
 *  Returns a YES if receiver is later than provided comparison date, otherwise returns NO
 *
 *  @param date NSDate - Provided date for comparison
 *
 *  @return BOOL representing comparison result
 */
- (BOOL)qcloud_isLaterThan:(NSDate *)date {
    if (self.timeIntervalSince1970 > date.timeIntervalSince1970) {
        return YES;
    }
    return NO;
}

/**
 *  Returns a YES if receiver is earlier than or equal to the provided comparison date, otherwise returns NO
 *
 *  @param date NSDate - Provided date for comparison
 *
 *  @return BOOL representing comparison result
 */
- (BOOL)qcloud_isEarlierThanOrEqualTo:(NSDate *)date {
    if (self.timeIntervalSince1970 <= date.timeIntervalSince1970) {
        return YES;
    }
    return NO;
}

@end
