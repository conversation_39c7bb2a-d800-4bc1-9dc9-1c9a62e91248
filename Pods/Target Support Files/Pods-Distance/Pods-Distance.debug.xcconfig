ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AAInfographics" "${PODS_CONFIGURATION_BUILD_DIR}/ActiveLabel" "${PODS_CONFIGURATION_BUILD_DIR}/Alamofire" "${PODS_CONFIGURATION_BUILD_DIR}/AppAuth" "${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore" "${PODS_CONFIGURATION_BUILD_DIR}/Cache" "${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack" "${PODS_CONFIGURATION_BUILD_DIR}/EFQRCode" "${PODS_CONFIGURATION_BUILD_DIR}/EMPageViewController" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions" "${PODS_CONFIGURATION_BUILD_DIR}/FloatingPanel" "${PODS_CONFIGURATION_BUILD_DIR}/GRDB.swift" "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth" "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" "${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift" "${PODS_CONFIGURATION_BUILD_DIR}/ImageViewer.swift" "${PODS_CONFIGURATION_BUILD_DIR}/InputBarAccessoryView" "${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar" "${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView" "${PODS_CONFIGURATION_BUILD_DIR}/Jelly" "${PODS_CONFIGURATION_BUILD_DIR}/LocoKit" "${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD" "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" "${PODS_CONFIGURATION_BUILD_DIR}/Masonry" "${PODS_CONFIGURATION_BUILD_DIR}/MessageKit" "${PODS_CONFIGURATION_BUILD_DIR}/Moya" "${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift" "${PODS_CONFIGURATION_BUILD_DIR}/Protobuf" "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCOSXML" "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCore" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SQLite.swift" "${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive" "${PODS_CONFIGURATION_BUILD_DIR}/SVGAPlayer" "${PODS_CONFIGURATION_BUILD_DIR}/SnapKit" "${PODS_CONFIGURATION_BUILD_DIR}/Spine" "${PODS_CONFIGURATION_BUILD_DIR}/SpineCppLite" "${PODS_CONFIGURATION_BUILD_DIR}/SpineShadersStructs" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftNotes" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyStoreKit" "${PODS_CONFIGURATION_BUILD_DIR}/TAThirdParty" "${PODS_CONFIGURATION_BUILD_DIR}/TZImagePickerController" "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingDataCore" "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingSDK" "${PODS_CONFIGURATION_BUILD_DIR}/Toast-Swift" "${PODS_CONFIGURATION_BUILD_DIR}/UICountingLabel" "${PODS_CONFIGURATION_BUILD_DIR}/URLNavigator" "${PODS_CONFIGURATION_BUILD_DIR}/Upsurge" "${PODS_CONFIGURATION_BUILD_DIR}/YLCore" "${PODS_CONFIGURATION_BUILD_DIR}/YLPaymentKit" "${PODS_CONFIGURATION_BUILD_DIR}/YLWebView" "${PODS_CONFIGURATION_BUILD_DIR}/ZLPhotoBrowser" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_CONFIGURATION_BUILD_DIR}/swiftScan" "${PODS_ROOT}/Ads-Global/SDK" "${PODS_ROOT}/AppLovinSDK/applovin-ios-sdk-13.2.0" "${PODS_ROOT}/AppsFlyerFramework/binaries/xcframework/full" "${PODS_ROOT}/FBAEMKit/XCFrameworks" "${PODS_ROOT}/FBAudienceNetwork/Static" "${PODS_ROOT}/FBSDKCoreKit/XCFrameworks" "${PODS_ROOT}/FBSDKCoreKit_Basics/XCFrameworks" "${PODS_ROOT}/FBSDKLoginKit/XCFrameworks" "${PODS_ROOT}/FirebaseAnalytics/Frameworks" "${PODS_ROOT}/Google-Mobile-Ads-SDK/Frameworks/GoogleMobileAdsFramework" "${PODS_ROOT}/GoogleAppMeasurement/Frameworks" "${PODS_ROOT}/GoogleMobileAdsMediationAppLovin/AppLovinAdapter-13.2.0.0" "${PODS_ROOT}/GoogleMobileAdsMediationFacebook/MetaAdapter-6.17.1.0" "${PODS_ROOT}/GoogleMobileAdsMediationPangle/PangleAdapter-7.1.1.0.1" "${PODS_ROOT}/GoogleMobileAdsMediationUnity/UnityAdapter-4.14.2.0" "${PODS_ROOT}/GoogleUserMessagingPlatform/Frameworks/Release" "${PODS_ROOT}/HyphenateChat" "${PODS_ROOT}/LocoKitCore" "${PODS_ROOT}/TPNAdmobSDKAdapter/AnyThinkAdmobAdapter-6.4.76" "${PODS_ROOT}/TPNApplovinSDKAdapter/AnyThinkApplovinAdapter-6.4.76" "${PODS_ROOT}/TPNFacebookSDKAdapter/AnyThinkFacebookAdapter-6.4.76" "${PODS_ROOT}/TPNPangleSDKAdapter/AnyThinkPangleAdapter-6.4.76" "${PODS_ROOT}/TPNUnityAdsSDKAdapter/AnyThinkUnityAdsAdapter-6.4.76" "${PODS_ROOT}/TPNiOS/core" "${PODS_ROOT}/UnityAds" "${PODS_XCFRAMEWORKS_BUILD_DIR}/Ads-Global/BUAdSDK" "${PODS_XCFRAMEWORKS_BUILD_DIR}/AppLovinSDK" "${PODS_XCFRAMEWORKS_BUILD_DIR}/AppsFlyerFramework/Main" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAudienceNetwork" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/AdIdSupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/Google-Mobile-Ads-SDK" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/WithoutAdIdSupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationAppLovin" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationFacebook" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationPangle" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationUnity" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleUserMessagingPlatform" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNAdmobSDKAdapter" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNApplovinSDKAdapter" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNFacebookSDKAdapter" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNPangleSDKAdapter" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNUnityAdsSDKAdapter" "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNiOS/TPNSDK" "${PODS_XCFRAMEWORKS_BUILD_DIR}/UnityAds"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1 $(inherited) SD_WEBP=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/AAInfographics/AAInfographics.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ActiveLabel/ActiveLabel.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AppAuth/AppAuth.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore/AppCheckCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Cache/Cache.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EFQRCode/EFQRCode.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EMPageViewController/EMPageViewController.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions/FirebaseSessions.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FloatingPanel/FloatingPanel.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GRDB.swift/GRDB.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth/GTMAppAuth.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/Google_Mobile_Ads_SDK.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn/GoogleSignIn.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ImageViewer.swift/ImageViewer_swift.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/InputBarAccessoryView/InputBarAccessoryView.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar/JTAppleCalendar.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView/JXSegmentedView.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Jelly/Jelly.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/LocoKit/LocoKit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh/MJRefresh.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Masonry/Masonry.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/MessageKit/MessageKit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Moya/Moya.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper/ObjectMapper.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Protobuf/Protobuf.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCOSXML/QCloudCOSXML.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCore/QCloudCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SQLite.swift/SQLite.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive/SSZipArchive.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SVGAPlayer/SVGAPlayer.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SnapKit/SnapKit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Spine/Spine.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SpineCppLite/SpineCppLite.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SpineShadersStructs/SpineShadersStructs.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate/SwiftDate.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftNotes/SwiftNotes.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON/SwiftyJSON.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyStoreKit/SwiftyStoreKit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/TAThirdParty/TAThirdParty.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/TZImagePickerController/TZImagePickerController.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingDataCore/ThinkingDataCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingSDK/ThinkingSDK.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Toast-Swift/Toast_Swift.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/UICountingLabel/UICountingLabel.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/URLNavigator/URLNavigator.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Upsurge/Upsurge.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/YLCore/YLCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/YLPaymentKit/YLPaymentKit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/YLWebView/YLWebView.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ZLPhotoBrowser/ZLPhotoBrowser.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/Lottie.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/swiftScan/swiftScan.framework/Headers" "$(PODS_ROOT)/SpineCppLite/spine-cpp/spine-cpp/include" "$(PODS_ROOT)/SpineCppLite/spine-cpp/spine-cpp-lite"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(SDKROOT)/usr/lib/swift
OTHER_CFLAGS = $(inherited) -isystem "${PODS_CONFIGURATION_BUILD_DIR}/AAInfographics/AAInfographics.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/ActiveLabel/ActiveLabel.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/AppAuth/AppAuth.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore/AppCheckCore.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Cache/Cache.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/EFQRCode/EFQRCode.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/EMPageViewController/EMPageViewController.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions/FirebaseSessions.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/FloatingPanel/FloatingPanel.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/GRDB.swift/GRDB.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth/GTMAppAuth.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/Google_Mobile_Ads_SDK.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn/GoogleSignIn.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/ImageViewer.swift/ImageViewer_swift.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/InputBarAccessoryView/InputBarAccessoryView.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar/JTAppleCalendar.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView/JXSegmentedView.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Jelly/Jelly.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/LocoKit/LocoKit.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh/MJRefresh.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Masonry/Masonry.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/MessageKit/MessageKit.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Moya/Moya.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper/ObjectMapper.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Protobuf/Protobuf.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCOSXML/QCloudCOSXML.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCore/QCloudCore.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SQLite.swift/SQLite.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive/SSZipArchive.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SVGAPlayer/SVGAPlayer.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SnapKit/SnapKit.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Spine/Spine.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SpineCppLite/SpineCppLite.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SpineShadersStructs/SpineShadersStructs.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate/SwiftDate.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SwiftNotes/SwiftNotes.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON/SwiftyJSON.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyStoreKit/SwiftyStoreKit.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/TAThirdParty/TAThirdParty.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/TZImagePickerController/TZImagePickerController.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingDataCore/ThinkingDataCore.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingSDK/ThinkingSDK.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Toast-Swift/Toast_Swift.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/UICountingLabel/UICountingLabel.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/URLNavigator/URLNavigator.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/Upsurge/Upsurge.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/YLCore/YLCore.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/YLPaymentKit/YLPaymentKit.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/YLWebView/YLWebView.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/ZLPhotoBrowser/ZLPhotoBrowser.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/Lottie.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers" -isystem "${PODS_CONFIGURATION_BUILD_DIR}/swiftScan/swiftScan.framework/Headers" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/AAInfographics" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/ActiveLabel" -iframework "${PODS_ROOT}/Ads-Global/SDK" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/Ads-Global/BUAdSDK" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Alamofire" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/AppAuth" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore" -iframework "${PODS_ROOT}/AppLovinSDK/applovin-ios-sdk-13.2.0" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/AppLovinSDK" -iframework "${PODS_ROOT}/AppsFlyerFramework/binaries/xcframework/full" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/AppsFlyerFramework/Main" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Cache" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/EFQRCode" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/EMPageViewController" -iframework "${PODS_ROOT}/FBAEMKit/XCFrameworks" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit" -iframework "${PODS_ROOT}/FBAudienceNetwork/Static" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAudienceNetwork" -iframework "${PODS_ROOT}/FBSDKCoreKit/XCFrameworks" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit" -iframework "${PODS_ROOT}/FBSDKCoreKit_Basics/XCFrameworks" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics" -iframework "${PODS_ROOT}/FBSDKLoginKit/XCFrameworks" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit" -iframework "${PODS_ROOT}/FirebaseAnalytics/Frameworks" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/AdIdSupport" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/FloatingPanel" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/GRDB.swift" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" -iframework "${PODS_ROOT}/Google-Mobile-Ads-SDK/Frameworks/GoogleMobileAdsFramework" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/Google-Mobile-Ads-SDK" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK" -iframework "${PODS_ROOT}/GoogleAppMeasurement/Frameworks" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/WithoutAdIdSupport" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" -iframework "${PODS_ROOT}/GoogleMobileAdsMediationAppLovin/AppLovinAdapter-13.2.0.0" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationAppLovin" -iframework "${PODS_ROOT}/GoogleMobileAdsMediationFacebook/MetaAdapter-6.17.1.0" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationFacebook" -iframework "${PODS_ROOT}/GoogleMobileAdsMediationPangle/PangleAdapter-7.1.1.0.1" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationPangle" -iframework "${PODS_ROOT}/GoogleMobileAdsMediationUnity/UnityAdapter-4.14.2.0" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMobileAdsMediationUnity" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn" -iframework "${PODS_ROOT}/GoogleUserMessagingPlatform/Frameworks/Release" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleUserMessagingPlatform" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" -iframework "${PODS_ROOT}/HyphenateChat" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/ImageViewer.swift" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/InputBarAccessoryView" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Jelly" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/LocoKit" -iframework "${PODS_ROOT}/LocoKitCore" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Masonry" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/MessageKit" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Moya" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Protobuf" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCOSXML" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/QCloudCore" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SQLite.swift" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SVGAPlayer" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SnapKit" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Spine" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SpineCppLite" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SpineShadersStructs" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SwiftNotes" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyStoreKit" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/TAThirdParty" -iframework "${PODS_ROOT}/TPNAdmobSDKAdapter/AnyThinkAdmobAdapter-6.4.76" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNAdmobSDKAdapter" -iframework "${PODS_ROOT}/TPNApplovinSDKAdapter/AnyThinkApplovinAdapter-6.4.76" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNApplovinSDKAdapter" -iframework "${PODS_ROOT}/TPNFacebookSDKAdapter/AnyThinkFacebookAdapter-6.4.76" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNFacebookSDKAdapter" -iframework "${PODS_ROOT}/TPNPangleSDKAdapter/AnyThinkPangleAdapter-6.4.76" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNPangleSDKAdapter" -iframework "${PODS_ROOT}/TPNUnityAdsSDKAdapter/AnyThinkUnityAdsAdapter-6.4.76" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNUnityAdsSDKAdapter" -iframework "${PODS_ROOT}/TPNiOS/core" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/TPNiOS/TPNSDK" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/TZImagePickerController" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingDataCore" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/ThinkingSDK" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Toast-Swift" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/UICountingLabel" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/URLNavigator" -iframework "${PODS_ROOT}/UnityAds" -iframework "${PODS_XCFRAMEWORKS_BUILD_DIR}/UnityAds" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/Upsurge" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/YLCore" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/YLPaymentKit" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/YLWebView" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/ZLPhotoBrowser" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/libwebp" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" -iframework "${PODS_CONFIGURATION_BUILD_DIR}/swiftScan"
OTHER_LDFLAGS = $(inherited) -ObjC -l"bz2" -l"c++" -l"c++abi" -l"iconv" -l"resolv" -l"sqlite3" -l"swiftCoreGraphics" -l"xml2" -l"z" -framework "AAInfographics" -framework "AVFAudio" -framework "AVFoundation" -framework "Accelerate" -framework "ActiveLabel" -framework "AdSupport" -framework "Alamofire" -framework "AnyThinkAdmobAdapter" -framework "AnyThinkApplovinAdapter" -framework "AnyThinkBanner" -framework "AnyThinkFacebookAdapter" -framework "AnyThinkInterstitial" -framework "AnyThinkMediaVideo" -framework "AnyThinkNative" -framework "AnyThinkPangleAdapter" -framework "AnyThinkRewardedVideo" -framework "AnyThinkSDK" -framework "AnyThinkSplash" -framework "AnyThinkUnityAdsAdapter" -framework "AppAuth" -framework "AppCheckCore" -framework "AppLovinAdapter" -framework "AppLovinSDK" -framework "AppTrackingTransparency" -framework "AppsFlyerLib" -framework "AudioToolbox" -framework "CFNetwork" -framework "Cache" -framework "CocoaLumberjack" -framework "CoreFoundation" -framework "CoreGraphics" -framework "CoreImage" -framework "CoreLocation" -framework "CoreMedia" -framework "CoreMotion" -framework "CoreTelephony" -framework "CoreText" -framework "CoreVideo" -framework "EFQRCode" -framework "EMPageViewController" -framework "FBAEMKit" -framework "FBAudienceNetwork" -framework "FBLPromises" -framework "FBSDKCoreKit" -framework "FBSDKCoreKit_Basics" -framework "FBSDKLoginKit" -framework "FirebaseAnalytics" -framework "FirebaseCore" -framework "FirebaseCoreExtension" -framework "FirebaseCoreInternal" -framework "FirebaseCrashlytics" -framework "FirebaseInstallations" -framework "FirebaseRemoteConfigInterop" -framework "FirebaseSessions" -framework "FloatingPanel" -framework "Foundation" -framework "GRDB" -framework "GTMAppAuth" -framework "GTMSessionFetcher" -framework "GoogleAppMeasurement" -framework "GoogleAppMeasurementIdentitySupport" -framework "GoogleDataTransport" -framework "GoogleMobileAds" -framework "GoogleSignIn" -framework "GoogleUtilities" -framework "Google_Mobile_Ads_SDK" -framework "HyphenateChat" -framework "IQKeyboardManagerSwift" -framework "ImageIO" -framework "ImageViewer_swift" -framework "InputBarAccessoryView" -framework "JTAppleCalendar" -framework "JXSegmentedView" -framework "JavaScriptCore" -framework "Jelly" -framework "LocalAuthentication" -framework "LocoKit" -framework "LocoKitCore" -framework "Lottie" -framework "MBProgressHUD" -framework "MJRefresh" -framework "Masonry" -framework "MediaPlayer" -framework "MessageKit" -framework "MessageUI" -framework "MetaAdapter" -framework "MobileCoreServices" -framework "Moya" -framework "Network" -framework "ObjectMapper" -framework "PAGAdSDK" -framework "PangleAdapter" -framework "Photos" -framework "PhotosUI" -framework "Promises" -framework "Protobuf" -framework "QCloudCOSXML" -framework "QCloudCore" -framework "QuartzCore" -framework "SDWebImage" -framework "SDWebImageWebPCoder" -framework "SQLite" -framework "SSZipArchive" -framework "SVGAPlayer" -framework "SafariServices" -framework "Security" -framework "SnapKit" -framework "Spine" -framework "SpineCppLite" -framework "SpineShadersStructs" -framework "StoreKit" -framework "SwiftDate" -framework "SwiftNotes" -framework "SwiftyJSON" -framework "SwiftyStoreKit" -framework "SystemConfiguration" -framework "TAThirdParty" -framework "TZImagePickerController" -framework "ThinkingDataCore" -framework "ThinkingSDK" -framework "Toast_Swift" -framework "UICountingLabel" -framework "UIKit" -framework "URLNavigator" -framework "UnityAdapter" -framework "UnityAds" -framework "Upsurge" -framework "UserMessagingPlatform" -framework "WebKit" -framework "YLCore" -framework "YLPaymentKit" -framework "YLWebView" -framework "ZLPhotoBrowser" -framework "libwebp" -framework "nanopb" -framework "swiftScan" -weak_framework "AdSupport" -weak_framework "AppTrackingTransparency" -weak_framework "AuthenticationServices" -weak_framework "CFNetwork" -weak_framework "Combine" -weak_framework "CoreML" -weak_framework "CoreMotion" -weak_framework "CoreTelephony" -weak_framework "DeviceCheck" -weak_framework "JavaScriptCore" -weak_framework "LocalAuthentication" -weak_framework "SafariServices" -weak_framework "SwiftUI" -weak_framework "SystemConfiguration" -weak_framework "VideoToolbox" -weak_framework "WebKit"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/AAInfographics" "-F${PODS_CONFIGURATION_BUILD_DIR}/ActiveLabel" "-F${PODS_CONFIGURATION_BUILD_DIR}/Ads-Global" "-F${PODS_CONFIGURATION_BUILD_DIR}/Alamofire" "-F${PODS_CONFIGURATION_BUILD_DIR}/AppAuth" "-F${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/AppLovinSDK" "-F${PODS_CONFIGURATION_BUILD_DIR}/AppsFlyerFramework" "-F${PODS_CONFIGURATION_BUILD_DIR}/Cache" "-F${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack" "-F${PODS_CONFIGURATION_BUILD_DIR}/EFQRCode" "-F${PODS_CONFIGURATION_BUILD_DIR}/EMPageViewController" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBAEMKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBAudienceNetwork" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBSDKCoreKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBSDKCoreKit_Basics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBSDKLoginKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAnalytics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions" "-F${PODS_CONFIGURATION_BUILD_DIR}/FloatingPanel" "-F${PODS_CONFIGURATION_BUILD_DIR}/GRDB.swift" "-F${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth" "-F${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" "-F${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleAppMeasurement" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMobileAdsMediationAppLovin" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMobileAdsMediationFacebook" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMobileAdsMediationPangle" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMobileAdsMediationUnity" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUserMessagingPlatform" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "-F${PODS_CONFIGURATION_BUILD_DIR}/HyphenateChat" "-F${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift" "-F${PODS_CONFIGURATION_BUILD_DIR}/ImageViewer.swift" "-F${PODS_CONFIGURATION_BUILD_DIR}/InputBarAccessoryView" "-F${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar" "-F${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView" "-F${PODS_CONFIGURATION_BUILD_DIR}/Jelly" "-F${PODS_CONFIGURATION_BUILD_DIR}/LocoKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/LocoKitCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD" "-F${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" "-F${PODS_CONFIGURATION_BUILD_DIR}/Masonry" "-F${PODS_CONFIGURATION_BUILD_DIR}/MessageKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/Moya" "-F${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift" "-F${PODS_CONFIGURATION_BUILD_DIR}/Protobuf" "-F${PODS_CONFIGURATION_BUILD_DIR}/QCloudCOSXML" "-F${PODS_CONFIGURATION_BUILD_DIR}/QCloudCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SQLite.swift" "-F${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive" "-F${PODS_CONFIGURATION_BUILD_DIR}/SVGAPlayer" "-F${PODS_CONFIGURATION_BUILD_DIR}/SnapKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/Spine" "-F${PODS_CONFIGURATION_BUILD_DIR}/SpineCppLite" "-F${PODS_CONFIGURATION_BUILD_DIR}/SpineShadersStructs" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftNotes" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftyStoreKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/TAThirdParty" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPNAdmobSDKAdapter" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPNApplovinSDKAdapter" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPNFacebookSDKAdapter" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPNPangleSDKAdapter" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPNUnityAdsSDKAdapter" "-F${PODS_CONFIGURATION_BUILD_DIR}/TPNiOS" "-F${PODS_CONFIGURATION_BUILD_DIR}/TZImagePickerController" "-F${PODS_CONFIGURATION_BUILD_DIR}/ThinkingDataCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/ThinkingSDK" "-F${PODS_CONFIGURATION_BUILD_DIR}/Toast-Swift" "-F${PODS_CONFIGURATION_BUILD_DIR}/UICountingLabel" "-F${PODS_CONFIGURATION_BUILD_DIR}/URLNavigator" "-F${PODS_CONFIGURATION_BUILD_DIR}/UnityAds" "-F${PODS_CONFIGURATION_BUILD_DIR}/Upsurge" "-F${PODS_CONFIGURATION_BUILD_DIR}/YLCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/YLPaymentKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/YLWebView" "-F${PODS_CONFIGURATION_BUILD_DIR}/ZLPhotoBrowser" "-F${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "-F${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "-F${PODS_CONFIGURATION_BUILD_DIR}/swiftScan"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USER_HEADER_SEARCH_PATHS = $(inherited) $(SRCROOT)/libwebp/src
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
