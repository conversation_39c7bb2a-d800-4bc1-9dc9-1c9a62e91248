//
//  QCloudCOSTransferMangerService.h
//  QCloudCOSTransferMangerService
//
//  Created by tencent
//
//
//   ██████╗  ██████╗██╗      ██████╗ ██╗   ██╗██████╗     ████████╗███████╗██████╗ ███╗   ███╗██╗███╗   ██╗ █████╗ ██╗         ██╗      █████╗
//   ██████╗
//  ██╔═══██╗██╔════╝██║     ██╔═══██╗██║   ██║██╔══██╗    ╚══██╔══╝██╔════╝██╔══██╗████╗ ████║██║████╗  ██║██╔══██╗██║         ██║ ██╔══██╗██╔══██╗
//  ██║   ██║██║     ██║     ██║   ██║██║   ██║██║  ██║       ██║   █████╗  ██████╔╝██╔████╔██║██║██╔██╗ ██║███████║██║         ██║ ███████║██████╔╝
//  ██║▄▄ ██║██║     ██║     ██║   ██║██║   ██║██║  ██║       ██║   ██╔══╝  ██╔══██╗██║╚██╔╝██║██║██║╚██╗██║██╔══██║██║         ██║ ██╔══██║██╔══██╗
//  ╚██████╔╝╚██████╗███████╗╚██████╔╝╚██████╔╝██████╔╝       ██║   ███████╗██║  ██║██║ ╚═╝ ██║██║██║ ╚████║██║  ██║███████╗    ███████╗██║
//  ██║██████╔╝
//   ╚══▀▀═╝  ╚═════╝╚══════╝ ╚═════╝  ╚═════╝ ╚═════╝        ╚═╝   ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝╚══════╝    ╚══════╝╚═╝ ╚═╝╚═════╝
//
//
//                                                                              _             __                 _                _
//                                                                             (_)           / _|               | |              | |
//                                                          ___  ___ _ ____   ___  ___ ___  | |_ ___  _ __    __| | _____   _____| | ___  _ __   ___ _
//                                                          __ ___
//                                                         / __|/ _ \ '__\ \ / / |/ __/ _ \ |  _/ _ \| '__|  / _` |/ _ \ \ / / _ \ |/ _ \| '_ \ / _ \
//                                                         '__/ __|
//                                                         \__ \  __/ |   \ V /| | (_|  __/ | || (_) | |    | (_| |  __/\ V /  __/ | (_) | |_) |  __/
//                                                         |  \__
//                                                         |___/\___|_|    \_/ |_|\___\___| |_| \___/|_|     \__,_|\___| \_/ \___|_|\___/| .__/
//                                                         \___|_|  |___/
//    ______ ______ ______ ______ ______ ______ ______ ______                                                                            | |
//   |______|______|______|______|______|______|______|______|                                                                           |_|
//

#import <Foundation/Foundation.h>
#import <QCloudCore/QCloudService.h>

@class QCloudCOSXMLService;
@class QCloudCOSXMLUploadObjectRequest;
@class QCloudCOSXMLCopyObjectRequest;
@class QCloudCOSXMLDownloadObjectRequest;
NS_ASSUME_NONNULL_BEGIN
@interface QCloudCOSTransferMangerService : QCloudService

@property (nonatomic, strong, readonly) QCloudCOSXMLService *cosService;

#pragma hidden super selectors
- (int)performRequest:(QCloudBizHTTPRequest *)httpRequst NS_UNAVAILABLE;
- (int)performRequest:(QCloudBizHTTPRequest *)httpRequst withFinishBlock:(QCloudRequestFinishBlock)block NS_UNAVAILABLE;

#pragma Factory
+ (QCloudCOSTransferMangerService *)defaultCOSTransferManager;
+ (QCloudCOSTransferMangerService *)costransfermangerServiceForKey:(NSString *)key;
+ (QCloudCOSTransferMangerService *)registerDefaultCOSTransferMangerWithConfiguration:(QCloudServiceConfiguration *)configuration;
+ (QCloudCOSTransferMangerService *)registerCOSTransferMangerWithConfiguration:(QCloudServiceConfiguration *)configuration withKey:(NSString *)key;

- (void)UploadObject:(QCloudCOSXMLUploadObjectRequest *)upload;
- (void)CopyObject:(QCloudCOSXMLCopyObjectRequest *)copy;
- (void)DownloadObject:(QCloudCOSXMLDownloadObjectRequest *)get;


/// 是否注册了该key对应的服务
+ (BOOL)hasTransferMangerServiceForKey:(NSString *)key;

/// 删除key对应的服务
+ (void)removeTransferMangerServiceWithKey:(NSString *)key;
@end
NS_ASSUME_NONNULL_END
