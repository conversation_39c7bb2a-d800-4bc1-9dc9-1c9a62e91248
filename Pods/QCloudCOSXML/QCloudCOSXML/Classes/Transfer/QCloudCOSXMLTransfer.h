//
//  QCloudCOSXMLTransfe.h
//  QCloudCOSXML
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON>(张恒铭) on 08/12/2017.
//

#ifndef QCloudCOSXMLTransfe_h
#define QCloudCOSXMLTransfe_h

#import "QCloudCOSXMLEndPoint.h"
#import "QCloudCOSXMLService+Transfer.h"
#import "QCloudCOSTransferMangerService.h"

#import "QCloudCOSXMLUploadObjectRequest.h"
#import "QCloudCOSXMLUploadObjectRequest_Private.h"
#import "QCloudPutObjectRequest.h"
#import "QCloudGetObjectRequest.h"
#import "QCloudInitiateMultipartUploadRequest.h"
#import "QCloudUploadPartRequest.h"
#import "QCloudCompleteMultipartUploadRequest.h"
#import "QCloudAbortMultipfartUploadRequest.h"
#import "QCloudListMultipartRequest.h"
#import "QCloudHeadObjectRequest.h"
#import "QCloudPutObjectCopyRequest.h"
#import "QCloudUploadPartCopyRequest.h"


#endif /* QCloudCOSXMLTransfe_h */
