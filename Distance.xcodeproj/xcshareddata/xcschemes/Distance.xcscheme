<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1340"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "CC51A4902522CB6E0051C60F"
               BuildableName = "Distance.app"
               BlueprintName = "Distance"
               ReferencedContainer = "container:Distance.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      disableMainThreadChecker = "YES"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      enableGPUFrameCaptureMode = "3"
      enableGPUValidationMode = "1"
      allowLocationSimulation = "YES"
      consoleMode = "0"
      structuredConsoleMode = "1">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "CC51A4902522CB6E0051C60F"
            BuildableName = "Distance.app"
            BlueprintName = "Distance"
            ReferencedContainer = "container:Distance.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
         <CommandLineArgument
            argument = "-FIRDebugEnabled"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-noFIRAnalyticsDebugEnabled"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-FIRAnalyticsDebugEnabled"
            isEnabled = "NO">
         </CommandLineArgument>
      </CommandLineArguments>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "DYLD_PRINT_STATISTICS"
            value = "1"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
      <AdditionalOptions>
         <AdditionalOption
            key = "APP_DISTRIBUTOR_ID_OVERRIDE"
            value = "com.apple.AppStore"
            isEnabled = "YES">
         </AdditionalOption>
      </AdditionalOptions>
      <LocationScenarioReference
         identifier = "com.apple.dt.IDEFoundation.CurrentLocationScenarioIdentifier"
         referenceType = "1">
      </LocationScenarioReference>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Debug"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "CC51A4902522CB6E0051C60F"
            BuildableName = "Distance.app"
            BlueprintName = "Distance"
            ReferencedContainer = "container:Distance.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
