# Uncomment the next line to define a global platform for your project
platform :ios, '13.0'
use_frameworks!

#use_frameworks! :linkage => :static
#install! 'cocoapods', :disable_input_output_paths => true
source 'http://192.168.1.81:8081/iOS/Repo/repo.git'
source 'https://github.com/CocoaPods/Specs.git'
source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'

inhibit_all_warnings!

target 'Distance' do
  # Comment the next line if you don't want to use dynamic frameworks
      
  # Pods for Distance
  pod 'SnapKit'
  pod 'Moya'
  pod 'SwiftDate'
  pod 'SwiftyJSON', :git => 'https://github.com/SwiftyJSON/SwiftyJSON/'
  pod 'SDWebImage'
  pod 'SDWebImageWebPCoder'
  pod 'ObjectMapper'
  pod 'Toast-Swift'
  pod 'IQKeyboardManagerSwift'
  pod 'Jelly'
  pod 'Cache', :git => 'https://github.com/hyperoslo/Cache.git'
  pod 'SVGAPlayer', '~> 2.5.7'
  pod 'lottie-ios'
  pod 'YLWebView'
  pod 'EFQRCode'
  pod 'ActiveLabel'
  pod 'URLNavigator'
  pod 'UICountingLabel'
  pod 'EMPageViewController'
  pod 'JTAppleCalendar', '~> 8.0.5'
  pod 'ZLPhotoBrowser', '~> 4.1.6'
  pod 'MJRefresh'
  pod 'ImageViewer.swift', '~> 3.0'
  pod 'ImageViewer.swift/Fetcher', '~> 3.0'
  pod 'SwiftyStoreKit'
  pod 'MessageKit', '~> 3.3.0'
  pod 'QCloudCOSXML/Transfer'
  pod 'CocoaLumberjack/Swift'
  pod 'YLCore/Date'
  pod 'HyphenateChat', '3.9.8'
  pod 'FirebaseAnalytics'
  pod 'FirebaseCrashlytics'
  pod 'SQLite.swift', :git => 'https://github.com/stephencelis/SQLite.swift.git'
  pod 'LocoKit'
  pod 'LocoKitCore'
  pod 'SwiftNotes'

  pod 'JXSegmentedView'
  
 # pod 'Fastboard', '~> 1.0.8'
  pod 'TZImagePickerController'
  pod 'MBProgressHUD', :git => 'https://github.com/jdg/MBProgressHUD.git'
  pod 'AAInfographics', :git => 'https://github.com/AAChartModel/AAChartKit-Swift.git'
  
  pod 'ThinkingSDK', :git => 'https://github.com/ThinkingDataAnalytics/ios-sdk.git'  #ThinkingSDK 数数
  pod 'TAThirdParty'
  pod 'swiftScan'
  
 
  pod 'AppsFlyerFramework'
  pod 'FBSDKCoreKit'
  pod 'FBSDKLoginKit'
  pod 'Masonry'
  pod 'FloatingPanel'
  
  pod 'Spine', :podspec => 'https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/Spine.podspec'
  pod 'SpineCppLite', :podspec => 'https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/SpineCppLite.podspec'
  pod 'SpineShadersStructs', :podspec => 'https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/SpineShadersStructs.podspec'

  pod 'GoogleSignIn'
  pod 'Google-Mobile-Ads-SDK'
  
  # Meta Audience Network
  pod 'GoogleMobileAdsMediationFacebook'

  # AppLovin
  pod 'GoogleMobileAdsMediationAppLovin'

  # Unity Ads
  pod 'GoogleMobileAdsMediationUnity'

  # Pangle
  pod 'GoogleMobileAdsMediationPangle'
  
  #topon 广告
  pod 'TPNiOS'#,'6.4.86'
  pod 'TPNUnityAdsSDKAdapter'#,'6.4.86'
  pod 'TPNPangleSDKAdapter'#,'~> 6.4.86'
  pod 'TPNFacebookSDKAdapter'#,'6.4.86'
  pod 'TPNAdmobSDKAdapter'#,'6.4.86'
  pod 'TPNApplovinSDKAdapter'#,'6.4.86'
  # v2内购
  pod 'YLPaymentKit/Core', :git => 'http://gitlab.ylwnl.com/youloft-ios-framework/ylpaymentkit.git', :tag => '1.0.3'  # 核心功能
  pod 'YLPaymentKit/IAP', :git => 'http://gitlab.ylwnl.com/youloft-ios-framework/ylpaymentkit.git', :tag => '1.0.3'   # 内购功能

end


target 'DistanceWidgetExtension' do
  # Comment the next line if you don't want to use dynamic frameworks
  # Pods for DistanceWidgetExtension
  pod 'Moya'
  pod 'SwiftDate'
  pod 'SwiftyJSON', :git => 'https://github.com/SwiftyJSON/SwiftyJSON/'
  pod 'SDWebImage'
end

#post_install do |pi|
#  # https://github.com/CocoaPods/CocoaPods/issues/7314
#  fix_deployment_target(pi)
#end
# xcode 15 适配
post_install do |installer|
  installer.aggregate_targets.each do |target|
    target.xcconfigs.each do |variant, xcconfig|
      xcconfig_path = target.client_root + target.xcconfig_relative_path(variant)
      IO.write(xcconfig_path, IO.read(xcconfig_path).gsub("DT_TOOLCHAIN_DIR", "TOOLCHAIN_DIR"))
    end
  end
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      if config.base_configuration_reference.is_a? Xcodeproj::Project::Object::PBXFileReference
        xcconfig_path = config.base_configuration_reference.real_path
        IO.write(xcconfig_path, IO.read(xcconfig_path).gsub("DT_TOOLCHAIN_DIR", "TOOLCHAIN_DIR"))
      end
    end
  end
  # https://github.com/CocoaPods/CocoaPods/issues/7314
  fix_deployment_target(installer)
end 

def fix_deployment_target(pod_installer)
  if !pod_installer
    return
  end
  puts "Make the pods deployment target version the same as our target"
  
  project = pod_installer.pods_project
  deploymentMap = {}
  project.build_configurations.each do |config|
    deploymentMap[config.name] = config.build_settings['IPHONEOS_DEPLOYMENT_TARGET']
  end
  # p deploymentMap
  
  project.targets.each do |t|
    puts "  #{t.name}"
    t.build_configurations.each do |config|
      oldTarget = config.build_settings['IPHONEOS_DEPLOYMENT_TARGET']
      newTarget = deploymentMap[config.name]
      if oldTarget.to_f >= newTarget.to_f
        next
      end
      puts "    #{config.name} deployment target: #{oldTarget} => #{newTarget}"
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = newTarget
    end
  end
end
