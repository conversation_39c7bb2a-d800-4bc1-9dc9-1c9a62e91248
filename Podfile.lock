PODS:
  - AAInfographics (7.3.0)
  - ActiveLabel (1.1.0)
  - Ads-Global (7.1.1.0):
    - Ads-Global/BUAdSDK (= 7.1.1.0)
  - Ads-Global/BUAdSDK (7.1.1.0)
  - Alamofire (5.9.1)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - AppLovinSDK (13.2.0)
  - AppsFlyerFramework (6.15.3):
    - AppsFlyerFramework/Main (= 6.15.3)
  - AppsFlyerFramework/Main (6.15.3)
  - <PERSON>ache (7.1.0)
  - CocoaLumberjack/Core (3.8.5)
  - CocoaLumberjack/Swift (3.8.5):
    - CocoaLumberjack/Core
  - EFQRCode (6.0.0):
    - swift_qrcodejs (~> 1.2.0)
  - EMPageViewController (4.0.0)
  - FBAEMKit (17.3.0):
    - FBSDKCoreKit_Basics (= 17.3.0)
  - FBAudienceNetwork (6.17.1)
  - FBSDKCoreKit (17.3.0):
    - FBAEMKit (= 17.3.0)
    - FBSDKCoreKit_Basics (= 17.3.0)
  - FBSDKCoreKit_Basics (17.3.0)
  - FBSDKLoginKit (17.3.0):
    - FBSDKCoreKit (= 17.3.0)
  - FirebaseAnalytics (11.3.0):
    - FirebaseAnalytics/AdIdSupport (= 11.3.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.3.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.3.0):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.3.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.3.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.3.0)
  - FirebaseSessions (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FloatingPanel (2.8.6)
  - Google-Mobile-Ads-SDK (12.3.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement (11.3.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.3.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.3.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMobileAdsMediationAppLovin (13.2.0.0):
    - AppLovinSDK (= 13.2.0)
    - Google-Mobile-Ads-SDK (~> 12.0)
  - GoogleMobileAdsMediationFacebook (6.17.1.0):
    - FBAudienceNetwork (= 6.17.1)
    - Google-Mobile-Ads-SDK (~> 12.0)
  - GoogleMobileAdsMediationPangle (7.1.1.0.1):
    - Ads-Global (= 7.1.1.0)
    - Google-Mobile-Ads-SDK (~> 12.0)
  - GoogleMobileAdsMediationUnity (4.14.2.0):
    - Google-Mobile-Ads-SDK (~> 12.0)
    - UnityAds (= 4.14.2)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GRDB.swift (4.14.0):
    - GRDB.swift/standard (= 4.14.0)
  - GRDB.swift/standard (4.14.0)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - HyphenateChat (3.9.8)
  - ImageViewer.swift (3.3.3)
  - ImageViewer.swift/Fetcher (3.3.3):
    - SDWebImage
  - InputBarAccessoryView (5.1.0):
    - InputBarAccessoryView/Core (= 5.1.0)
  - InputBarAccessoryView/Core (5.1.0)
  - IQKeyboardManagerSwift (6.5.16)
  - Jelly (2.3.0)
  - JTAppleCalendar (8.0.5)
  - JXSegmentedView (1.4.1)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - LocoKit (7.0.0):
    - LocoKit/Base (= 7.0.0)
  - LocoKit/Base (7.0.0):
    - GRDB.swift (~> 4)
    - LocoKitCore (= 7.0.0)
    - Upsurge (~> 0.10)
  - LocoKitCore (7.0.0)
  - lottie-ios (4.4.1)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - MessageKit (3.3.0):
    - InputBarAccessoryView (~> 5.1.0)
  - MJRefresh (3.7.9)
  - Moya (15.0.0):
    - Moya/Core (= 15.0.0)
  - Moya/Core (15.0.0):
    - Alamofire (~> 5.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - ObjectMapper (4.2.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - Protobuf (3.17.0)
  - QCloudCore/WithoutMTA (6.5.1)
  - QCloudCOSXML/Transfer (6.5.1):
    - QCloudCore/WithoutMTA (= 6.5.1)
  - SDWebImage (5.19.1):
    - SDWebImage/Core (= 5.19.1)
  - SDWebImage/Core (5.19.1)
  - SDWebImageWebPCoder (0.14.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SnapKit (5.7.1)
  - Spine (4.2.0):
    - SpineCppLite
    - SpineShadersStructs
  - SpineCppLite (4.2.0)
  - SpineShadersStructs (4.2.0)
  - SQLite.swift (0.15.0):
    - SQLite.swift/standard (= 0.15.0)
  - SQLite.swift/standard (0.15.0)
  - SSZipArchive (2.4.2)
  - SVGAPlayer (2.5.7):
    - SVGAPlayer/Core (= 2.5.7)
    - SVGAPlayer/ProtoFiles (= 2.5.7)
  - SVGAPlayer/Core (2.5.7):
    - SSZipArchive (>= 1.8.1)
    - SVGAPlayer/ProtoFiles
  - SVGAPlayer/ProtoFiles (2.5.7):
    - Protobuf (~> 3.4)
  - SwiftDate (6.3.1)
  - SwiftNotes (1.1.0)
  - swiftScan (1.2.1)
  - SwiftyJSON (5.0.2)
  - SwiftyStoreKit (0.16.1)
  - TAThirdParty (0.3.5)
  - ThinkingDataCore (1.0.2):
    - ThinkingDataCore/Main (= 1.0.2)
  - ThinkingDataCore/iOS (1.0.2)
  - ThinkingDataCore/Main (1.0.2):
    - ThinkingDataCore/iOS
    - ThinkingDataCore/OSX
  - ThinkingSDK (3.0.2):
    - ThinkingSDK/Main (= 3.0.2)
  - ThinkingSDK/iOS (3.0.2):
    - ThinkingDataCore (>= 1.0.2)
  - ThinkingSDK/Main (3.0.2):
    - ThinkingSDK/iOS
    - ThinkingSDK/OSX
  - Toast-Swift (5.1.0)
  - TPNAdmobSDKAdapter (6.4.76):
    - Google-Mobile-Ads-SDK (= 12.3.0)
    - TPNiOS (= 6.4.76)
  - TPNApplovinSDKAdapter (6.4.76):
    - AppLovinSDK (= 13.2.0)
    - TPNiOS (= 6.4.76)
  - TPNFacebookSDKAdapter (6.4.76):
    - FBAudienceNetwork (= 6.17.1)
    - TPNiOS (= 6.4.76)
  - TPNiOS (6.4.76):
    - TPNiOS/TPNSDK (= 6.4.76)
  - TPNiOS/TPNSDK (6.4.76)
  - TPNPangleSDKAdapter (6.4.76):
    - Ads-Global (= 7.1.1.0)
    - TPNiOS (= 6.4.76)
  - TPNUnityAdsSDKAdapter (6.4.76):
    - TPNiOS (= 6.4.76)
    - UnityAds (= 4.14.2)
  - TZImagePickerController (3.8.3):
    - TZImagePickerController/Basic (= 3.8.3)
    - TZImagePickerController/Location (= 3.8.3)
  - TZImagePickerController/Basic (3.8.3)
  - TZImagePickerController/Location (3.8.3)
  - UICountingLabel (1.4.1)
  - UnityAds (4.14.2)
  - Upsurge (0.10.2)
  - URLNavigator (2.3.0)
  - YLCore/Date (1.3.6)
  - YLPaymentKit/Core (1.0.1)
  - YLPaymentKit/IAP (1.0.1):
    - YLPaymentKit/Core
  - YLWebView (1.3.1)
  - ZLPhotoBrowser (4.1.6):
    - ZLPhotoBrowser/Core (= 4.1.6)
  - ZLPhotoBrowser/Core (4.1.6)

DEPENDENCIES:
  - AAInfographics (from `https://github.com/AAChartModel/AAChartKit-Swift.git`)
  - ActiveLabel
  - AppsFlyerFramework
  - Cache (from `https://github.com/hyperoslo/Cache.git`)
  - CocoaLumberjack/Swift
  - EFQRCode
  - EMPageViewController
  - FBSDKCoreKit
  - FBSDKLoginKit
  - FirebaseAnalytics
  - FirebaseCrashlytics
  - FloatingPanel
  - Google-Mobile-Ads-SDK
  - GoogleMobileAdsMediationAppLovin
  - GoogleMobileAdsMediationFacebook
  - GoogleMobileAdsMediationPangle
  - GoogleMobileAdsMediationUnity
  - GoogleSignIn
  - HyphenateChat (= 3.9.8)
  - ImageViewer.swift (~> 3.0)
  - ImageViewer.swift/Fetcher (~> 3.0)
  - IQKeyboardManagerSwift
  - Jelly
  - JTAppleCalendar (~> 8.0.5)
  - JXSegmentedView
  - LocoKit
  - LocoKitCore
  - lottie-ios
  - Masonry
  - MBProgressHUD (from `https://github.com/jdg/MBProgressHUD.git`)
  - MessageKit (~> 3.3.0)
  - MJRefresh
  - Moya
  - ObjectMapper
  - QCloudCOSXML/Transfer
  - SDWebImage
  - SDWebImageWebPCoder
  - SnapKit
  - Spine (from `https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/Spine.podspec`)
  - SpineCppLite (from `https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/SpineCppLite.podspec`)
  - SpineShadersStructs (from `https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/SpineShadersStructs.podspec`)
  - SQLite.swift (from `https://github.com/stephencelis/SQLite.swift.git`)
  - SVGAPlayer (~> 2.5.7)
  - SwiftDate
  - SwiftNotes
  - swiftScan
  - SwiftyJSON (from `https://github.com/SwiftyJSON/SwiftyJSON/`)
  - SwiftyStoreKit
  - TAThirdParty
  - ThinkingSDK (from `https://github.com/ThinkingDataAnalytics/ios-sdk.git`)
  - Toast-Swift
  - TPNAdmobSDKAdapter
  - TPNApplovinSDKAdapter
  - TPNFacebookSDKAdapter
  - TPNiOS
  - TPNPangleSDKAdapter
  - TPNUnityAdsSDKAdapter
  - TZImagePickerController
  - UICountingLabel
  - URLNavigator
  - YLCore/Date
  - YLPaymentKit/Core (from `http://gitlab.ylwnl.com/youloft-ios-framework/ylpaymentkit.git`, tag `1.0.3`)
  - YLPaymentKit/IAP (from `http://gitlab.ylwnl.com/youloft-ios-framework/ylpaymentkit.git`, tag `1.0.3`)
  - YLWebView
  - ZLPhotoBrowser (~> 4.1.6)

SPEC REPOS:
  http://192.168.1.81:8081/iOS/Repo/repo.git:
    - YLCore
    - YLWebView
  https://github.com/CocoaPods/Specs.git:
    - JXSegmentedView
    - LocoKitCore
    - Masonry
    - QCloudCore
    - QCloudCOSXML
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - ActiveLabel
    - Ads-Global
    - Alamofire
    - AppAuth
    - AppCheckCore
    - AppLovinSDK
    - AppsFlyerFramework
    - CocoaLumberjack
    - EFQRCode
    - EMPageViewController
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FloatingPanel
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMobileAdsMediationAppLovin
    - GoogleMobileAdsMediationFacebook
    - GoogleMobileAdsMediationPangle
    - GoogleMobileAdsMediationUnity
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GRDB.swift
    - GTMAppAuth
    - GTMSessionFetcher
    - HyphenateChat
    - ImageViewer.swift
    - InputBarAccessoryView
    - IQKeyboardManagerSwift
    - Jelly
    - JTAppleCalendar
    - libwebp
    - LocoKit
    - lottie-ios
    - MessageKit
    - MJRefresh
    - Moya
    - nanopb
    - ObjectMapper
    - PromisesObjC
    - PromisesSwift
    - Protobuf
    - SDWebImage
    - SDWebImageWebPCoder
    - SnapKit
    - SSZipArchive
    - SVGAPlayer
    - SwiftDate
    - SwiftNotes
    - swiftScan
    - SwiftyStoreKit
    - TAThirdParty
    - ThinkingDataCore
    - Toast-Swift
    - TPNAdmobSDKAdapter
    - TPNApplovinSDKAdapter
    - TPNFacebookSDKAdapter
    - TPNiOS
    - TPNPangleSDKAdapter
    - TPNUnityAdsSDKAdapter
    - TZImagePickerController
    - UICountingLabel
    - UnityAds
    - Upsurge
    - URLNavigator
    - ZLPhotoBrowser

EXTERNAL SOURCES:
  AAInfographics:
    :git: https://github.com/AAChartModel/AAChartKit-Swift.git
  Cache:
    :git: https://github.com/hyperoslo/Cache.git
  MBProgressHUD:
    :git: https://github.com/jdg/MBProgressHUD.git
  Spine:
    :podspec: https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/Spine.podspec
  SpineCppLite:
    :podspec: https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/SpineCppLite.podspec
  SpineShadersStructs:
    :podspec: https://raw.githubusercontent.com/EsotericSoftware/spine-runtimes/4.2/SpineShadersStructs.podspec
  SQLite.swift:
    :git: https://github.com/stephencelis/SQLite.swift.git
  SwiftyJSON:
    :git: https://github.com/SwiftyJSON/SwiftyJSON/
  ThinkingSDK:
    :git: https://github.com/ThinkingDataAnalytics/ios-sdk.git
  YLPaymentKit:
    :git: http://gitlab.ylwnl.com/youloft-ios-framework/ylpaymentkit.git
    :tag: 1.0.3

CHECKOUT OPTIONS:
  AAInfographics:
    :commit: 30f36c21323cfe188dbed2d9e1b7b01fbea6e445
    :git: https://github.com/AAChartModel/AAChartKit-Swift.git
  Cache:
    :commit: f44a8f6b5ec27730198725ccc542fef0d1cc6b3d
    :git: https://github.com/hyperoslo/Cache.git
  MBProgressHUD:
    :commit: 18c442d57398cee5ef57f852df10fc5ff65f0763
    :git: https://github.com/jdg/MBProgressHUD.git
  Spine:
    :commit: 2864e323c308fc66348f890260d654611c64130f
    :git: https://github.com/esotericsoftware/spine-runtimes.git
  SpineCppLite:
    :commit: 2864e323c308fc66348f890260d654611c64130f
    :git: https://github.com/esotericsoftware/spine-runtimes.git
  SpineShadersStructs:
    :commit: 2864e323c308fc66348f890260d654611c64130f
    :git: https://github.com/esotericsoftware/spine-runtimes.git
  SQLite.swift:
    :commit: c36138022a690d691d2597b11a477b02baa491c6
    :git: https://github.com/stephencelis/SQLite.swift.git
  SwiftyJSON:
    :commit: af76cf3ef710b6ca5f8c05f3a31307d44a3c5828
    :git: https://github.com/SwiftyJSON/SwiftyJSON/
  ThinkingSDK:
    :commit: 6440d1b9023e5006ac08d12096947b69608701e8
    :git: https://github.com/ThinkingDataAnalytics/ios-sdk.git
  YLPaymentKit:
    :git: http://gitlab.ylwnl.com/youloft-ios-framework/ylpaymentkit.git
    :tag: 1.0.3

SPEC CHECKSUMS:
  AAInfographics: ee2a7959acdd0a00243b7f5a3f03a1fa2bdf0781
  ActiveLabel: 5e3f4de79a1952d4604b845a0610d4776e4b82b3
  Ads-Global: a37caba17e88311462278dd2e2b0d3a5dcbbfce2
  Alamofire: f36a35757af4587d8e4f4bfa223ad10be2422b8c
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  AppLovinSDK: 1eb88a82b4feea9f3696ce9cc1e3343c4997ee12
  AppsFlyerFramework: ad7ff0d22aa36c7f8cc4f71a5424e19b89ccb8ae
  Cache: 79717afe1491def3c772c242bc1166766d7b9b36
  CocoaLumberjack: 6a459bc897d6d80bd1b8c78482ec7ad05dffc3f0
  EFQRCode: 27f7ef4382159d79d583d839cdfc596b5ac98c9f
  EMPageViewController: de8c8d6ea44c77bc2463f2cb9b4128390d2391f2
  FBAEMKit: 92f488a9c97208e2f6f01655750944d213fbc0b0
  FBAudienceNetwork: eb3ffbf2b35df25e21e163657044ffef66616a40
  FBSDKCoreKit: 1be47a742ccdddf8dd85272c3953534214dc0f30
  FBSDKCoreKit_Basics: fcafe75f7deeed1146d04ea51b45a69731e63c16
  FBSDKLoginKit: ad0d6485feace32d65f407f6a288c4dfa8ec559c
  FirebaseAnalytics: ce1593872635a5ebd715d0d3937fab195991ecc9
  FirebaseCore: 8542de610f35f86196ba26cdb2544565a5157c8e
  FirebaseCoreExtension: 30bb063476ef66cd46925243d64ad8b2c8ac3264
  FirebaseCoreInternal: ac26d09a70c730e497936430af4e60fb0c68ec4e
  FirebaseCrashlytics: ba7b6a55dc10393f6583d87d8600d0d3ab2671d8
  FirebaseInstallations: 58cf94dabf1e2bb2fa87725a9be5c2249171cda0
  FirebaseRemoteConfigInterop: c3a5c31b3c22079f41ba1dc645df889d9ce38cb9
  FirebaseSessions: 655ff17f3cc1a635cbdc2d69b953878001f9e25b
  FloatingPanel: 11559d3700c39f1c2ee8dbb7a5415223b52fdce0
  Google-Mobile-Ads-SDK: 01b1b77e0bdba8b4027ae62e9bd52e5a95d930aa
  GoogleAppMeasurement: c8bac5f6ad85d3a0bdf2b9aee7fd484d6615d486
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMobileAdsMediationAppLovin: 84f9181ab13ad890f1280710150afc4454e32fa3
  GoogleMobileAdsMediationFacebook: 750a2c14ce0bdf813e4e1d4ab4aa283f72f8a875
  GoogleMobileAdsMediationPangle: 6ebf2dd8edfdcf0839024f1541062abe41e06b99
  GoogleMobileAdsMediationUnity: 7beb7c5375d3a747c8f4d9149df280725d06027a
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GRDB.swift: 8cc3ab7c8b8b4ac1761deb8dca4b51c1c9d429f8
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HyphenateChat: 050c6134995936e52866e1347d0360b3ab1b03d6
  ImageViewer.swift: 3dbb74a68ac4bdd6cdd585b3128dae17ccd733e5
  InputBarAccessoryView: 19953f486a23e846e9487099f92bbe3456e46ce5
  IQKeyboardManagerSwift: 12d89768845bb77b55cc092ecc2b1f9370f06b76
  Jelly: 5c6315db07d392b178ebf9edf27072a294d4f434
  JTAppleCalendar: 16c6501b22cb27520372c28b0a2e0b12c8d0cd73
  JXSegmentedView: cd73555ce2134d1656db2cb383ba9c2f36fb5078
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  LocoKit: 8a06074e90dfd24ee10e94c9f5274e300f6b9d2f
  LocoKitCore: 30cff6a1e4ac5a32eefe232c19e24fd79485661d
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 1b0fb447e80a0fda94808180750e8b78a07b3cd2
  MessageKit: 1f07618183be6d92b648ba7a384aafa097362a00
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  Moya: 138f0573e53411fb3dc17016add0b748dfbd78ee
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  ObjectMapper: 1eb41f610210777375fa806bf161dc39fb832b81
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  QCloudCore: 6f8c67b96448472d2c6a92b9cfe1bdb5abbb1798
  QCloudCOSXML: 92f50a787b4e8d9a7cb6ea8e626775256b4840a7
  SDWebImage: 40b0b4053e36c660a764958bff99eed16610acbb
  SDWebImageWebPCoder: c94f09adbca681822edad9e532ac752db713eabf
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  Spine: fab8ea7427af5f7c4ffa42259989b404759389b0
  SpineCppLite: 0c05549dc08fff40da8853e079606e559f6036ce
  SpineShadersStructs: 51ae786df4f4ea9ca52d7d5ff365d2018b561346
  SQLite.swift: 3788ee6fb628d32f4ffb633ba9fab058b4b93418
  SSZipArchive: e7b4f3d9e780c2acc1764cd88fbf2de28f26e5b2
  SVGAPlayer: 318b85a78b61292d6ae9dfcd651f3f0d1cdadd86
  SwiftDate: 72d28954e8e1c6c1c0f917ccc8005e4f83c7d4b2
  SwiftNotes: 51d71568d7515c5c82aa791b88900cf8059b8beb
  swiftScan: 0849e7d7c1c641384767a573ee8cb90b88d1ddd9
  SwiftyJSON: 576fbf26942d5ef414daad8870b1642413ecc00c
  SwiftyStoreKit: 6b9c08810269f030586dac1fae8e75871a82e84a
  TAThirdParty: 65db0235cd209237781535f19f9d0eef706156e5
  ThinkingDataCore: 381f642f0c17b5d75621bb068fffb78882569e9e
  ThinkingSDK: 798d57d7ec0d2c5ff5aa7e852bbcf9e34e695297
  Toast-Swift: dd369e68ee529f542c65bd0d0c6c50cd743cf6f2
  TPNAdmobSDKAdapter: 39ae33483134e2869a575e09c463400291042103
  TPNApplovinSDKAdapter: f49d08340dba12dc2b493ae8950d46d47ffd08e3
  TPNFacebookSDKAdapter: 5dd94720edf03316e6f6505e970b62e07a80bfe0
  TPNiOS: 1b19f54b097912acf89dd41821605cc366432756
  TPNPangleSDKAdapter: d96656537c9718581142e2885c2c8108a3698a51
  TPNUnityAdsSDKAdapter: 655206a266b48be72e24db08fc08d83d88459ccb
  TZImagePickerController: e9909edbadf7381140efc5b5c9f5bdbfd630f7d4
  UICountingLabel: faf890b505d96312e324a86718f031fafffb0ccb
  UnityAds: eabbbb3014326e45dc221a467fba16960e7b73e3
  Upsurge: 5866beadc3da27f91c5df4ac795deb3f3238d678
  URLNavigator: 2593c0e2d293732ec5c44503cf54dceba1a68085
  YLCore: 30f659a89df7bb1d4ff7f20f31f1bc81f1f4b539
  YLPaymentKit: 17f081b7ee7f82606aa12401a6e976dcad825e98
  YLWebView: dc8c0058b350668fc6f58df0de9e91c00a328f10
  ZLPhotoBrowser: 193f779936f49a3f485100009efb8e0b8b3fb2fe

PODFILE CHECKSUM: bb1897242896b431e7c27f6f1a76ac6a4e34cd43

COCOAPODS: 1.16.2
